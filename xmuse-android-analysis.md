# Xmuse-Android 项目分析

## 项目概述

Xmuse-Android 是一个与脑电设备(Muse)相关的健康应用，采用现代化的模块化架构设计，遵循 Clean Architecture + MVVM 模式，并使用 Hilt 进行依赖注入。

## 项目结构

### 核心模块 (Core Modules)

- **`core-network`** - 网络请求层，负责与服务器通信
- **`core-data`** - 数据仓库层，协调网络和本地数据
- **`core-database`** - 数据库层，处理本地数据存储
- **`core-model`** - 数据模型层，定义应用中使用的数据结构
- **`core-common`** - 通用工具层，提供各种工具类
- **`core-ui`** - UI通用组件层，包含可复用的UI组件

### 功能模块 (Feature Modules)

- **`feature:login`** - 登录功能
- **`feature:user`** - 用户管理
- **`feature:device`** - 设备管理
- **`feature:setting`** - 设置功能
- **`feature:ncalendar`** - 日历功能

### 主应用模块

- **`brainprint`** - 主应用模块，集成所有功能模块

## HTTP请求封装分析

HTTP请求的封装主要位于 **`core-network`** 模块中，采用了业界标准的技术栈和最佳实践。

### 技术栈

- **Retrofit2** - REST API客户端
- **OkHttp3** - HTTP客户端和拦截器
- **Gson** - JSON序列化/反序列化
- **RxJava3** - 响应式编程支持
- **Hilt** - 依赖注入

### 核心特性

#### 1. 多环境配置

项目支持多环境配置，包括本地测试、测试环境和生产环境：

```kotlin
private const val HOST_LOCAL_TEST = "http://***********:9051"
private const val HOST_TEST = "https://test-cloud.xmuse.cn/cloud/"
private const val HOST_PUBLISH = "https://api.xmuse.cn/"

private val HOST: String =
    if (IS_LOCAL_TEST) HOST_LOCAL_TEST else if (IS_TEST) HOST_TEST else HOST_PUBLISH
```

#### 2. 统一请求头处理

项目实现了自动添加请求头的拦截器，包括：

- **签名验证** (`s` header)
- **版本信息** (`v` header) 
- **用户Token** (`t` header)
- **应用标识** (`f` header)

```kotlin
private fun provideUserTokenHeader(chain: Interceptor.Chain): Response {
    val original = chain.request()
    val path = original.url.encodedPath
    val key = "0104"
    val version = "1"
    val sign = generateRequestSign(key, version, path)
    var requestBuilder = original.newBuilder()
    requestBuilder = requestBuilder.addHeader("s", sign)
    requestBuilder = requestBuilder.addHeader("f", key)
    requestBuilder = requestBuilder.addHeader("v", version)

    val token = SPUtils.getInstance().getString("userToken", "") //账号token
    if (!token.isNullOrEmpty()) {
        requestBuilder = requestBuilder.addHeader("t", token)
    }
    val request = requestBuilder.build()
    return chain.proceed(request)
}
```

#### 3. 请求签名机制

实现了复杂的请求签名机制，确保API调用的安全性：

```kotlin
private fun generateRequestSign(key: String, version: String, url: String): String {
    val f = "2024v${version}@muse@${key}"
    val timeMillis = System.currentTimeMillis()
    val randomString = generateRandomString(4)
    val md5String = EncryptUtils.encryptMD5ToString("${randomString}${timeMillis}${url}${f}")
        .lowercase(Locale.ROOT)
    val sign = "${randomString}&${timeMillis}&${md5String}"
    return sign.encodeToBase64String()
}
```

#### 4. 统一响应格式

定义了统一的API响应格式，便于统一处理：

```kotlin
data class ApiRequestResult<T>(val code: Int, val data: T?, val msg: String)

data class ApiRequestPageResult<T>(
    val total: Int,
    val list: List<T>
)
```

#### 5. 服务接口分类

项目按业务模块划分了不同的Service接口：

- **`UserService`** - 用户相关API
- **`CourseService`** - 课程相关API  
- **`SleepService`** - 睡眠数据API
- **`FileService`** - 文件上传API
- **`OtherService`** - 其他通用API
- **`WxService`** - 微信相关API

示例接口定义：

```kotlin
interface UserService {
    @Headers("Content-Type: application/json")
    @POST("us/api/user/send-sms")
    suspend fun sendSMS(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/login")
    suspend fun phoneLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>
    
    // 更多API...
}
```

#### 6. 错误处理机制

实现了账户过期自动处理的拦截器：

```kotlin
class AccountExpiredInterceptor(val userDao: UserDao) : RequestResultInterceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        val result = response.body?.source()?.buffer?.toString()
        if (result?.contains("401") == true) {
            userDao.deleteCurrentUserSync()
            ThreadUtils.runOnUiThread {
                // 自动跳转到登录页面
                val mainActivity = ActivityUtils.getMainActivities()[0]
                val topActivity = ActivityUtils.getTopActivity()
                val packageName = AppUtils.getAppPackageName()
                val componentName = ComponentName(packageName, mainActivity)
                val intent = Intent()
                intent.putExtra("isUserExpired", true)
                intent.setComponent(componentName)
                if (intent.resolveActivity(topActivity.packageManager) != null) {
                    topActivity.startActivity(intent)
                }
            }
        }
        return response
    }
}
```

#### 7. Retrofit客户端配置

提供了多种Retrofit客户端配置，适应不同的需求：

```kotlin
private fun provideRetrofitClient(resultInterceptor: RequestResultInterceptor? = null): Retrofit {
    val logging = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    val okHttpClient = OkHttpClient.Builder().apply {
        if (resultInterceptor != null) {
            addInterceptor(resultInterceptor)
        }
        addInterceptor { provideUserTokenHeader(it) }
        addInterceptor(logging)
    }.build()

    return Retrofit.Builder()
        .baseUrl(HOST)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
}
```

## 项目特点

1. **脑电设备应用** - 从包名和功能模块可以看出这是一个与脑电设备(Muse)相关的健康应用
2. **模块化设计** - 清晰的模块分离，便于维护和扩展
3. **现代化架构** - 使用了Android最新的架构组件和最佳实践
4. **完善的网络层** - 包含签名、加密、错误处理等完整的网络请求解决方案

## 总结

Xmuse-Android项目采用了企业级的架构设计和最佳实践，特别是在HTTP请求封装方面非常完善，具有良好的可维护性和扩展性。项目的模块化结构使得团队可以并行开发不同功能，同时保持代码的清晰和可维护性。
