/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven(url = "https://jitpack.io")
        maven(url = "https://developer.huawei.com/repo/")

    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven(url = "https://jitpack.io")
        maven(url = "https://developer.huawei.com/repo/")
        maven(url = "https://maven.aliyun.com/nexus/content/repositories/releases/")
        repositories {
            flatDir {
                dirs(File(rootProject.projectDir, "libs"))
            }
        }
    }
}



rootProject.name = "BrainPrint"

//include(":app")
include(":brainprint")

include(":core-data")
//include(":core-tensorflow")
include(":core-common")
include(":core-network")
include(":core-database")
include(":core-ui")
include(":core-model")
include(":feature:login")
//include(":feature:brainprint")

include(":feature:user")
include(":feature:device")
include(":feature:setting")
include(":feature:ncalendar")

include(":local-lib:logger-release")
include(":local-lib:main-release")
include(":local-lib:login-auth")

