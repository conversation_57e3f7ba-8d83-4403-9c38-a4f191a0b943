package com.ruiheng.xmuse.feature.ncalendar.calendarx.internal.monthcalendar

import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.CalendarDay
import com.ruiheng.xmuse.feature.ncalendar.calendarx.CalendarView
import com.ruiheng.xmuse.feature.ncalendar.calendarx.LayoutHelper
import com.ruiheng.xmuse.feature.ncalendar.calendarx.MarginValues
import com.ruiheng.xmuse.feature.ncalendar.calendarx.internal.CalendarLayoutManager
import com.ruiheng.xmuse.feature.ncalendar.calendarx.internal.dayTag
import com.ruiheng.xmuse.feature.ncalendar.calendarx.internal.monthcalendar.MonthCalendarAdapter
import java.time.YearMonth

internal class MonthCalendarLayoutManager(private val calView: CalendarView) :
    CalendarLayoutManager<YearMonth, CalendarDay>(calView, calView.orientation) {
    private val adapter: MonthCalendarAdapter
        get() = calView.adapter as MonthCalendarAdapter

    override fun getaItemAdapterPosition(data: YearMonth): Int = adapter.getAdapterPosition(data)
    override fun getaDayAdapterPosition(data: CalendarDay): Int = adapter.getAdapterPosition(data)
    override fun getDayTag(data: CalendarDay): Int = dayTag(data.date)
    override fun getItemMargins(): MarginValues = calView.monthMargins
    override fun scrollPaged(): Boolean = calView.scrollPaged
    override fun notifyScrollListenerIfNeeded() = adapter.notifyMonthScrollListenerIfNeeded()
    override fun getLayoutHelper(): LayoutHelper? = calView.layoutHelper
}
