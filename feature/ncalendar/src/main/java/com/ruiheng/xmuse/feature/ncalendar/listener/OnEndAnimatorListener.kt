package com.ruiheng.xmuse.feature.ncalendar.listener

import android.animation.Animator

/**
 * <AUTHOR>
 */
open class OnEndAnimatorListener : Animator.AnimatorListener {
    override fun onAnimationStart(animation: Animator) {}
    override fun onAnimationEnd(animation: Animator) {}
    override fun onAnimationCancel(animation: Animator) {}
    override fun onAnimationRepeat(animation: Animator) {}
}