package com.ruiheng.xmuse.feature.ncalendar.utils

import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.ruiheng.xmuse.feature.ncalendar.R
import com.ruiheng.xmuse.feature.ncalendar.enumeration.CalendarState
import com.ruiheng.xmuse.feature.ncalendar.utils.NAttrs

/**
 * <AUTHOR>
 * @date 2018/11/28
 */
object NAttrsUtil {
    fun setAttrs(context: Context, attributeSet: AttributeSet?) {
        val ta = context.obtainStyledAttributes(attributeSet, R.styleable.NCalendar)
        NAttrs.todayCheckedBackground = ta.getResourceId(R.styleable.NCalendar_todayCheckedBackground, R.drawable.n_bg_checked_today)
        NAttrs.defaultCheckedBackground = ta.getResourceId(R.styleable.NCalendar_defaultCheckedBackground, R.drawable.n_bg_checked_default)
        NAttrs.todayCheckedSolarTextColor = ta.getColor(R.styleable.NCalendar_todayCheckedSolarTextColor, ContextCompat.getColor(context, R.color.N_white))
        NAttrs.todayUnCheckedSolarTextColor = ta.getColor(R.styleable.NCalendar_todayUnCheckedSolarTextColor, ContextCompat.getColor(context, R.color.N_todaySolarUnCheckedTextColor))
        NAttrs.defaultCheckedSolarTextColor = ta.getColor(R.styleable.NCalendar_defaultCheckedSolarTextColor, ContextCompat.getColor(context, R.color.N_defaultSolarTextColor))
        NAttrs.defaultUnCheckedSolarTextColor = ta.getColor(R.styleable.NCalendar_defaultUnCheckedSolarTextColor, ContextCompat.getColor(context, R.color.N_defaultSolarTextColor))
        NAttrs.solarTextSize = ta.getDimension(R.styleable.NCalendar_solarTextSize, context.resources.getDimension(R.dimen.N_solarTextSize))
        NAttrs.weekBarTextSize = ta.getDimension(R.styleable.NCalendar_weekBarTextSize, context.resources.getDimension(R.dimen.N_weekBarTextSize))
        NAttrs.solarTextBold = ta.getBoolean(R.styleable.NCalendar_solarTextBold, context.resources.getBoolean(R.bool.N_textBold))
        NAttrs.showLunar = ta.getBoolean(R.styleable.NCalendar_showLunar, context.resources.getBoolean(R.bool.N_showLunar))
        NAttrs.todayCheckedLunarTextColor = ta.getColor(R.styleable.NCalendar_todayCheckedLunarTextColor, ContextCompat.getColor(context, R.color.N_white))
        NAttrs.todayUnCheckedLunarTextColor = ta.getColor(R.styleable.NCalendar_todayUnCheckedLunarTextColor, ContextCompat.getColor(context, R.color.N_todayCheckedColor))
        NAttrs.defaultCheckedLunarTextColor = ta.getColor(R.styleable.NCalendar_defaultCheckedLunarTextColor, ContextCompat.getColor(context, R.color.N_defaultLunarTextColor))
        NAttrs.defaultUnCheckedLunarTextColor = ta.getColor(R.styleable.NCalendar_defaultUnCheckedLunarTextColor, ContextCompat.getColor(context, R.color.N_defaultLunarTextColor))
        NAttrs.lunarTextSize = ta.getDimension(R.styleable.NCalendar_lunarTextSize, context.resources.getDimension(R.dimen.N_lunarTextSize))
        NAttrs.lunarTextBold = ta.getBoolean(R.styleable.NCalendar_lunarTextBold, context.resources.getBoolean(R.bool.N_textBold))
        NAttrs.lunarDistance = ta.getDimension(R.styleable.NCalendar_lunarDistance, context.resources.getDimension(R.dimen.N_lunarDistance))
        NAttrs.pointLocation = ta.getInt(R.styleable.NCalendar_pointLocation, NAttrs.UP)
        NAttrs.pointDistance = ta.getDimension(R.styleable.NCalendar_pointDistance, context.resources.getDimension(R.dimen.N_pointDistance))
        NAttrs.todayCheckedPoint = ta.getResourceId(R.styleable.NCalendar_todayCheckedPoint, R.drawable.n_point_checked_today)
        NAttrs.todayUnCheckedPoint = ta.getResourceId(R.styleable.NCalendar_todayUnCheckedPoint, R.drawable.n_point_unchecked_today)
        NAttrs.defaultCheckedPoint = ta.getResourceId(R.styleable.NCalendar_defaultCheckedPoint, R.drawable.n_point_checked_default)
        NAttrs.defaultUnCheckedPoint = ta.getResourceId(R.styleable.NCalendar_defaultUnCheckedPoint, R.drawable.n_point_unchecked_default)
        NAttrs.showHolidayWorkday = ta.getBoolean(R.styleable.NCalendar_showHolidayWorkday, context.resources.getBoolean(R.bool.N_showHolidayWorkday))
        NAttrs.defaultCheckedFirstDate = ta.getBoolean(R.styleable.NCalendar_defaultCheckedFirstDate, context.resources.getBoolean(R.bool.N_defaultCheckedFirstDate))
        NAttrs.todayCheckedHoliday = ta.getDrawable(R.styleable.NCalendar_todayCheckedHoliday)
        NAttrs.todayUnCheckedHoliday = ta.getDrawable(R.styleable.NCalendar_todayUnCheckedHoliday)
        NAttrs.defaultCheckedHoliday = ta.getDrawable(R.styleable.NCalendar_defaultCheckedHoliday)
        NAttrs.defaultUnCheckedHoliday = ta.getDrawable(R.styleable.NCalendar_defaultUnCheckedHoliday)
        NAttrs.todayCheckedWorkday = ta.getDrawable(R.styleable.NCalendar_todayCheckedWorkday)
        NAttrs.todayUnCheckedWorkday = ta.getDrawable(R.styleable.NCalendar_todayUnCheckedWorkday)
        NAttrs.defaultCheckedWorkday = ta.getDrawable(R.styleable.NCalendar_defaultCheckedWorkday)
        NAttrs.defaultUnCheckedWorkday = ta.getDrawable(R.styleable.NCalendar_defaultUnCheckedWorkday)
        NAttrs.holidayWorkdayTextSize = ta.getDimension(R.styleable.NCalendar_holidayWorkdayTextSize, context.resources.getDimension(R.dimen.N_holidayWorkdayTextSize))
        NAttrs.holidayWorkdayTextBold = ta.getBoolean(R.styleable.NCalendar_holidayWorkdayTextBold, context.resources.getBoolean(R.bool.N_textBold))
        NAttrs.holidayWorkdayDistance = ta.getDimension(R.styleable.NCalendar_holidayWorkdayDistance, context.resources.getDimension(R.dimen.N_holidayWorkdayDistance))
        NAttrs.holidayWorkdayLocation = ta.getInt(R.styleable.NCalendar_holidayWorkdayLocation, NAttrs.TOP_RIGHT)
        NAttrs.holidayText = ta.getString(R.styleable.NCalendar_holidayText)
        NAttrs.workdayText = ta.getString(R.styleable.NCalendar_workdayText)
        NAttrs.todayCheckedHolidayTextColor = ta.getColor(R.styleable.NCalendar_todayCheckedHolidayTextColor, ContextCompat.getColor(context, R.color.N_white))
        NAttrs.todayUnCheckedHolidayTextColor = ta.getColor(R.styleable.NCalendar_todayUnCheckedHolidayTextColor, ContextCompat.getColor(context, R.color.N_holidayTextColor))
        NAttrs.defaultCheckedHolidayTextColor = ta.getColor(R.styleable.NCalendar_defaultCheckedHolidayTextColor, ContextCompat.getColor(context, R.color.N_holidayTextColor))
        NAttrs.defaultUnCheckedHolidayTextColor = ta.getColor(R.styleable.NCalendar_defaultUnCheckedHolidayTextColor, ContextCompat.getColor(context, R.color.N_holidayTextColor))
        NAttrs.todayCheckedWorkdayTextColor = ta.getColor(R.styleable.NCalendar_todayCheckedWorkdayTextColor, ContextCompat.getColor(context, R.color.N_white))
        NAttrs.todayUnCheckedWorkdayTextColor = ta.getColor(R.styleable.NCalendar_todayUnCheckedWorkdayTextColor, ContextCompat.getColor(context, R.color.N_workdayTextColor))
        NAttrs.defaultCheckedWorkdayTextColor = ta.getColor(R.styleable.NCalendar_defaultCheckedWorkdayTextColor, ContextCompat.getColor(context, R.color.N_workdayTextColor))
        NAttrs.defaultUnCheckedWorkdayTextColor = ta.getColor(R.styleable.NCalendar_defaultUnCheckedWorkdayTextColor, ContextCompat.getColor(context, R.color.N_workdayTextColor))
        NAttrs.showNumberBackground = ta.getBoolean(R.styleable.NCalendar_showNumberBackground, context.resources.getBoolean(R.bool.N_showNumberBackground))
        NAttrs.numberBackgroundTextSize = ta.getDimension(R.styleable.NCalendar_numberBackgroundTextSize, context.resources.getDimension(R.dimen.N_numberBackgroundTextSize))
        NAttrs.numberBackgroundTextColor = ta.getColor(R.styleable.NCalendar_numberBackgroundTextColor, ContextCompat.getColor(context, R.color.N_todaySolarUnCheckedTextColor))
        NAttrs.weekBarTextColor = ta.getColor(R.styleable.NCalendar_weekBarTextColor, ContextCompat.getColor(context, R.color.N_weekBarTextColor))
        NAttrs.weekBarBackgroundColor = ta.getColor(R.styleable.NCalendar_weekBarBackgroundColor, ContextCompat.getColor(context, R.color.N_weekBarBackgroundColor))
        NAttrs.backgroundAlphaColor = ta.getInt(R.styleable.NCalendar_backgroundAlphaColor, context.resources.getInteger(R.integer.N_backgroundAlphaColor))
        NAttrs.firstDayOfWeek = ta.getInt(R.styleable.NCalendar_firstDayOfWeek, NAttrs.SUNDAY)
        NAttrs.allMonthSixLine = ta.getBoolean(R.styleable.NCalendar_allMonthSixLine, context.resources.getBoolean(R.bool.N_allMonthSixLine))
        NAttrs.lastNextMonthClickEnable = ta.getBoolean(R.styleable.NCalendar_lastNextMonthClickEnable, context.resources.getBoolean(R.bool.N_lastNextMonthClickEnable))
        NAttrs.horizontalScrollEnable = ta.getBoolean(R.styleable.NCalendar_horizontalScrollEnable, context.resources.getBoolean(R.bool.N_horizontalScrollEnable))
        NAttrs.calendarBackground = ta.getDrawable(R.styleable.NCalendar_calendarBackground)
        NAttrs.lastNextMothAlphaColor = ta.getInt(R.styleable.NCalendar_lastNextMothAlphaColor, context.resources.getInteger(R.integer.N_lastNextMothAlphaColor))
        NAttrs.disabledAlphaColor = ta.getInt(R.styleable.NCalendar_disabledAlphaColor, context.resources.getInteger(R.integer.N_disabledAlphaColor))
        NAttrs.disabledString = ta.getString(R.styleable.NCalendar_disabledString)
        NAttrs.defaultCalendar = ta.getInt(R.styleable.NCalendar_defaultCalendar, CalendarState.MONTH.getValue())
        NAttrs.calendarHeight = ta.getDimension(R.styleable.NCalendar_calendarHeight, context.resources.getDimension(R.dimen.N_calendarHeight)).toInt()
        NAttrs.weekBarHeight = ta.getDimension(R.styleable.NCalendar_weekBarHeight, context.resources.getDimension(R.dimen.N_weekBarHeight)).toInt()
        NAttrs.animationDuration = ta.getInt(R.styleable.NCalendar_animationDuration, context.resources.getInteger(R.integer.N_animationDuration))
        NAttrs.stretchCalendarEnable = ta.getBoolean(R.styleable.NCalendar_stretchCalendarEnable, context.resources.getBoolean(R.bool.N_stretchCalendarEnable))
        NAttrs.stretchCalendarHeight = ta.getDimension(R.styleable.NCalendar_stretchCalendarHeight, context.resources.getDimension(R.dimen.N_stretchCalendarHeight)).toInt()
        NAttrs.stretchTextSize = ta.getDimension(R.styleable.NCalendar_stretchTextSize, context.resources.getDimension(R.dimen.N_stretchTextSize))
        NAttrs.stretchTextBold = ta.getBoolean(R.styleable.NCalendar_stretchTextBold, context.resources.getBoolean(R.bool.N_textBold))
        NAttrs.stretchTextColor = ta.getColor(R.styleable.NCalendar_stretchTextColor, ContextCompat.getColor(context, R.color.N_stretchTextColor))
        NAttrs.stretchTextDistance = ta.getDimension(R.styleable.NCalendar_stretchTextDistance, context.resources.getDimension(R.dimen.N_stretchTextDistance))
        ta.recycle()
    }
}