package com.ruiheng.xmuse.feature.ncalendar.calendarx.data

import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.CalendarDay
import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.DayPosition
import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.nextMonth
import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.previousMonth
import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.yearMonth
import java.time.DayOfWeek
import java.time.YearMonth

// E.g DayOfWeek.SATURDAY.daysUntil(DayOfWeek.TUESDAY) = 3
public fun DayOfWeek.daysUntil(other: DayOfWeek): Int = (7 + (other.ordinal - ordinal)) % 7

// Find the actual month on the calendar where this date is shown.
public val CalendarDay.positionYearMonth: YearMonth
    get() = when (position) {
        DayPosition.InDate -> date.yearMonth.nextMonth
        DayPosition.MonthDate -> date.yearMonth
        DayPosition.OutDate -> date.yearMonth.previousMonth
    }

public inline fun <T> Iterable<T>.indexOfFirstOrNull(predicate: (T) -> Boolean): Int? {
    val result = indexOfFirst(predicate)
    return if (result == -1) null else result
}
