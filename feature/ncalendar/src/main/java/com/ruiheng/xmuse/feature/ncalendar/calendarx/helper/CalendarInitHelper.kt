package com.ruiheng.xmuse.feature.ncalendar.calendarx.helper

import android.widget.TextView
import androidx.core.view.children
import com.ruiheng.xmuse.feature.ncalendar.calendarx.core.daysOfWeek
import com.ruiheng.xmuse.feature.ncalendar.databinding.CalendarDayLegendContainerBinding
import java.time.DayOfWeek

fun CalendarDayLegendContainerBinding.initWeekView(daysOfWeek:List<DayOfWeek>){
    root.children.map {
        it as TextView
    }.forEachIndexed { index, textView ->
        textView.text = daysOfWeek[index].displayText()
    }
}