<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- The xml resource that is inflated and used as the day cell view, must be provided. -->
    <attr name="cv_dayViewResource" format="reference" />

    <!-- The xml resource that is inflated and used as a header for every month. -->
    <attr name="cv_monthHeaderResource" format="reference" />

    <!-- The xml resource that is inflated and used as a footer for every month. -->
    <attr name="cv_monthFooterResource" format="reference" />

    <!-- Determines how outDates are generated for each month on the calendar. -->
    <attr name="cv_outDateStyle" format="enum">
        <!-- The calendar will generate outDates until it reaches the end
        of the month row. This means that if a month has 5 rows, it will
        display 5 rows and if a month has 6 rows, it will display 6 rows. -->
        <enum name="endOfRow" value="0" />
        <!-- The calendar will generate outDates until it reaches the end
        of a 6 x 7 grid on each month. This means that all months will have 6 rows. -->
        <enum name="endOfGrid" value="1" />
    </attr>

    <!-- A ViewGroup which is instantiated and used as the container for each month.
     This class must have a constructor which takes only a Context. You should
     exclude the name and constructor of this class from code obfuscation if enabled. -->
    <attr name="cv_monthViewClass" format="string" />

    <!--This determines the scroll direction of the the calendar. -->
    <attr name="cv_orientation" format="enum">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>

    <!-- The scrolling behavior of the calendar. If `true`, the calendar will
     snap to the nearest month or week (in the WeekCalendarView) after a scroll
     or swipe action. If `false`, the calendar scrolls normally. -->
    <attr name="cv_scrollPaged" format="boolean" />

    <!-- Determines how the size of each day on the calendar is calculated. -->
    <attr name="cv_daySize" format="enum">
        <!-- Each day will have both width and height matching
         the width of the calendar divided by 7. -->
        <enum name="square" value="0" />
        <!-- Each day will have its width matching the width of the calendar
         divided by 7, and its height matching the height of the calendar divided
         by the number of weeks in the index - could be 4, 5 or 6 for the month
         calendar and 1 for the week calendar. Use this if you want each month or
         week to fill the parent's width and height. -->
        <enum name="rectangle" value="1" />
        <!-- Each day will have its width matching the width of the calendar
         divided by 7. The day is allowed to determine its height by
         setting a specific value or using [ViewGroup.LayoutParams.WRAP_CONTENT]. -->
        <enum name="seventhWidth" value="2" />
        <!-- This day is allowed to determine its width and height by
         setting specific values or using [ViewGroup.LayoutParams.WRAP_CONTENT]. -->
        <enum name="freeForm" value="3" />
    </attr>

    <declare-styleable name="CalendarView">
        <attr name="cv_dayViewResource" />
        <attr name="cv_monthHeaderResource" />
        <attr name="cv_monthFooterResource" />
        <attr name="cv_outDateStyle" />
        <attr name="cv_monthViewClass" />
        <attr name="cv_orientation" />
        <attr name="cv_scrollPaged" />
        <attr name="cv_daySize" />
    </declare-styleable>

    <declare-styleable name="WeekCalendarView">
        <attr name="cv_dayViewResource" />

        <!-- The xml resource that is inflated and used as a header for every week. -->
        <attr name="cv_weekHeaderResource" format="reference" />

        <!-- The xml resource that is inflated and used as a footer for every week. -->
        <attr name="cv_weekFooterResource" format="reference" />

        <!-- A ViewGroup which is instantiated and used as the container for each week.
         This class must have a constructor which takes only a Context. You should
         exclude the name and constructor of this class from code obfuscation if enabled. -->
        <attr name="cv_weekViewClass" format="string" />

        <attr name="cv_scrollPaged" />
        <attr name="cv_daySize" />
    </declare-styleable>

    <declare-styleable name="YearCalendarView">
        <attr name="cv_dayViewResource" />
        <attr name="cv_monthHeaderResource" />
        <attr name="cv_monthFooterResource" />

        <!-- The xml resource that is inflated and used as a header for every year. -->
        <attr name="cv_yearHeaderResource" format="reference" />

        <!-- The xml resource that is inflated and used as a footer for every year. -->
        <attr name="cv_yearFooterResource" format="reference" />

        <attr name="cv_outDateStyle" />
        <!-- A ViewGroup which is instantiated and used as the container for each month.
         This class must have a constructor which takes only a Context. You should
         exclude the name and constructor of this class from code obfuscation if enabled. -->
        <attr name="cv_yearViewClass" format="string" />

        <attr name="cv_monthViewClass" />

        <!-- The number of month columns in each year. Must be from 1 to 12. -->
        <attr name="cv_monthColumns" format="integer" />

        <!-- The horizontal spacing between month columns in each year. -->
        <attr name="cv_monthHorizontalSpacing" format="dimension" />

        <!-- The vertical spacing between month rows in each year. -->
        <attr name="cv_monthVerticalSpacing" format="dimension" />

        <!-- Determines how the height of each month row on the year-based calendar is calculated. -->
        <attr name="cv_monthHeight" format="enum">
            <!-- Each month's row height is determined by the [DaySize] value set on the calendar.
            Effectively, this is `wrap-content` if the value is [DaySize.Square],
            [DaySize.SeventhWidth], or [DaySize.FreeForm], and will be equal to the calendar height
            divided by the number of rows if the value is [DaySize.Rectangle].

            When used together with [DaySize.Rectangle], the calendar months and days will
            uniformly stretch to fill the parent's height. -->
            <enum name="followDaySize" value="0" />

            <!-- Each month's row height will be the calendar height divided by the number
            of rows on the calendar. This means that the calendar months will be distributed
            uniformly to fill the parent's height. However, the day content height will
            independently determine its height.

            This allows you to spread the calendar months evenly across the screen while
            using a `daySize` value of [DaySize.Square] if you want square day content
            or [DaySize.SeventhWidth] if you want to set a specific height value for the
            day content. -->
            <enum name="fill" value="1" />
        </attr>

        <attr name="cv_orientation" />
        <attr name="cv_scrollPaged" />
        <attr name="cv_daySize" />
    </declare-styleable>

    <declare-styleable name="NCalendar">


        <attr name="solarTextSize" format="dimension" />
        <attr name="solarTextBold" format="boolean" />
        <attr name="todayCheckedSolarTextColor" format="color" />
        <attr name="todayUnCheckedSolarTextColor" format="color" />
        <attr name="defaultCheckedSolarTextColor" format="color" />
        <attr name="defaultUnCheckedSolarTextColor" format="color" />

        <attr name="showLunar" format="boolean" />
        <attr name="todayCheckedLunarTextColor" format="color" />
        <attr name="todayUnCheckedLunarTextColor" format="color" />
        <attr name="defaultCheckedLunarTextColor" format="color" />
        <attr name="defaultUnCheckedLunarTextColor" format="color" />
        <attr name="lunarTextSize" format="dimension" />
        <attr name="lunarTextBold" format="boolean" />
        <attr name="lunarDistance" format="dimension" />


        <attr name="pointSize" format="dimension" />
        <attr name="pointDistance" format="dimension" />
        <attr name="todayCheckedPoint" format="reference" />
        <attr name="todayUnCheckedPoint" format="reference" />
        <attr name="defaultCheckedPoint" format="reference" />
        <attr name="defaultUnCheckedPoint" format="reference" />

        <attr name="todayCheckedHoliday" format="reference" />
        <attr name="todayUnCheckedHoliday" format="reference" />
        <attr name="defaultCheckedHoliday" format="reference" />
        <attr name="defaultUnCheckedHoliday" format="reference" />

        <attr name="todayCheckedWorkday" format="reference" />
        <attr name="todayUnCheckedWorkday" format="reference" />
        <attr name="defaultCheckedWorkday" format="reference" />
        <attr name="defaultUnCheckedWorkday" format="reference" />

        <attr name="calendarBackground" format="reference" />


        <attr name="holidayText" format="string" />
        <attr name="workdayText" format="string" />
        <attr name="holidayWorkdayTextBold" format="boolean" />
        <attr name="holidayWorkdayTextSize" format="dimension" />
        <attr name="holidayWorkdayDistance" format="dimension" />
        <attr name="todayCheckedHolidayTextColor" format="color" />
        <attr name="todayUnCheckedHolidayTextColor" format="color" />
        <attr name="defaultCheckedHolidayTextColor" format="color" />
        <attr name="defaultUnCheckedHolidayTextColor" format="color" />
        <attr name="todayCheckedWorkdayTextColor" format="color" />
        <attr name="todayUnCheckedWorkdayTextColor" format="color" />
        <attr name="defaultCheckedWorkdayTextColor" format="color" />
        <attr name="defaultUnCheckedWorkdayTextColor" format="color" />


        <attr name="disabledString" format="string" />

        <attr name="calendarHeight" format="dimension" />
        <attr name="weekBarHeight" format="dimension" />
        <attr name="stretchCalendarHeight" format="dimension" />
        <attr name="stretchCalendarEnable" format="boolean" />
        <attr name="animationDuration" format="integer" />

        <attr name="lastNextMothAlphaColor" format="integer" />
        <attr name="disabledAlphaColor" format="integer" />


        <attr name="showHolidayWorkday" format="boolean" />
        <attr name="defaultCheckedFirstDate" format="boolean" />
        <attr name="allMonthSixLine" format="boolean" />
        <attr name="lastNextMonthClickEnable" format="boolean" />
        <attr name="horizontalScrollEnable" format="boolean" />
        <attr name="disabledColor" format="color" />

        <attr name="defaultCalendar" format="enum">
            <enum name="week" value="100" />
            <enum name="month" value="101" />
        </attr>
        <attr name="singleCalendar" format="boolean" />

        <attr name="pointLocation" format="enum">
            <enum name="up" value="200" />
            <enum name="down" value="201" />
        </attr>

        <attr name="firstDayOfWeek" format="enum">
            <enum name="sunday" value="300" />
            <enum name="monday" value="301" />
        </attr>

        <attr name="holidayWorkdayLocation" format="enum">
            <enum name="top_right" value="400" />
            <enum name="top_left" value="401" />
            <enum name="bottom_right" value="402" />
            <enum name="bottom_left" value="403" />
        </attr>

        <attr name="stretchTextSize" format="dimension" />
        <attr name="stretchTextBold" format="boolean" />
        <attr name="stretchTextDistance" format="dimension" />
        <attr name="stretchTextColor" format="color" />


        <attr name="showNumberBackground" format="boolean" />
        <attr name="numberBackgroundTextSize" format="dimension" />
        <attr name="numberBackgroundTextColor" format="color" />
        <attr name="backgroundAlphaColor" format="integer" />

        <attr name="weekBarTextColor" format="color" />
        <attr name="weekBarBackgroundColor" format="color" />
        <attr name="weekBarTextSize" format="dimension" />

        <attr name="defaultCheckedBackground" format="reference" />
        <attr name="todayCheckedBackground" format="reference" />

    </declare-styleable>


</resources>