<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="@dimen/space36"
        android:paddingTop="@dimen/space36"
        android:paddingEnd="@dimen/space36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/reset_password"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/tv_login_title">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_account"
                android:inputType="text"
                android:textColor="?colorOnSurface"
                android:textColorHint="?colorOnSurfaceVariant"
                tools:text="***********" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_verify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/input_layout_account">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_verify"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_account"
                android:inputType="number"
                android:maxLength="6"
                android:textColor="?colorOnSurface"
                android:textColorHint="?colorOnSurfaceVariant"
                tools:text="***********" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.ruiheng.xmuse.core.ui.widget.verify.CountDownTextView
            android:id="@+id/tv_count_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_horizontal_margin"
            android:text="@string/get_verification_code"
            android:textColor="?colorPrimary"
            android:textSize="@dimen/font_normal"
            app:layout_constraintBottom_toBottomOf="@id/input_layout_verify"
            app:layout_constraintEnd_toEndOf="@id/input_layout_verify"
            app:layout_constraintTop_toTopOf="@id/input_layout_verify" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/input_layout_verify">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_password"
                android:inputType="textPassword"
                android:textColor="?colorOnSurface"
                android:textColorHint="?colorOnSurfaceVariant" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_password_again"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/input_layout_password">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_password_again"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enter_password_again"
                android:inputType="textPassword"
                android:textColor="?colorOnSurface"
                android:textColorHint="?colorOnSurfaceVariant" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="@dimen/space24"
            android:text="@string/reset_password"
            app:layout_constraintTop_toBottomOf="@id/input_layout_password_again" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>