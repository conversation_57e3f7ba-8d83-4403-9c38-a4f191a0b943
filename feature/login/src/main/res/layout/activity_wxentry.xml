<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black64">

        <ProgressBar
            android:id="@+id/loading_register"
            android:layout_width="64dp"
            android:layout_height="64dp"
            app:indicatorColor="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <LinearLayout
            android:id="@+id/layout_wx_code_login"
            android:layout_width="270dp"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_corner_surface_bg"
            android:backgroundTint="?colorSurfaceContainerLow"
            android:orientation="vertical"
            android:paddingTop="@dimen/activity_vertical_margin"
            android:paddingBottom="@dimen/activity_vertical_margin"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.45">


            <TextView
                android:id="@+id/tv_wx_scan_code"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingBottom="@dimen/activity_vertical_margin"
                android:text="@string/open_wechat_and_scan"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_normal" />

            <FrameLayout
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/activity_vertical_margin">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_wx_code_login"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/ico_avatar_default" />

                <!--                <androidx.appcompat.widget.AppCompatImageView-->
                <!--                    android:id="@+id/img_wx_code_login_logo"-->
                <!--                    android:layout_width="48dp"-->
                <!--                    android:layout_height="48dp"-->
                <!--                    android:layout_gravity="center"-->
                <!--                    android:scaleType="centerCrop"-->
                <!--                    tools:src="@drawable/icon_border02" />-->
            </FrameLayout>

            <!--            <View-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="1px"-->
            <!--                android:background="@drawable/shape_linear_divider" />-->

            <!--            <TextView-->
            <!--                android:id="@+id/tv_wx_login_cancel"-->
            <!--                style="@style/TextNormalStyle"-->
            <!--                android:layout_width="match_parent"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:padding="@dimen/spacing_12"-->
            <!--                android:text="@string/already_know"-->
            <!--                android:textColor="@color/colorPrimary" />-->
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>