<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="@dimen/space36"
        android:paddingTop="@dimen/space36"
        android:paddingEnd="@dimen/space36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/configure_password"
            android:textColor="?colorOnSurface"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_small"
            app:layout_constraintStart_toStartOf="@id/tv_login_title"
            app:layout_constraintTop_toBottomOf="@id/tv_login_title"
            tools:text="+8618149561495" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/tv_login_tip">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_password"
                android:layout_width="match_parent"
                android:textColorHint="?colorOnSurfaceVariant"
                android:textColor="?colorOnSurface"
                android:layout_height="wrap_content"
                android:hint="@string/enter_password"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_password_again"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/input_layout_password">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_password_again"
                android:layout_width="match_parent"
                android:textColorHint="?colorOnSurfaceVariant"
                android:textColor="?colorOnSurface"
                android:layout_height="wrap_content"
                android:hint="@string/enter_password_again"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_next"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="@dimen/space24"
            android:text="@string/setting"
            app:layout_constraintTop_toBottomOf="@id/input_layout_password_again" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_skip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/activity_vertical_margin"
            android:text="@string/skip_setting"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@id/btn_next"
            app:layout_constraintStart_toStartOf="@id/btn_next"
            app:layout_constraintTop_toBottomOf="@id/btn_next" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>