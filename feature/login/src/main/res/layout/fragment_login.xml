<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_onekey_login_bg" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/view_bottom_space"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/app_bar"
            layout="@layout/toolbar_normal" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_logo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="@dimen/space24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar"
            app:shapeAppearance="?attr/shapeAppearanceCornerMedium"
            app:srcCompat="@mipmap/ic_launcher_round" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space36"
            android:layout_marginEnd="@dimen/space36"
            app:layout_constraintBottom_toTopOf="@id/btn_qq_login"
            app:layout_constraintTop_toBottomOf="@id/img_logo"
            app:layout_constraintVertical_bias="0.2">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabIndicatorColor="@color/white"
                app:tabTextColor="@color/white">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_with_mobile_phone_number" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/email_login" />
            </com.google.android.material.tabs.TabLayout>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_input_container"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:cardBackgroundColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tab_layout_login"
                app:layout_constraintVertical_bias="0.05">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_phone_input_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_bind_phone_zone_num"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:background="@null"
                        android:maxLines="1"
                        android:padding="@dimen/activity_horizontal_margin"
                        android:singleLine="true"
                        android:text="+86"
                        android:textSize="@dimen/font_normal"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="RtlSymmetry"
                        tools:text="+86" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/edit_bind_phone_number"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@null"
                        android:gravity="center_vertical|start"
                        android:hint="@string/enter_phone_number"
                        android:inputType="number"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textSize="@dimen/font_normal"
                        app:layout_constraintBottom_toBottomOf="@id/tv_bind_phone_zone_num"
                        app:layout_constraintEnd_toStartOf="@id/btn_clear_text"
                        app:layout_constraintStart_toEndOf="@id/tv_bind_phone_zone_num"
                        app:layout_constraintTop_toTopOf="@id/tv_bind_phone_zone_num"
                        tools:ignore="RtlSymmetry"
                        tools:text="hahaha" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btn_clear_text"
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginEnd="@dimen/activity_horizontal_margin"
                        android:padding="4dp"
                        android:scaleType="fitXY"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ico_remove" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_input_email_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/half_normal_margin">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/edit_input_email"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:gravity="center_vertical|start"
                        android:hint="@string/enter_email"
                        android:inputType="textEmailAddress"
                        android:maxLines="1"
                        android:padding="@dimen/half_normal_margin"
                        android:singleLine="true"
                        android:textSize="@dimen/font_normal"
                        app:layout_constraintEnd_toStartOf="@id/btn_email_clear_text"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="RtlSymmetry"
                        tools:text="hahaha" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btn_email_clear_text"
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_marginEnd="@dimen/activity_horizontal_margin"
                        android:padding="4dp"
                        android:scaleType="fitXY"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ico_remove" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_next_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_marginTop="@dimen/space24"
                app:layout_constraintTop_toBottomOf="@id/layout_input_container">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_request_code"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/get_verification_code"
                    android:textColor="?colorOnPrimary"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_request_email_code"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/get_verification_code"
                    android:textColor="?colorOnPrimary"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_login_account"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/activity_vertical_margin"
                android:text="@string/login_by_account_pwd"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_next_container" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_qq_login"
            style="?attr/materialIconButtonFilledTonalStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_horizontal_margin"
            android:layout_marginBottom="@dimen/half_normal_margin"
            app:backgroundTint="@color/white12"
            app:icon="@drawable/ico_qq"
            app:iconSize="28dp"
            app:iconTint="@color/grey50"
            app:layout_constraintBottom_toTopOf="@id/check_box"
            app:layout_constraintEnd_toStartOf="@id/btn_wechat_login"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_wechat_login"
            style="?attr/materialIconButtonFilledTonalStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            app:backgroundTint="@color/white12"
            app:icon="@drawable/ico_wechat"
            app:iconSize="28dp"
            app:iconTint="@color/grey50"
            app:layout_constraintEnd_toStartOf="@id/btn_email_login"
            app:layout_constraintStart_toEndOf="@id/btn_qq_login"
            app:layout_constraintTop_toTopOf="@id/btn_qq_login" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_email_login"
            style="?attr/materialIconButtonFilledTonalStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            android:visibility="gone"
            app:backgroundTint="@color/white12"
            app:icon="@drawable/ico_wechat"
            app:iconSize="28dp"
            app:iconTint="@color/grey50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_wechat_login"
            app:layout_constraintTop_toTopOf="@id/btn_qq_login" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/check_box"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/space36"
            android:button="@drawable/sel_checkbox"
            app:buttonTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_check_content"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_check_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_small"
            app:layout_constraintBottom_toBottomOf="@id/check_box"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/check_box"
            app:layout_constraintTop_toTopOf="@id/check_box"
            tools:text="勾选即同意《隐私政策条款》及《用户协议》" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_bottom_space"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>