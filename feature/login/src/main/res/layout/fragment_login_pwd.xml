<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurfaceDim">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />

    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingStart="@dimen/space24"
        android:paddingTop="@dimen/space36"
        android:paddingEnd="@dimen/space24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space12"
            android:text="@string/login_by_account_pwd"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:layout_constraintTop_toBottomOf="@id/tv_login_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/tab_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="509:112"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/img_login_top01" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabRippleColor="@android:color/transparent"
                app:tabSelectedTextAppearance="@style/TextAppearance.AppCompat.Large"
                app:tabSelectedTextColor="?colorOnSurface"
                app:tabTextAppearance="@style/TextAppearance.AppCompat.Body1">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_with_mobile_phone_number" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/email_login" />
            </com.google.android.material.tabs.TabLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_input_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_bottom_corner_surface_bg"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin"
                android:paddingBottom="@dimen/space24"
                app:layout_constraintTop_toBottomOf="@id/tab_bg">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/input_layout_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:boxBackgroundColor="?colorSurfaceContainer"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_account"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/enter_account"
                        android:inputType="phone"
                        android:textColor="?colorOnSurface"
                        android:textColorHint="?colorOnSurfaceVariant"
                        tools:text="***********" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/input_layout_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_vertical_margin"
                    app:boxBackgroundColor="?colorSurfaceContainer"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintTop_toBottomOf="@id/input_layout_account">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/enter_password"
                        android:inputType="textPassword"
                        android:textColor="?colorOnSurface"
                        android:textColorHint="?colorOnSurfaceVariant"
                        tools:text="fefefefefefefefefe" />

                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_forget_phone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin"
                    android:text="@string/forget_password"
                    android:textColor="?colorOnSurfaceVariant"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/input_layout_password" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_forget_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin"
                    android:text="@string/forget_password"
                    android:textColor="?colorOnSurfaceVariant"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/input_layout_password" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_next"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="@dimen/half_normal_margin"
                    android:text="@string/login"
                    app:layout_constraintTop_toBottomOf="@id/tv_forget_phone" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_email_next"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="@dimen/half_normal_margin"
                    android:text="@string/login"
                    app:layout_constraintTop_toBottomOf="@id/tv_forget_email" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/check_box"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space36"
        android:button="@drawable/sel_checkbox"
        app:buttonTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_check_content"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_check_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_small"
        app:layout_constraintBottom_toBottomOf="@id/check_box"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/check_box"
        app:layout_constraintTop_toTopOf="@id/check_box"
        tools:text="勾选即同意《隐私政策条款》及《用户协议》" />

</androidx.constraintlayout.widget.ConstraintLayout>