<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurface">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />

    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tv_login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space36"
        android:text="@string/bind_phone"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_title_large"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/layout_phone_number_container"
        app:layout_constraintTop_toBottomOf="@id/app_bar" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_phone_number_container"
        style="?attr/materialCardViewFilledStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/space36"
        android:layout_marginTop="@dimen/space36"
        android:layout_marginEnd="@dimen/space36"
        app:cardBackgroundColor="?colorSurfaceContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_login_title">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_bind_phone_zone_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:background="@null"
                android:maxLines="1"
                android:padding="@dimen/activity_horizontal_margin"
                android:singleLine="true"
                android:textColor="?colorOnSurface"
                android:text="+86"
                android:textSize="@dimen/font_normal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="RtlSymmetry"
                tools:text="+86" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:boxBackgroundMode="none"
                app:endIconDrawable="@drawable/ic_close"
                app:endIconMode="clear_text"
                app:endIconTint="?colorOnSurface"
                app:hintEnabled="false"
                app:layout_constraintBottom_toBottomOf="@id/tv_bind_phone_zone_num"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_bind_phone_zone_num"
                app:layout_constraintTop_toTopOf="@id/tv_bind_phone_zone_num">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_bind_phone_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical|start"
                    android:inputType="phone"
                    android:maxLines="1"
                    android:textColorHint="?colorOnSurfaceVariant"
                    android:textColor="?colorOnSurface"
                    android:singleLine="true"
                    android:textSize="@dimen/font_normal"
                    tools:ignore="RtlSymmetry"
                    tools:text="18149561495" />

            </com.google.android.material.textfield.TextInputLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_request_code"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginTop="@dimen/space24"
        android:text="@string/get_verification_code"
        app:layout_constraintEnd_toEndOf="@id/layout_phone_number_container"
        app:layout_constraintStart_toStartOf="@id/layout_phone_number_container"
        app:layout_constraintTop_toBottomOf="@id/layout_phone_number_container" />

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/check_box"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/space36"
        android:button="@drawable/sel_checkbox"
        app:buttonTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_check_content"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/layout_phone_number_container" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_check_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="320dp"
        android:text="@string/check_and_agree"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_small"
        app:layout_constraintBottom_toBottomOf="@id/check_box"
        app:layout_constraintEnd_toEndOf="@id/layout_phone_number_container"
        app:layout_constraintStart_toEndOf="@id/check_box"
        app:layout_constraintTop_toTopOf="@id/check_box" />

</androidx.constraintlayout.widget.ConstraintLayout>