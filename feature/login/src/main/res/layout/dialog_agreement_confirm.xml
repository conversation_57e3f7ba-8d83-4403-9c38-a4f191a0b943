<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black26">

    <com.google.android.material.card.MaterialCardView
        style="?attr/materialCardViewFilledStyle"
        android:layout_width="268dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.45">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/activity_vertical_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_top_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                app:layout_constraintDimensionRatio="267:60"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/bg_agreement" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:text="@string/please_read_and_agree_to_the_relevant_terms"
                android:textColor="?colorOnSurfaceInverse"
                android:textSize="@dimen/font_large"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/img_top_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/img_top_bg" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_dialog_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:lineSpacingExtra="6dp"
                android:padding="@dimen/activity_horizontal_margin"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_small"
                app:layout_constraintTop_toBottomOf="@id/img_top_bg"
                tools:text="感谢您信任并使用妙诗App。如您同意《用户协议》和《隐私政策》，请点击“同意并继续”开始使用我们的产品和服务。" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sure"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:text="@string/agree_and_continue"
                android:textSize="@dimen/font_small"
                app:layout_constraintTop_toBottomOf="@id/tv_dialog_content" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_skip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/half_normal_margin"
                android:text="@string/disagree"
                android:textColor="?colorOnSurfaceVariant"
                android:textSize="@dimen/font_small"
                app:layout_constraintTop_toBottomOf="@id/btn_sure" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>


</androidx.constraintlayout.widget.ConstraintLayout>