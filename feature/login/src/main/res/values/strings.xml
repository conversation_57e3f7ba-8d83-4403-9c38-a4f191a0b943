<resources>
    <!-- Strings used for fragments for navigation -->


    <string name="sign_in_with_apple">Sign in with Apple</string>
    <string name="sign_in_with_google">Sign in with Google</string>
    <string name="configure_password">Set login password</string>
    <string name="login_by_account_pwd">Log in with account and password</string>
    <string name="enter_account">Enter the account</string>
    <string name="enter_password">Enter the password</string>
    <string name="enter_password_again">Enter the password again</string>
    <string name="uninstall_wechat">WeChat is not installed on your phone.</string>
    <string name="open_wechat_and_scan">Please open WeChat and scan the QR code</string>
    <string name="please_read_and_agree_to_the_relevant_terms">Please read and agree to the relevant terms.</string>
    <string name="agree_and_continue">Agree and continue</string>
    <string name="disagree">Disagree</string>
    <string name="enter_the_verification_code">Enter the verification code</string>
    <string name="login_with_mobile_phone_number">Phone Login</string>
    <string name="email_login">Email Login</string>
    <string name="enter_phone_number">Please enter mobile phone number.</string>
    <string name="enter_email">Please enter your mobile phone number.</string>
    <string name="get_verification_code">Get verification code</string>
    <string name="bind_phone">Bind mobile phone number</string>
    <string name="check_and_agree">Checking the box indicates your consent to the "Privacy Policy Terms" and the "User Agreement".</string>
    <string name="forget_password">Forget the password?</string>
    <string name="login">Login</string>
    <string name="reset_password">Reset password</string>
    <string name="skip_setting">Skip</string>
    <string name="register_or_login">Register/Login</string>
    <string name="enter_code">Enter the verification code</string>
    <!--    <string name="google_login_service_id">191133030126-urkoj35kreehfj9iu5ujpvmk6e5a7rac.apps.googleusercontent.com</string>-->

    <string name="authorize_landing">请授权登录</string>
    <string name="login_failure">登录出错,请重试</string>
</resources>