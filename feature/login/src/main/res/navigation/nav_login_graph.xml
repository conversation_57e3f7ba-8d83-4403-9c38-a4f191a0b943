<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_cat_add_graph"
    app:startDestination="@id/loginHome">

    <fragment
        android:id="@+id/loginHome"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginHomeFragment"
        android:label="@string/login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/actionHomeToSmsVerifyPage"
            app:destination="@id/smsVerifyFragment" />

        <action
            android:id="@+id/actionHomeToEmailSmsVerifyPage"
            app:destination="@id/emailSmsVerifyFragment" />

        <action
            android:id="@+id/actionHomeToPasswordLoginPage"
            app:destination="@id/loginByPasswordFragment" />

        <action
            android:id="@+id/actionHomeToPasswordPage"
            app:destination="@id/passwordSettingFragment" />
        <!--        <action-->
        <!--            android:id="@+id/actionHomeToBindPhonePage"-->
        <!--            app:destination="@id/bindPhoneFragment" />-->
    </fragment>

    <fragment
        android:id="@+id/emailSmsVerifyFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginEmailSMSVerifyFragment"
        tools:layout="@layout/fragment_email_sms_verify">
        <action
            android:id="@+id/actionSmsVerifyToPasswordPage"
            app:destination="@id/passwordSettingFragment" />
    </fragment>


    <fragment
        android:id="@+id/smsVerifyFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginSMSVerifyFragment"
        tools:layout="@layout/fragment_sms_verify">
        <action
            android:id="@+id/actionSmsVerifyToPasswordPage"
            app:destination="@id/passwordSettingFragment" />
    </fragment>

    <fragment
        android:id="@+id/passwordSettingFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginPasswordSettingFragment"
        tools:layout="@layout/fragment_login_pwd_setting">

    </fragment>

    <fragment
        android:id="@+id/loginByPasswordFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginByPasswordFragment"
        tools:layout="@layout/fragment_login_pwd">
        <action
            android:id="@+id/actionToResettingPasswordPage"
            app:destination="@id/resettingPwdFragment" />

        <action
            android:id="@+id/actionToEmailResettingPasswordPage"
            app:destination="@id/emailResetPwdFragment" />
    </fragment>


    <fragment
        android:id="@+id/resettingPwdFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginPasswordResettingFragment"
        tools:layout="@layout/fragment_login_pwd_resetting">

    </fragment>

    <fragment
        android:id="@+id/emailResetPwdFragment"
        android:name="com.ruiheng.xmuse.feature.login.ui.LoginEmailPasswordResettingFragment"
        tools:layout="@layout/fragment_login_pwd_resetting">

    </fragment>
    <!--    <fragment-->
    <!--        android:id="@+id/bindPhoneFragment"-->
    <!--        android:name="com.ruiheng.xmuse.feature.login.ui.LoginBindPhoneFragment"-->
    <!--        tools:layout="@layout/fragment_login_bind_phone">-->
    <!--        <action-->
    <!--            android:id="@+id/actionBindPhoneToSmsVerifyPage"-->
    <!--            app:destination="@id/smsVerifyFragment" />-->
    <!--    </fragment>-->
    <!--    <fragment-->
    <!--        android:id="@+id/scheduleAddFragment"-->
    <!--        android:name="com.ruiheng.xmuse.feature.schedule.ui.ScheduleAddFragment"-->
    <!--        android:label="@string/Add_a_Schedule"-->
    <!--        tools:layout="@layout/fragment_schedule_add">-->
    <!--        <action-->
    <!--            android:id="@+id/actionAddToAddMeal"-->
    <!--            app:destination="@id/scheduleAddMealFragment" />-->
    <!--    </fragment>-->

    <!--    <fragment-->
    <!--        android:id="@+id/scheduleAddMealFragment"-->
    <!--        android:name="com.ruiheng.xmuse.feature.schedule.ui.ScheduleAddMealFragment"-->
    <!--        android:label="@string/Add_meal"-->
    <!--        tools:layout="@layout/fragment_schedule_meal_add">-->

    <!--    </fragment>-->
</navigation>