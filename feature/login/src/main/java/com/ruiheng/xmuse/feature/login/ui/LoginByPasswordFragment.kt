package com.ruiheng.xmuse.feature.login.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.KeyboardUtils
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurfaceInverse
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginPwdBinding

class LoginByPasswordFragment : BaseLoginFragment<FragmentLoginPwdBinding>() {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentLoginPwdBinding.inflate(inflater, container, false)


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        binding.tvForgetPhone.setOnClickListener(this)
        binding.tvForgetEmail.setOnClickListener(this)
        binding.btnNext.setOnClickListener(this)
        binding.btnEmailNext.setOnClickListener(this)
        val nextBtn = arrayOf(binding.btnNext, binding.btnEmailNext)
        val forgetBtn = arrayOf(binding.tvForgetPhone, binding.tvForgetEmail)
        initLoginTab(
            tabLayout = binding.tabLayoutLogin,
            tabImageBg = binding.tabBg,
            onSelectedCallback = { position ->
                nextBtn.forEachIndexed { index, materialButton ->
                    materialButton.visibility = if (index == position) View.VISIBLE else View.GONE
                }
                forgetBtn.forEachIndexed { index, appCompatTextView ->
                    appCompatTextView.visibility =
                        if (index == position) View.VISIBLE else View.GONE
                }
            })
        initAgreementText(binding.tvCheckContent)
        binding.checkBox.isChecked = loginViewModel.protocolChecked
    }

    /**
     * 使用手机号/密码登录
     */
    private fun startLogin() {
        KeyboardUtils.hideSoftInput(binding.editPassword)
        KeyboardUtils.hideSoftInput(binding.editAccount)
        val account = binding.editAccount.text.toString()
        val password = binding.editPassword.text.toString()
        if (account.isEmpty() || password.isEmpty()) return
        checkAlreadyConfirmAgreement(binding.checkBox) {
            autoRemoveFlow(loginViewModel.loginByPassword(account, password)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result.isSuccessWithData()) {
                    requireActivity().finish()
                }
            }
        }
    }

    /**
     * 使用邮箱/密码登录
     */
    private fun startLoginByEmail() {
        KeyboardUtils.hideSoftInput(binding.editPassword)
        KeyboardUtils.hideSoftInput(binding.editAccount)
        val account = binding.editAccount.text.toString()
        val password = binding.editPassword.text.toString()
        if (account.isEmpty() || password.isEmpty()) return
        checkAlreadyConfirmAgreement(binding.checkBox) {
            autoRemoveFlow(loginViewModel.loginEmailByPassword(account, password)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result.isSuccessWithData()) {
                    requireActivity().finish()
                }
            }
        }

    }

    override fun onWidgetClick(view: View) {
        when (view) {
            //忘记密码
            binding.tvForgetPhone -> {
                startNavPage(R.id.actionToResettingPasswordPage)
            }

            //邮箱忘记密码
            binding.tvForgetEmail -> {
                startNavPage(R.id.actionToEmailResettingPasswordPage)
            }

            binding.btnEmailNext -> {
                startLoginByEmail()
            }

            binding.btnNext -> {
                startLogin()
            }
        }
        super.onWidgetClick(view)
    }
}