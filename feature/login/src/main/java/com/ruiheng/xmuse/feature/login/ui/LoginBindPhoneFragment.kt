//package com.ruiheng.xmuse.feature.login.ui
//
//import android.os.Bundle
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import com.ruiheng.xmuse.core.common.result.Result
//import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
//import com.ruiheng.xmuse.core.ui.helper.controlLoading
//import com.ruiheng.xmuse.core.ui.showImage
//import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
//import com.ruiheng.xmuse.feature.login.R
//import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginBindPhoneBinding
//
//class LoginBindPhoneFragment : BaseLoginFragment<FragmentLoginBindPhoneBinding>(),
//    IBasePageErrorView {
//    override fun bindDataBindingView(
//        inflater: LayoutInflater,
//        container: ViewGroup?
//    ) = FragmentLoginBindPhoneBinding.inflate(inflater, container, false)
//
//    override fun initView(saveInsBundle: Bundle?, contentView: View) {
//        super.initView(saveInsBundle, contentView)
//        binding.btnRequestCode.setOnClickListener(this)
//        binding.imgLoginBg.showImage(R.drawable.img_onekey_login_bg)
////        binding.appBar.dividerToolbar.visibility = View.GONE
//    }
//
//    private fun startSendSMS() {
//        val phone = binding.editBindPhoneNumber.text?.toString() ?: return
//        autoRemoveFlow(loginViewModel.startSendSMS(phone)) { result ->
//            result.controlLoading(this)
//            result.showResult()
//            if (result is Result.Success) {
//                loginViewModel.startSMSCodePage(phone)
//                startNavPage(R.id.actionBindPhoneToSmsVerifyPage)
//            }
//        }
//    }
//
//    override fun onWidgetClick(view: View) {
//        when (view) {
//
//            binding.btnRequestCode -> {
//                startSendSMS()
//            }
//
//            else -> {
//                super.onWidgetClick(view)
//            }
//        }
//    }
//}