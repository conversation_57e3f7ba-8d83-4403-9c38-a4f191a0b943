package com.ruiheng.xmuse.feature.login.ui

import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.CheckBox
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.blankj.utilcode.util.SpanUtils
import com.google.android.material.tabs.TabLayout
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.privacyPolicy
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.userProtocol
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurface
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurfaceVariant
import com.ruiheng.xmuse.core.ui.helper.isNightMode
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseNavFragment
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.feature.login.LoginActivity
import com.ruiheng.xmuse.feature.login.LoginViewModel
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.ui.dialog.ConfirmAgreementDialog
import org.w3c.dom.Text

abstract class BaseLoginFragment<T : ViewBinding> : BaseNavFragment<T>() {

    protected val loginViewModel: LoginViewModel by lazy {
        (requireActivity() as LoginActivity).loginViewModel
    }

    private val imageBg :AppCompatImageView? by lazy {
        view?.findViewById<AppCompatImageView?>(R.id.img_login_bg)
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        imageBg?.visibility =
            if (requireContext().isNightMode()) View.GONE else View.VISIBLE
    }

    private val imageTableBg by lazy {
        arrayOf(
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top01,
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top02
        )
    }

    private val imageTableDarkBg by lazy {
        arrayOf(
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top_dark_01,
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top_dark02
        )
    }

    fun initLoginTab(
        tabLayout: TabLayout,
        tabTitle: Array<String> = arrayOf("手机登录", "邮箱登录"),
        tabImageBg: AppCompatImageView?,
        onSelectedCallback: (Int) -> Unit
    ) {
        tabTitle.forEachIndexed { index, s ->
            tabLayout.getTabAt(index)?.text = s
        }
        fun updateTab() {
            val position = tabLayout.selectedTabPosition
            val imageBgList = if (requireContext().isNightMode()) imageTableDarkBg else imageTableBg
            tabImageBg?.showImage(imageBgList[position])
            onSelectedCallback.invoke(position)
        }
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTab()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })
        updateTab()
    }

    protected var protocolChecked: Boolean = false
    protected fun checkAlreadyConfirmAgreement(checkedBox: CheckBox, checkedCallback: () -> Unit) {
        if (checkedBox.isChecked) {
            return checkedCallback.invoke()
        } else {
            ConfirmAgreementDialog {
                loginViewModel.protocolChecked = true
                checkedBox.isChecked = true
                checkedCallback.invoke()
            }.startShow(childFragmentManager)
        }
    }

    protected fun initAgreementText(
        text: TextView,
        @ColorInt normalColor: Int = requireContext().getColorOnSurfaceVariant(),
        @ColorInt importantColor: Int = requireContext().getColorOnSurface()
    ) {
        SpanUtils.with(text)
            .append("勾选即同意")
            .setForegroundColor(normalColor)
            .append("《隐私政策条款》")
            .setClickSpan(importantColor, false) {
                AgentWebActivity.start(requireContext(), privacyPolicy, "")
            }
            .append("及")
            .setForegroundColor(normalColor)
            .append("《用户协议》")
            .setClickSpan(importantColor, false) {
                AgentWebActivity.start(requireContext(), userProtocol, "")
            }
            .create()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val uiMode = newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK
        when (uiMode) {
            Configuration.UI_MODE_NIGHT_YES -> {
                imageBg?.visibility = View.GONE
            }

            Configuration.UI_MODE_NIGHT_NO -> {
                imageBg?.visibility = View.VISIBLE
            }
        }
    }
}