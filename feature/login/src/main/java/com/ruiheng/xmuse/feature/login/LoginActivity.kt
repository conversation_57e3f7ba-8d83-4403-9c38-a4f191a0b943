/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.feature.login

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.ToastUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.login.databinding.ActivityLoginBinding
import com.ruiheng.xmuse.feature.login.vo.QQLoginResult
import com.tencent.tauth.IUiListener
import com.tencent.tauth.Tencent
import com.tencent.tauth.UiError
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class LoginActivity : BaseBindingActivity<ActivityLoginBinding>() {
    override fun bindDataBindingView() = ActivityLoginBinding.inflate(layoutInflater)
    val loginViewModel: LoginViewModel by viewModels()

    var qqCodeGet: Boolean = false

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

        binding.qqCodeGet.setOnClickListener(this)
    }

    val mTencentLoginServerSideListener = object : IUiListener {
        override fun onComplete(p0: Any?) {
            if (qqCodeGet) {
                val result = GsonUtils.fromJson(p0.toString(), QQLoginResult::class.java)
                val accessToken = result?.access_token ?: throw Exception()
                binding.qqCodeGet.text = accessToken
            } else {
                startQQLogin(p0.toString())
            }
        }

        override fun onError(p0: UiError?) {
        }

        override fun onCancel() {
        }

        override fun onWarning(p0: Int) {
        }
    }

    private fun startQQLogin(qqLoginResult: String?) {
        if (qqLoginResult.isNullOrEmpty()) return
        try {
            val result = GsonUtils.fromJson(qqLoginResult, QQLoginResult::class.java)
            val accessToken = result?.access_token ?: throw Exception()
            autoRemoveFlow(loginViewModel.loginWithQQ(accessToken)) { loginResult ->
                loginResult.controlLoading(this)
                loginResult.showResult()
                if (loginResult.isSuccessWithData()) {
                    finish()
                }
            }
        } catch (e: Exception) {
            val result = Result.Error(e, null)
            result.showResult()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Tencent.onActivityResultData(requestCode, resultCode, data, mTencentLoginServerSideListener)
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.qqCodeGet -> {
                ClipboardUtils.copyText(binding.qqCodeGet.text.toString())
                ToastUtils.showShort("已复制")
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }

    override fun bindNavigationSpaceView() = binding.viewNavigationSpace
}
