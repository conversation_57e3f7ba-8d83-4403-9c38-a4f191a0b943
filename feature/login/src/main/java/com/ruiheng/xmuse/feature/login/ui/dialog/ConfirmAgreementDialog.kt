package com.ruiheng.xmuse.feature.login.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.blankj.utilcode.util.SpanUtils
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.privacyPolicy
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.userProtocol
import com.ruiheng.xmuse.core.ui.helper.dialog.FullScreenDialogFragment
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurface
import com.ruiheng.xmuse.core.ui.helper.getColorPrimary
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.DialogAgreementConfirmBinding

class ConfirmAgreementDialog(private val onSureClick: () -> Unit) :
    FullScreenDialogFragment<DialogAgreementConfirmBinding>() {
    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogAgreementConfirmBinding.inflate(layoutInflater)

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        binding.imgTopBg.showImage(R.drawable.bg_agreement)
        SpanUtils.with(binding.tvDialogContent)
            .append("感谢您信任并使用妙诗App。如您同意")
            .setForegroundColor(requireContext().getColorOnSurface())
            .append("《用户协议》")
            .setClickSpan(requireContext().getColorPrimary(), false) {
                AgentWebActivity.start(requireContext(), userProtocol, "")
            }
            .append("和")
            .setForegroundColor(requireContext().getColorOnSurface())
            .append("《隐私政策》")
            .setClickSpan(requireContext().getColorPrimary(), false, View.OnClickListener {
                AgentWebActivity.start(requireContext(), privacyPolicy, "")
            })
            .append("，请点击“同意并继续”开始使用我们的产品和服务。")
            .setForegroundColor(requireContext().getColorOnSurface())
            .create()
        binding.tvSkip.setOnClickListener {
            dismiss()
        }
        binding.btnSure.setOnClickListener {
            dismiss()
            onSureClick.invoke()
        }
        return dialog
    }
}