package com.ruiheng.xmuse.feature.login.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.blankj.utilcode.util.KeyboardUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginPwdResettingBinding
import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginPwdSettingBinding

class LoginEmailPasswordResettingFragment : BaseLoginFragment<FragmentLoginPwdResettingBinding>() {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentLoginPwdResettingBinding.inflate(inflater, container, false)


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        binding.btnNext.setOnClickListener(this)
        binding.editAccount.setHint("输入邮箱地址")
        binding.tvCountDown.init(com.ruiheng.xmuse.core.ui.R.string.resend)
        binding.tvCountDown.setOnClickListener {
            val email = binding.editAccount.text.toString()
            if (email.isEmpty()) return@setOnClickListener
            autoRemoveFlow(loginViewModel.startSendEmailSMSForget(email)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    binding.tvCountDown.start()
                }
            }
        }
    }

    private fun startResetting() {
        KeyboardUtils.hideSoftInput(binding.editAccount)
        KeyboardUtils.hideSoftInput(binding.editVerify)
        KeyboardUtils.hideSoftInput(binding.editPassword)
        KeyboardUtils.hideSoftInput(binding.editPasswordAgain)
        val account = binding.editAccount.text.toString()
        val code = binding.editVerify.text.toString()
        val password = binding.editPassword.text.toString()
        val passwordAgain = binding.editPasswordAgain.text.toString()
        if (account.isEmpty() || code.isEmpty() || password.isEmpty() || passwordAgain.isEmpty()) return
        autoRemoveFlow(
            loginViewModel.emailResetPassword(
                account,
                code,
                password,
                passwordAgain
            )
        ) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                finishNavPage()
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {

            binding.btnNext -> {
                startResetting()
            }
        }
        super.onWidgetClick(view)
    }
}