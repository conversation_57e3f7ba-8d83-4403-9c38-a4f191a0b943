package com.ruiheng.xmuse.feature.login.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginPwdSettingBinding

class LoginPasswordSettingFragment : BaseLoginFragment<FragmentLoginPwdSettingBinding>() {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentLoginPwdSettingBinding.inflate(inflater, container, false)


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        binding.tvSkip.setOnClickListener(this)
        binding.btnNext.setOnClickListener(this)
    }

    private fun configurePassword() {
        val password = binding.editPassword.text.toString()
        val passwordAgain = binding.editPasswordAgain.text.toString()
        if (password.isNotEmpty() && passwordAgain.isNotEmpty()) {
            autoRemoveFlow(loginViewModel.configurePassword(password, passwordAgain)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result.isSuccessWithData()) {
                    requireActivity().finish()
                }
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.tvSkip -> {
                requireActivity().finish()
            }

            binding.btnNext -> {
                configurePassword()
            }
        }
        super.onWidgetClick(view)
    }
}