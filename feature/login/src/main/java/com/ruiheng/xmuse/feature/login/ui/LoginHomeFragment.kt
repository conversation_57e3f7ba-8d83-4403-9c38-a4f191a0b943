package com.ruiheng.xmuse.feature.login.ui

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.SpanUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.google.android.material.tabs.TabLayout
import com.mobile.auth.gatewayauth.AuthUIConfig
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper
import com.mobile.auth.gatewayauth.ResultCode
import com.mobile.auth.gatewayauth.TokenResultListener
import com.mobile.auth.gatewayauth.model.TokenRet
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.PHONE_ONEKEY_LOGIN_KEY
import com.ruiheng.xmuse.core.data.TENCENT_APP_ID
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.privacyPolicy
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.userProtocol
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurface
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurfaceVariant
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.ruiheng.xmuse.core.ui.ui.BaseFragment
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.feature.login.LoginActivity
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.FragmentLoginBinding
import com.ruiheng.xmuse.feature.login.ui.dialog.ConfirmAgreementDialog
import com.ruiheng.xmuse.wxapi.WXEntryActivity
import com.ruiheng.xmuse.wxapi.WXEntryActivity.Companion.BUNDLE_WX_LOGIN_CODE
import com.tencent.tauth.Tencent
import timber.log.Timber

class LoginHomeFragment : BaseLoginFragment<FragmentLoginBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentLoginBinding.inflate(inflater, container, false)

    private val mTokenResultListener = object : TokenResultListener {
        override fun onTokenSuccess(token: String?) {
            val tokenResult = TokenRet.fromJson(token)
            if (ResultCode.CODE_SUCCESS == tokenResult.code) {
                startOnKeyLoginRequest(tokenResult.token)
            }
        }

        override fun onTokenFailed(p0: String?) {
        }

    }

    private val mPhoneNumberAuthHelper by lazy {
        PhoneNumberAuthHelper.getInstance(requireContext(), mTokenResultListener)
    }

    /**
     * 微信登录
     */
    private val wxStartForResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->

        }

    private val loginTypeContainer by lazy {
        arrayOf(
            binding.layoutPhoneInputContainer,
            binding.layoutInputEmailContainer
        )
    }

    private val loginTypeNextBtn by lazy {
        arrayOf(
            binding.btnRequestCode,
            binding.btnRequestEmailCode
        )
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.tvLoginAccount.setOnClickListener(this)
        binding.btnRequestCode.setOnClickListener(this)
        binding.btnRequestEmailCode.setOnClickListener(this)
        binding.imgLoginBg.showImage(R.drawable.img_onekey_login_bg)
        binding.btnQqLogin.setOnClickListener(this)
        binding.btnWechatLogin.setOnClickListener(this)
        binding.appBar.btnToolbarBack.imageTintList = ContextCompat.getColorStateList(
            requireContext(),
            com.ruiheng.xmuse.core.ui.R.color.white
        )
//        binding.appBar.btnToolbarBack.visibility = View.INVISIBLE
        binding.btnClearText.setOnClickListener(this)
        binding.btnEmailClearText.setOnClickListener(this)
        binding.editBindPhoneNumber.addTextChangedListener {
            val text = binding.editBindPhoneNumber.text.toString()
            binding.btnClearText.visibility = if (text.isEmpty()) View.GONE else View.VISIBLE
        }
        fun updateSelectedLoginType() {
            val position = binding.tabLayoutLogin.selectedTabPosition
            loginTypeContainer.forEachIndexed { index, constraintLayout ->
                constraintLayout.visibility = if (index == position) View.VISIBLE else View.GONE
            }
            loginTypeNextBtn.forEachIndexed { index, materialButton ->
                materialButton.visibility = if (index == position) View.VISIBLE else View.GONE
            }
        }
        updateSelectedLoginType()
        binding.tabLayoutLogin.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateSelectedLoginType()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })
        initAgreementText(
            binding.tvCheckContent, ContextCompat.getColor(
                requireContext(),
                com.ruiheng.xmuse.core.ui.R.color.grey500
            ), ContextCompat.getColor(
                requireContext(),
                com.ruiheng.xmuse.core.ui.R.color.white
            )
        )
        val firstLogin = SPUtils.getInstance().getBoolean("FirstLogin", true)
        if (firstLogin) {
            ConfirmAgreementDialog {
                loginViewModel.protocolChecked = true
                binding.checkBox.isChecked = true
                startOneKeyLogin()
            }.startShow(childFragmentManager)
        } else {
            startOneKeyLogin()
        }
        SPUtils.getInstance().put("FirstLogin", false)
    }

    /**
     * 邮箱登录->发送验证码
     */
    private fun startSendEmail() {
        val email = binding.editInputEmail.text?.toString() ?: return
        if (email.isEmpty()) return
        checkAlreadyConfirmAgreement(binding.checkBox) {
            autoRemoveFlow(loginViewModel.sendEmailSMS(email)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    loginViewModel.startEmailSMSCodePage(email)
                    startNavPage(R.id.actionHomeToEmailSmsVerifyPage)
                }
            }
        }
    }

    /**
     * 发送短信验证码
     */
    private fun startSendSMS() {
        val phone = binding.editBindPhoneNumber.text?.toString() ?: return
        if (phone.isEmpty()) {
            return
        }
        checkAlreadyConfirmAgreement(binding.checkBox) {
            autoRemoveFlow(loginViewModel.startSendSMS(phone)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    loginViewModel.startSMSCodePage(phone)
                    startNavPage(R.id.actionHomeToSmsVerifyPage)
                }
            }
        }
    }

    private fun startOneKeyLogin() {
        try {
            val navigationBarHeight =
                (requireActivity() as BaseActivity).bottomNavigationSpaceLiveData.value

            mPhoneNumberAuthHelper.reporter?.setLoggerEnable(true)
            mPhoneNumberAuthHelper.setAuthSDKInfo(PHONE_ONEKEY_LOGIN_KEY)
            mPhoneNumberAuthHelper.checkEnvAvailable()
            mPhoneNumberAuthHelper.closeAuthPageReturnBack(false)
            val uiConfig = AuthUIConfig.Builder()
                .setPageBackgroundPath("img_onekey_login_bg")
                .setStatusBarColor(Color.TRANSPARENT)
                .setNavHidden(true)
                .setNumberColor(Color.WHITE)
                .setNumFieldOffsetY(180)
                .setLogBtnText("本机号码一键登录")
                .setLogBtnBackgroundPath("shape_onekey_login_btn_bg")

                .setSwitchAccText("其他手机号码登录")
                .setSwitchAccTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        com.ruiheng.xmuse.core.ui.R.color.grey300
                    )
                )
                .setSloganTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        com.ruiheng.xmuse.core.ui.R.color.grey400
                    )
                )
                .setPrivacyOffsetY_B((navigationBarHeight ?: 0) + 24.px.toInt())
                .setSloganTextSizeDp(12)
                .setSloganOffsetY(230)
//            .setStatusBarHidden(true)
                .create()
            mPhoneNumberAuthHelper.setAuthUIConfig(uiConfig)

            //用户控制返回键及左上角返回按钮效果
//            mPhoneNumberAuthHelper.userControlAuthPageCancel()
            //用户禁用utdid
            //mPhoneNumberAuthHelper.prohibitUseUtdid();
            //授权页是否跟随系统深色模式
            // mPhoneNumberAuthHelper.setAuthPageUseDayLight(true);
            //授权页物理返回键禁用
            //mPhoneNumberAuthHelper.closeAuthPageReturnBack(true);
            //横屏水滴屏全屏适配
            mPhoneNumberAuthHelper.keepAuthPageLandscapeFullSreen(true)
            //sdk内置所有界面隐藏底部导航栏
            mPhoneNumberAuthHelper.keepAllPageHideNavigationBar()
            //授权页扩大协议按钮选择范围至我已阅读并同意
            mPhoneNumberAuthHelper.expandAuthPageCheckedScope(true)

            //弹窗式授权页在软键盘出现时自动调整位置
            //mPhoneNumberAuthHelper.updateDialogWithKeyboard();
            getLoginToken(5000)
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 拉起授权页
     * @param timeout 超时时间
     */
    private fun getLoginToken(timeout: Int) {
        mPhoneNumberAuthHelper.getLoginToken(requireContext(), timeout)
//        showLoadingDialog("正在唤起授权页")
    }

    private fun startOnKeyLoginRequest(token: String) {
        mPhoneNumberAuthHelper.setAuthListener(null)
        mPhoneNumberAuthHelper.quitLoginPage()
        autoRemoveFlow(loginViewModel.startOneKeyLoginVerify(token)) { result ->
            result.controlLoading(this)
            result.showResult(showSuccess = true)
            if (result.isSuccessWithData()) {
                val user = result.data!!
                if (user.judgeInputPassword == true) {
                    startNavPage(R.id.actionHomeToPasswordPage)
                } else {
                    requireActivity().finish()
                }
            }
        }
    }

    private val mTencent by lazy {
        Tencent.createInstance(TENCENT_APP_ID, Utils.getApp())
    }

    private fun startQQLogin() {
        Tencent.setIsPermissionGranted(true)
        if (mTencent.isSessionValid) return
        mTencent.loginServerSide(
            requireActivity(),
            "all",
            (requireActivity() as LoginActivity).mTencentLoginServerSideListener
        )
    }

    private fun startWxLogin(code: String) {
        autoRemoveFlow(loginViewModel.loginWithWechat(code)) { result ->
            result.controlLoading(this)
            result.showResult(showSuccess = true)
            if (result.isSuccessWithData()) {
                val user = result.data!!
                if (user.judgeInputPassword == true) {
                    startNavPage(R.id.actionSmsVerifyToPasswordPage)
                } else {
                    requireActivity().finish()
                }
            }
        }
    }

    override fun onWidgetClick(view: View) = when (view) {
        //账号密码登录
        binding.tvLoginAccount -> {
            loginViewModel.protocolChecked = binding.checkBox.isChecked
            startNavPage(R.id.actionHomeToPasswordLoginPage)
        }
        //邮箱登录请求验证码
        binding.btnRequestEmailCode -> {
            startSendEmail()
        }
        //手机登录请求验证码
        binding.btnRequestCode -> {
            startSendSMS()
        }

        binding.btnClearText -> {
            binding.editBindPhoneNumber.setText("")
        }

        binding.btnEmailClearText -> {
            binding.editInputEmail.setText("")
        }

        binding.btnQqLogin -> {
            checkAlreadyConfirmAgreement(binding.checkBox) {
                (requireActivity() as LoginActivity).qqCodeGet = false
                startQQLogin()
            }
        }

        binding.btnWechatLogin -> {
            checkAlreadyConfirmAgreement(binding.checkBox) {
                if (!loginViewModel.startWxAuthor()) {
                    ToastUtils.showShort("请先安装微信")
//                    WXEntryActivity.startActivity(
//                        requireActivity() as BaseActivity,
//                        codeLogin = true
//                    )
                } else {
                    val wxIntent = WXEntryActivity.startActivity(requireActivity() as BaseActivity)
                    WXEntryActivity.onWxRespCallback = { code ->
                        if (code.isNotEmpty()) {
                            startWxLogin(code)
                        }
                    }
                    wxStartForResultLauncher.launch(wxIntent)
                }
            }
        }

        else -> {
            super.onWidgetClick(view)
        }
    }

    override fun bindNavigationSpaceView() = binding.viewBottomSpace
//    override fun bindOnBackCallback(): ((Boolean) -> Boolean)? = {
//        val intent = Intent(Intent.ACTION_MAIN)
//        intent.addCategory(Intent.CATEGORY_HOME)
//        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
//        startActivity(intent)
//        true
//    }
}