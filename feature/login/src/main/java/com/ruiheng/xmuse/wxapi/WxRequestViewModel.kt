package com.ruiheng.xmuse.wxapi

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import androidx.lifecycle.ViewModel
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesWxLoginRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import java.io.ByteArrayOutputStream
import javax.inject.Inject
import androidx.core.graphics.createBitmap
import androidx.lifecycle.LiveData
import com.blankj.utilcode.util.EncryptUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.WECHAT_APP_ID
import com.ruiheng.xmuse.core.data.WECHAT_SECRET
import com.ruiheng.xmuse.core.model.data.wxapi.WxAccessToken
import com.ruiheng.xmuse.core.model.data.wxapi.WxLogin
import com.ruiheng.xmuse.core.model.data.wxapi.WxUserInfo
import java.util.Locale
import java.util.TreeMap

@HiltViewModel
class WxRequestViewModel @Inject constructor(
    private val wxLoginRepository: NetResourcesWxLoginRepository
) : ViewModel(){

    fun getAccessToken(code: String): LiveData<Result<WxLogin>> {
        return wxLoginRepository.getAccessToken(WECHAT_APP_ID, WECHAT_SECRET, code)
    }

    fun getCodeAccessToken(): LiveData<Result<WxAccessToken>> =
        wxLoginRepository.getCodeAccessToken(WECHAT_APP_ID, WECHAT_SECRET)

    fun getWxTicket(accessToken: String) = wxLoginRepository.getWxTicket(accessToken)

    fun clearCodeLogin() = wxLoginRepository.clearCodeLogin()

    fun getWxCodeLoginSinature(ticket: String, uuid: String, currentTime: Long): String {
        val map = TreeMap<String, Any>()
        map["appid"] = WECHAT_APP_ID
        map["noncestr"] = uuid
        map["sdk_ticket"] = ticket
        map["timestamp"] = currentTime
        return EncryptUtils.encryptSHA1ToString(NetResourcesWxLoginRepository.createSign(map)).toLowerCase(
            Locale.getDefault())
    }

    fun getWeChatUser(accessToken: String, openId: String): LiveData<Result<WxUserInfo>> {
        return wxLoginRepository.getWeChatUser(accessToken, openId)
    }

    private fun bitmap2Bytes(bmp: Bitmap): ByteArray {

        var size = Math.min(bmp.width, bmp.height)
        var size2 = Math.min(bmp.width, bmp.height)

        val bitmap = createBitmap(size, size2, Bitmap.Config.RGB_565)
        val canvas = Canvas(bitmap)

        while (true) {
            canvas.drawBitmap(bmp, Rect(0, 0, size, size2), Rect(0, 0, size, size2), null)
            bmp.recycle()
            val output = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 50, output)
            bitmap.recycle()
            val byteA = output.toByteArray()
            try {
                output.close()
                return byteA
            } catch (e: Exception) {
            }
            size = bmp.height
            size2 = bmp.height
        }
    }
}