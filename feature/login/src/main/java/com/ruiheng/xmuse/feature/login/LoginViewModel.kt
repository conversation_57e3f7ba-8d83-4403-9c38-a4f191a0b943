package com.ruiheng.xmuse.feature.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_EMAIL_SMS_TYPE_FORGET_RESET
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_SMS_TYPE_FORGET_RESET
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesWxLoginRepository
import com.ruiheng.xmuse.core.model.data.UserData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val wxLoginRepository: NetResourcesWxLoginRepository,
    private val userDataRepository: UserDataRepository,
    private val netResourcesUserRepository: NetResourcesUserRepository
) : ViewModel() {

    private val _userDataStateFlow = MutableStateFlow<Result<UserData>>(Result.Loading())
    val userDataStateFlow: StateFlow<Result<UserData>> = _userDataStateFlow

    var protocolChecked: Boolean = false

    init {
        viewModelScope.launch {
            userDataRepository.userData.collect { user ->
                if (user != null) {
                    _userDataStateFlow.value = Result.Success(user)
                } else {
                    _userDataStateFlow.value = Result.Error(Exception())
                }
            }
        }
    }

    /**
     * 启动微信登录
     */
    fun startWxAuthor() = netResourcesUserRepository.startWxAuthor()

//    fun getCodeAccessToken(): LiveData<Result<WxAccessToken>> =
//        wxLoginRepo.getCodeAccessToken(WECHAT_APP_ID, WECHAT_APP_ID)


    /**
     * 发送邮箱二维码 -- 登录
     */
    fun sendEmailSMS(email: String) = netResourcesUserRepository.sendEmailSMS(email).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = Result.Loading(),
    )

    /**
     * 发送手机登录验证码
     */
    fun startSendSMS(phone: String) = netResourcesUserRepository.sendSMS(phone).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = Result.Loading(),
    )

    /**
     * 手机号-忘记密码-请求短信验证
     */
    fun startSendSMSForget(phone: String) = netResourcesUserRepository.sendSMS(
        phone, SEND_SMS_TYPE_FORGET_RESET
    ).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = Result.Loading(),
    )

    /**
     * 邮箱-忘记密码-请求短信验证
     */
    fun startSendEmailSMSForget(email: String) = netResourcesUserRepository.sendEmailSMS(
        email, SEND_EMAIL_SMS_TYPE_FORGET_RESET
    ).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = Result.Loading(),
    )

    fun startReSendSMS(): StateFlow<Result<Any>>? {
        if (currentVerifyPhone.isNullOrEmpty()) return null
        return netResourcesUserRepository.sendSMS(currentVerifyPhone!!).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    fun startReSendEmailSMS(): StateFlow<Result<Any>>? {
        if (currentVerifyEmail.isNullOrEmpty()) return null
        return netResourcesUserRepository.sendEmailSMS(currentVerifyEmail!!).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    private var currentVerifyPhone: String? = null
    private var currentVerifyEmail: String? = null

    /**
     * 设置当前需要验证的手机号码
     */
    private val _verifyPageTipMutableLiveData = MutableStateFlow<String>("")
    val verifyPageTipMutableLiveData = _verifyPageTipMutableLiveData as StateFlow<String>
    fun startSMSCodePage(phone: String) {
        currentVerifyPhone = phone
        _verifyPageTipMutableLiveData.value = "短信已发送至+86${currentVerifyPhone}"
    }

    /**
     * 设置当前需要验证的邮箱号
     */
    fun startEmailSMSCodePage(email: String) {
        currentVerifyEmail = email
        _verifyPageTipMutableLiveData.value = "验证码已发送至 ${currentVerifyEmail}"
    }

    /**
     * 邮箱码验证
     */
    fun emailVerifyLogin(smsCode: String): StateFlow<Result<UserData>>? {
        if (currentVerifyEmail.isNullOrEmpty()) return null
        return netResourcesUserRepository.emailLogin(currentVerifyEmail!!, smsCode).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    /**
     * 手机号码验证
     */
    fun phoneVerifyLogin(smsCode: String): StateFlow<Result<UserData>>? {
        if (currentVerifyPhone.isNullOrEmpty()) return null
        return netResourcesUserRepository.phoneLogin(currentVerifyPhone!!, smsCode).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    /**
     * 手机号登录
     */
    fun loginByPassword(account: String, password: String): StateFlow<Result<UserData>>? {
        return netResourcesUserRepository.appLogin(account, password).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    /**
     * 邮箱 账号/密码登录
     * @param account 邮箱账号
     * @param password 邮箱密码
     */
    fun loginEmailByPassword(account: String, password: String): StateFlow<Result<UserData>>? {
        return netResourcesUserRepository.appLoginByEmailAccount(account, password).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }


    fun loginWithQQ(code: String) = thirdPartyLogin(code, UserDataRepository.ThirdPartyLoginType.QQ)

    fun loginWithWechat(code: String) =
        thirdPartyLogin(code, UserDataRepository.ThirdPartyLoginType.WeChat)

    /**
     * 绑定第三方账号
     */
    fun bindThirdParty(code: String, thirdType: Int) =
        netResourcesUserRepository.bindThirdParty(code, thirdType).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )

    private fun thirdPartyLogin(code: String, thirdType: UserDataRepository.ThirdPartyLoginType) =
        netResourcesUserRepository.thirdLogin(code, thirdType).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )

    fun bpWechatLogin(code: String) =
        netResourcesUserRepository.bpWechatLogin(code).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )

    fun startOneKeyLoginVerify(token: String): StateFlow<Result<UserData>> {
        return netResourcesUserRepository.oneKeyLoginRequest(token).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    fun configurePassword(
        password: String,
        againPassword: String
    ): StateFlow<Result<Boolean>> {
        return netResourcesUserRepository.configurePassword(password, againPassword).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    fun resetPassword(
        account: String,
        smsCode: String,
        password: String,
        againPassword: String
    ): StateFlow<Result<Boolean>> {
        return netResourcesUserRepository.resetPassword(account, smsCode, password, againPassword)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5_000),
                initialValue = Result.Loading(),
            )
    }

    fun emailResetPassword(
        account: String,
        smsCode: String,
        password: String,
        againPassword: String
    ): StateFlow<Result<Boolean>> {
        return netResourcesUserRepository.emailResetPassword(
            account,
            smsCode,
            password,
            againPassword
        )
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5_000),
                initialValue = Result.Loading(),
            )
    }
}