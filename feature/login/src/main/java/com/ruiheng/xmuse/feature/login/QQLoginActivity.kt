/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.feature.login

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.feature.login.databinding.ActivityLoginFakeBinding
import com.ruiheng.xmuse.feature.login.vo.QQLoginResult
import com.tencent.tauth.IUiListener
import com.tencent.tauth.Tencent
import com.tencent.tauth.UiError
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class QQLoginActivity : BaseBindingActivity<ActivityLoginFakeBinding>() {
    override fun bindDataBindingView() = ActivityLoginFakeBinding.inflate(layoutInflater)

    companion object {
        val QQ_LOGIN_RESULT = "qqlogin"
    }

    private val mTencent by lazy {

        Tencent.createInstance("102600823", Utils.getApp())
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        Tencent.setIsPermissionGranted(true)
        if (mTencent.isSessionValid) return
        mTencent.loginServerSide(this, "all", mTencentLoginServerSideListener)
    }

    private val mTencentLoginServerSideListener = object : IUiListener {
        override fun onComplete(p0: Any?) {
//            setResult(RESULT_OK)
//            finish()
            startQQLogin(p0.toString())
        }

        override fun onError(p0: UiError?) {
        }

        override fun onCancel() {
        }

        override fun onWarning(p0: Int) {
        }
    }

    private fun startQQLogin(qqLoginResult: String?) {
        if (qqLoginResult.isNullOrEmpty()) return
        try {
            val result = GsonUtils.fromJson(qqLoginResult, QQLoginResult::class.java)
            val accessToken = result?.access_token ?: throw Exception()
            val resultIntent = Intent().apply {
                putExtra(QQ_LOGIN_RESULT, result)
            }
            setResult(RESULT_OK, resultIntent)
            finish()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Tencent.onActivityResultData(requestCode, resultCode, data, mTencentLoginServerSideListener)
        super.onActivityResult(requestCode, resultCode, data)
    }

}
