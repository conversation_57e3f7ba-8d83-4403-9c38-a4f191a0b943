package com.ruiheng.xmuse.feature.login.ui

import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.core.ui.widget.verify.SmsObserver
import com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout.OnInputListener
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.FragmentSmsVerifyBinding

class LoginSMSVerifyFragment : BaseLoginFragment<FragmentSmsVerifyBinding>() {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentSmsVerifyBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        autoStoppedFlow(loginViewModel.verifyPageTipMutableLiveData) { content ->
            binding.tvLoginTip.text = content
        }
        binding.tvCountDown.init(com.ruiheng.xmuse.core.ui.R.string.resend)
        binding.tvCountDown.start()
        binding.tvCountDown.setOnClickListener {
            startReRequestCode()
        }
        binding.layoutVerificationCode.setOnInputListener(object : OnInputListener {
            override fun onSuccess(code: String) {
                startVerify(code)
            }

            override fun onDataChange(code: String?, totalSize: Int) {
            }
        })
    }

    private fun startReRequestCode() {
        autoRemoveFlow(loginViewModel.startReSendSMS()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()){
                binding.tvCountDown.reset()
                binding.tvCountDown.start()
            }
        }
    }

    private fun startVerify(code: String) {

        autoRemoveFlow(loginViewModel.phoneVerifyLogin(code)) { result ->
            result.controlLoading(this)
            result.showResult(showSuccess = true)
            if (result.isSuccessWithData()) {
                val user = result.data!!
                if (user.judgeInputPassword == true) {
                    startNavPage(R.id.actionSmsVerifyToPasswordPage)
                } else {
                    requireActivity().finish()
                }
            }
        }
    }
}