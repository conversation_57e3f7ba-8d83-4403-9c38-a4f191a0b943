package com.ruiheng.xmuse.feature.user.ui.thirdparty

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.ui.bottommenu.BottomListMenuDialog
import com.ruiheng.xmuse.core.ui.custom.PublicMenuImp
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.login.QQLoginActivity
import com.ruiheng.xmuse.feature.login.QQLoginActivity.Companion.QQ_LOGIN_RESULT
import com.ruiheng.xmuse.feature.login.vo.QQLoginResult
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.UserThirdPartyMenuItem
import com.ruiheng.xmuse.feature.user.databinding.FragmentThirdPartyListBinding
import com.ruiheng.xmuse.feature.user.ui.BaseUserFragment
import com.ruiheng.xmuse.wxapi.WXEntryActivity

class ThirdPartyListFragment : BaseUserFragment<FragmentThirdPartyListBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentThirdPartyListBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.account_setting

    private lateinit var qqRequestLauncher: ActivityResultLauncher<Intent>

    private var relateAdapter: PublicNormalMenuRVAdapter<UserThirdPartyMenuItem> by autoCleared()
//    private val bottomPhotoDialog by lazy {
//        val title =
//            getString(com.ruiheng.xmuse.core.ui.R.string.choose_photo_type)
//        BottomPhotoDialog(title, onPhotoPathCallback)
//    }
//    private val onPhotoPathCallback: (String) -> Unit = { path ->
//        autoRemoveFlow(userViewModel.uploadImageFile(path)) { result ->
//            result.showResult()
//            if (result.isLoading()) {
//                result.controlLoading(this)
//            }
//            if (result.isError()) {
//                result.controlLoading(this)
//            }
//            if (result.isSuccessWithData()) {
//                updateUserAvatar(result.data!!)
//            }
//        }
//    }

    /**
     * 微信登录
     */
    private val wxStartForResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->

        }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        relateAdapter = PublicNormalMenuRVAdapter { menu ->
            if (menu.requestKey.isNullOrEmpty()) {
                startThirdPartyRequest(menu)
            } else {
                showBottomMenu(menu)
            }
        }
        binding.rvThirdParty.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvThirdParty.adapter = relateAdapter

        autoStoppedFlow(userViewModel.thirdPartyStateFlow) { result ->
            relateAdapter.submitList(result)
        }

        autoRemoveFlow(userViewModel.loadThirdPartyList()) { result ->
            result.controlLoading(this)
        }

        qqRequestLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data = result.data?.extras?.getSerializable(QQ_LOGIN_RESULT) as? QQLoginResult?
                val code = data?.access_token ?: return@registerForActivityResult
                startBindThirdParty(code, UserDataRepository.ThirdPartyLoginType.QQ.requestType)
            }
        }
    }

    private val bottomMenuStringArray by lazy {
        resources.getStringArray(com.ruiheng.xmuse.core.ui.R.array.change_or_unbind)
    }
    private val bottomMenuList by lazy {
        bottomMenuStringArray.map {
            object : PublicMenuImp {
                override fun bindTitle() = it
                override fun bindBgResources() = android.R.color.transparent
                override fun bindArrowShow() = View.GONE
                override fun bindTitleGravity() = Gravity.CENTER
            }
        }
    }

    private fun showBottomMenu(menuItem: UserThirdPartyMenuItem) {
        BottomListMenuDialog(menuList = bottomMenuList, menuClickCallback = { menu ->
            val index = bottomMenuStringArray.indexOf(menu.bindTitle())
            when (index) {
                0 -> {
                    startThirdPartyRequest(menuItem)
                }

                else -> {
                    unbindThirdParty(menuItem)
                }
            }
        }).showMenuDialog(childFragmentManager)
    }

    private fun startThirdPartyRequest(menuItem: UserThirdPartyMenuItem) {
        when (menuItem.thirdType) {
            UserDataRepository.ThirdPartyLoginType.QQ.requestType -> {
                qqRequestLauncher.launch(Intent(requireContext(), QQLoginActivity::class.java))
            }

            UserDataRepository.ThirdPartyLoginType.WeChat.requestType -> {
                if (!userViewModel.startWxAuthor()) {
                    ToastUtils.showShort("请先安装微信")
//                    WXEntryActivity.startActivity(
//                        requireActivity() as BaseActivity,
//                        codeLogin = true
//                    )
                } else {
                    val wxIntent = WXEntryActivity.startActivity(requireActivity() as BaseActivity)
                    WXEntryActivity.onWxRespCallback = { code ->
                        if (code.isNotEmpty()) {
                            startBindThirdParty(code ,UserDataRepository.ThirdPartyLoginType.WeChat.requestType)
                        }
                    }
                    wxStartForResultLauncher.launch(wxIntent)
                }
            }
        }
    }

    private fun startBindThirdParty(code: String, thirdPartyType: Int) {
        autoRemoveFlow(
            userViewModel.bindThirdParty(
                code,
                thirdPartyType
            )
        ) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isError() && result.data != null) {
                userViewModel.startBindThirdPartyConflictFragment(result.data)
                startNavPage(R.id.actionBindThirdPartyConflictPage)
            }
        }
    }

    private fun unbindThirdParty(menuItem: UserThirdPartyMenuItem) {
        val unionId = menuItem.requestKey ?: return
        val thirdPartyType = menuItem.thirdType ?: return
        autoRemoveFlow(userViewModel.unbindThirdParty(unionId, thirdPartyType)) { result ->
            result.controlLoading(this)
            result.showResult()
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}