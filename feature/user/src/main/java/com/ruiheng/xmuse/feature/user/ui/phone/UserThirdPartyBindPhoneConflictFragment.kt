package com.ruiheng.xmuse.feature.user.ui.phone

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserBindPhoneConflictBinding

class UserThirdPartyBindPhoneConflictFragment :
    BaseBindPhoneFragment<FragmentUserBindPhoneConflictBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserBindPhoneConflictBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.phone_bind_conflict

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.layoutForceBindPhone.setOnClickListener(this)
        binding.layoutChangePhone.setOnClickListener(this)
        autoStoppedFlow(userViewModel.userConflictDataStateFlow) { result ->
            val conflictUser = result?.targetAccount?.get(0)
            binding.tvContent.text = getString(
                R.string.phone_bind_thirdparty_conflict_content,
                conflictUser?.bindDisplayPhoneNum() ?: "",
                conflictUser?.nickName ?: ""
            )
        }
    }

    private fun startForceUpdatePhone() {
        autoRemoveFlow(userViewModel.forceUpdateUserPhone()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                finishNavPage()
            }
        }
    }

//    private fun showModifyPhoneDialog() {
//        val user = userViewModel.userDataStateFlow.value.data ?: return
//        BottomUserPhoneModifyDialog(user) { phone ->
//            autoRemoveFlow(userViewModel.startSendSMS(phone)) { result ->
//                if (result.isSuccessWithData()) {
//                    userViewModel.startSMSCodePage(phone = phone)
//                    showBottomSmsVerifyDialog()
//                }
//            }
//        }.showDialog(childFragmentManager)
//    }

    private fun showBottomSmsVerifyDialog() {
//        fun startVerify(code: String) {
//            autoRemoveFlow(userViewModel.bindPhone(code)) { result ->
//                result.controlLoading(this)
//                result.showResult()
//            }
//        }
//
//        val phone = userViewModel.verifyPageTipMutableLiveData.value
//        BottomUserSMSDialog(phone) { code ->
//            startVerify(code)
//        }.showDialog(childFragmentManager)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutForceBindPhone -> {
                startForceUpdatePhone()
            }

            binding.layoutChangePhone -> {
                finishNavPage()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}