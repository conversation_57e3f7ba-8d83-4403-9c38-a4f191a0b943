package com.ruiheng.xmuse.feature.user.ui.thirdparty

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserThirdPartyConflictBinding
import com.ruiheng.xmuse.feature.user.ui.BaseUserFragment

class UserThirdPartyModifyConflictFragment :
    BaseUserFragment<FragmentUserThirdPartyConflictBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserThirdPartyConflictBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.third_party_modify_conflict

    private fun RequestUserResult.getAccountSource() =
        when (source) {
            0 -> {
                "Web"
            }

            1 -> {
                "App"
            }

            else -> {
                "问卷系统"
            }
        }

    private fun RequestUserResult.getConflictAccountInfo() =
        "账号注册来源：${getAccountSource()}\n当前绑定手机号：${bindDisplayPhoneNum()}\n上次登录时间：${lastLoginTime}"

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.layoutForceBindPhone.setOnClickListener(this)
        binding.layoutChangePhone.setOnClickListener(this)
        autoStoppedFlow(userViewModel.thirdPartyConflictDataStateFlow) { result ->
            val currentUser = result?.curAccount?.get(0)
            binding.tvCurrentAccount.text = currentUser?.nickName
            binding.imgCurrentAccountAvatar.showImage(
                currentUser?.headPortrait,
                placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default
            )
            binding.tvCurrentAccountContent.text = currentUser?.getConflictAccountInfo()

            val conflictUser = result?.targetAccount?.get(0)
            binding.tvConflictAccount.text = conflictUser?.nickName
            binding.imgConflictAccountAvatar.showImage(
                conflictUser?.headPortrait,
                placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default
            )
            binding.tvConflictAccountContent.text = conflictUser?.getConflictAccountInfo()

            val thirdPartyType = UserDataRepository.ThirdPartyLoginType.entries.toTypedArray()
                .find { it.requestType == result?.thirdPartyType }
            val thirdPartNameTitle = thirdPartyType?.thirdPartyAccountTitle ?: ""
            val thirdPartAccountName =
                conflictUser?.thirdList?.find { it.thirdType == result.thirdPartyType }?.thirdName
                    ?: ""
            binding.tvContent.text = getString(
                R.string.thirdparty_conflict_content,
                thirdPartNameTitle,
                thirdPartAccountName,
                conflictUser?.nickName ?: "",
                thirdPartNameTitle
            )

            binding.tvForceBindPhoneTitle.text =
                getString(
                    R.string.force_to_bind_third_party,
                    thirdPartNameTitle,
                    thirdPartAccountName
                )
        }
    }

    private fun startForceUpdate() {
        autoRemoveFlow(userViewModel.forceBindThirdParty()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                finishNavPage()
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutForceBindPhone -> {
                startForceUpdate()
            }

            binding.layoutChangePhone -> {
                finishNavPage()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}