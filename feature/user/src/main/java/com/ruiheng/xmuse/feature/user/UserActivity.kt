/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.feature.user

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.feature.user.databinding.ActivityUserBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class UserActivity : BaseBindingActivity<ActivityUserBinding>() {
    override fun bindDataBindingView() = ActivityUserBinding.inflate(layoutInflater)
    val userViewModel: UserViewModel by viewModels()

    //    private val mUIConfig: BaseUIConfig? = null
    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

    }

    override fun bindNavigationSpaceView() = binding.viewNavigationSpace
}
