package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.ui.helper.dialog.BindBottomSheetDialog
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserNameModifyBinding
import okhttp3.internal.userAgent

class BottomUserNameModifyDialog(private val userName: String, private val onInputCallback: (String) -> Unit) :
    BindBottomSheetDialog<DialogBottomUserNameModifyBinding>() {

    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogBottomUserNameModifyBinding.inflate(layoutInflater)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.editName.setText(userName)
        binding.btSureAction.setOnClickListener {
            val input = binding.editName.text.toString()
            if (input.isNotEmpty()) {
                onInputCallback.invoke(input)
                dismiss()
            }
        }
    }
}