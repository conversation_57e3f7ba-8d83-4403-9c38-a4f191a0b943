package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserBirthdayModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserEmailModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPhoneModifyBinding
import java.util.Calendar

class BottomUserGenderModifyDialog(
    private val user: UserData,
    private val onInputCallback: (Int) -> Unit
) : BaseBindingBottomDialogFragment<DialogBottomUserBirthdayModifyBinding>() {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomUserBirthdayModifyBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        val displayValue = arrayOf(getString(R.string.gender_female), getString(R.string.gender_man))
        binding.pickerViewYear.minValue = 0
        binding.pickerViewYear.maxValue = 1
        binding.pickerViewYear.displayedValues = displayValue
        binding.pickerViewYear.value = if (user.gender != null) user.gender!! else 0
        binding.pickerViewYear.setOnValueChangedListener { picker, oldVal, newVal ->

        }

        binding.btSureAction.setOnClickListener(this)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btSureAction -> {
                val value = binding.pickerViewYear.value
                onInputCallback.invoke(value)
                dismiss()
            }
        }
        super.onWidgetClick(view)
    }
}