package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserEmailModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPhoneModifyBinding

class BottomUserEmailModifyDialog(
    private val user: UserData,
    private val onInputCallback: (String) -> Unit
) : BaseBindingBottomDialogFragment<DialogBottomUserEmailModifyBinding>() {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomUserEmailModifyBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.tvSubContent.text =
            getString(R.string.current_email_string, user.email)
        binding.btSureAction.setOnClickListener(this)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btSureAction -> {
                val input = binding.editName.text.toString()
                if (input.isNotEmpty()) {
                    onInputCallback.invoke(input)
                    dismiss()
                }
            }
        }
        super.onWidgetClick(view)
    }
}