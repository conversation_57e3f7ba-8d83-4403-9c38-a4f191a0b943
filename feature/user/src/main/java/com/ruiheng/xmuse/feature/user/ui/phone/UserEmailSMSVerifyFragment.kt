package com.ruiheng.xmuse.feature.user.ui.phone

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout.OnInputListener
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserEmailSmsVerifyBinding
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserSmsVerifyBinding

class UserEmailSMSVerifyFragment : BaseBindPhoneFragment<FragmentUserEmailSmsVerifyBinding>() {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserEmailSmsVerifyBinding.inflate(inflater, container, false)


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        autoStoppedFlow(userViewModel.verifyPageTipMutableLiveData) { content ->
            binding.tvLoginTip.text = content
        }
        binding.tvCountDown.init(com.ruiheng.xmuse.core.ui.R.string.resend)
        binding.tvCountDown.start()
        binding.layoutVerificationCode.setOnInputListener(object : OnInputListener {
            override fun onSuccess(code: String) {
                startVerify(code)
            }

            override fun onDataChange(code: String?, totalSize: Int) {
            }
        })
    }

    private fun startVerify(code: String) {
        autoRemoveFlow(userViewModel.bindEmail(code)) { result ->
            result.controlLoading(this)
            result.showResult(showSuccess = true)
            if (result.isSuccessWithData()) {
                requireActivity().finish()
            }
            if (result.isError() && result.data != null) {
                userViewModel.startModifyPhoneConflictFragment(result.data)
                startNavPage(R.id.actionEmailSmsVerifyToConflictPage, finish = true)
            }
        }
    }
}