package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.blankj.utilcode.util.ToastUtils
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.ui.helper.dialog.BindBottomSheetDialog
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserDeleteBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserNameModifyBinding
import okhttp3.internal.userAgent

class BottomUserDeleteDialog(private val onDeleteCallback: () -> Unit) :
    BindBottomSheetDialog<DialogBottomUserDeleteBinding>() {

    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogBottomUserDeleteBinding.inflate(layoutInflater)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btSureAction.setOnClickListener {
            if (!binding.checkBox.isChecked) {
                ToastUtils.showShort("请先阅读并同意注意事项")
            }else{
                dismiss()
                onDeleteCallback.invoke()
            }
        }
        binding.btCancelAction.setOnClickListener {
            dismiss()
        }
    }
}