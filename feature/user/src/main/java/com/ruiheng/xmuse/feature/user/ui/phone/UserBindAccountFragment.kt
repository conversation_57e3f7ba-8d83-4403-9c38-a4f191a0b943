package com.ruiheng.xmuse.feature.user.ui.phone

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserBindAccountBinding

class UserBindAccountFragment : BaseBindPhoneFragment<FragmentUserBindAccountBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserBindAccountBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btnNext.setOnClickListener(this)
        binding.btnEmailNext.setOnClickListener(this)
        binding.imgLoginBg.showImage(R.drawable.img_login_top_bg)
        binding.tvChangeAccount.setOnClickListener(this)

        val inputContainer =
            arrayOf(binding.layoutPhoneNumberContainer, binding.inputLayoutAccount)
        val nextBtn = arrayOf(binding.btnNext, binding.btnEmailNext)
        val tabTitle: Array<String> = arrayOf("绑定手机号", "绑定邮箱")
        initLoginTab(
            binding.tabLayoutLogin,
            tabTitle = tabTitle,
            tabImageBg = binding.tabBg,
            onSelectedCallback = { position ->
                binding.tvLoginTitle.text = tabTitle[position]
                inputContainer.forEachIndexed { index, viewGroup ->
                    viewGroup.visibility = if (index == position) View.VISIBLE else View.GONE
                }
                nextBtn.forEachIndexed { index, materialButton ->
                    materialButton.visibility = if (index == position) View.VISIBLE else View.GONE
                }
            })
    }

    private fun startSendSMS() {
        val phone = binding.editBindPhoneNumber.text?.toString() ?: return
        autoRemoveFlow(userViewModel.startSendSMS(phone)) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result is Result.Success) {
                userViewModel.startSMSCodePage(phone = phone)

                startNavPage(R.id.actionBindPhoneToSmsVerifyPage)
            }
        }
    }

    private fun startSendEmailSMS() {
        val email = binding.editAccount.text?.toString() ?: return
        autoRemoveFlow(userViewModel.startSendEmailSMS(email)) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result is Result.Success) {
                userViewModel.startEmailSMSCodePage(email = email)
                startNavPage(R.id.actionToEmailSmsVerifyPage)
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.tvChangeAccount -> {
                autoRemoveFlow(userViewModel.quitUser()) { result ->
                    if (result.isSuccessWithData()) {
                        requireActivity().finish()
                    }
                }
            }

            binding.btnEmailNext -> {
                startSendEmailSMS()
            }

            binding.btnNext -> {
                startSendSMS()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }

    override fun bindOnBackCallback(): ((Boolean) -> Boolean)? = {
        true
    }

}