package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserEmailModifyConflictBinding

class UserEmailModifyConflictFragment : BaseUserFragment<FragmentUserEmailModifyConflictBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserEmailModifyConflictBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.email_modify_conflict

    private fun RequestUserResult.bindAccountSource() =
        when (source) {
            0 -> {
                "Web"
            }

            1 -> {
                "App"
            }

            else -> {
                "问卷系统"
            }
        }

    private fun RequestUserResult.getConflictAccountInfo() =
        "账号注册来源：${bindAccountSource()}\n当前绑定邮箱：${email}\n上次登录时间：${lastLoginTime}"

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.layoutForceBindPhone.setOnClickListener(this)
        binding.layoutChangePhone.setOnClickListener(this)
        autoStoppedFlow(userViewModel.userConflictDataStateFlow) { result ->
            val currentUser = result?.curAccount?.get(0)
            binding.tvCurrentAccount.text = currentUser?.nickName
            binding.imgCurrentAccountAvatar.showImage(
                currentUser?.headPortrait,
                placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default
            )
            binding.tvCurrentAccountContent.text = currentUser?.getConflictAccountInfo()

            val conflictUser = result?.targetAccount?.get(0)
            binding.tvConflictAccount.text = conflictUser?.nickName
            binding.imgConflictAccountAvatar.showImage(
                conflictUser?.headPortrait,
                placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default
            )
            binding.tvConflictAccountContent.text = conflictUser?.getConflictAccountInfo()

            binding.tvContent.text = getString(
                R.string.email_modify_conflict_content,
                conflictUser?.email ?: "",
                conflictUser?.nickName ?: ""
            )

            binding.tvForceBindPhoneTitle.text =
                getString(R.string.force_to_bind_email, conflictUser?.email ?: "")
            binding.tvForceBindPhoneContent.text =
                getString(R.string.logout_conflict_account, conflictUser?.nickName ?: "")
        }
    }

    private fun startForceUpdatePhone() {
        autoRemoveFlow(userViewModel.forceUpdateUserEmail()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                finishNavPage()
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutForceBindPhone -> {
                startForceUpdatePhone()
            }

            binding.layoutChangePhone -> {
                finishNavPage()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}