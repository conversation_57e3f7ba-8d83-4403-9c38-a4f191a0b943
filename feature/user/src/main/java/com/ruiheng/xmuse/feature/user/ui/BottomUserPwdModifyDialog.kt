package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.ui.helper.dialog.BindBottomSheetDialog
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserNameModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPwdModifyBinding
import okhttp3.internal.userAgent

class BottomUserPwdModifyDialog(
    private val userData: UserData?,
    private val onInputCallback: (String, String, String) -> Unit
) : BindBottomSheetDialog<DialogBottomUserPwdModifyBinding>() {

    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogBottomUserPwdModifyBinding.inflate(layoutInflater)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        if (userData?.judgeInputPassword == true) {
            binding.inputOldPwd.visibility = View.GONE
        } else {
            binding.inputOldPwd.visibility = View.VISIBLE
        }
        binding.btSureAction.setOnClickListener {
            val oldPwd = binding.editOldPwd.text.toString()
            val newPwd = binding.editPwd.text.toString()
            val againPwd = binding.editPwdAgain.text.toString()
            if (newPwd.isNotEmpty() && againPwd.isNotEmpty()) {
                dismiss()
                onInputCallback.invoke(oldPwd, newPwd, againPwd)
            }
//            if (input.isNotEmpty()) {
//                onInputCallback.invoke(input)
//                dismiss()
//            }
        }
    }
}