package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout
import com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout.OnInputListener
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPhoneModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPhoneSmsBinding

class BottomUserSMSDialog(
    private val phone: String,
    private val onInputCallback: (String) -> Unit
) : BaseBindingBottomDialogFragment<DialogBottomUserPhoneSmsBinding>() {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomUserPhoneSmsBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btSureAction.isEnabled = false
        var verifyCode: String = ""
        binding.layoutVerificationCode.setOnInputListener(object : OnInputListener {
            override fun onSuccess(code: String) {
                verifyCode = code
            }

            override fun onDataChange(code: String?, totalSize: Int) {
                binding.btSureAction.isEnabled = code?.length == totalSize
            }
        })
        binding.tvSubContent.text = phone
        binding.btSureAction.setOnClickListener {
            if (verifyCode.isNotEmpty()) {
                dismiss()
                onInputCallback.invoke(verifyCode)
            }
        }
    }
}