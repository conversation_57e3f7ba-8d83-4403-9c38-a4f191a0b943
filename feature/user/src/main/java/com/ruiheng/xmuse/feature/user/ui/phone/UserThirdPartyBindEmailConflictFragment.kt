package com.ruiheng.xmuse.feature.user.ui.phone

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserBindEmailConflictBinding

class UserThirdPartyBindEmailConflictFragment :
    BaseBindPhoneFragment<FragmentUserBindEmailConflictBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserBindEmailConflictBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.email_bind_conflict

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.layoutForceBindPhone.setOnClickListener(this)
        binding.layoutChangePhone.setOnClickListener(this)
        autoStoppedFlow(userViewModel.userConflictDataStateFlow) { result ->
            val conflictUser = result?.targetAccount?.get(0)
            binding.tvContent.text = getString(
                R.string.email_bind_thirdparty_conflict_content,
                conflictUser?.email ?: "",
                conflictUser?.nickName ?: ""
            )
        }
    }

    private fun startForceUpdatePhone() {
        autoRemoveFlow(userViewModel.forceUpdateUserEmail()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                finishNavPage()
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutForceBindPhone -> {
                startForceUpdatePhone()
            }

            binding.layoutChangePhone -> {
                finishNavPage()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}