package com.ruiheng.xmuse.feature.user.ui

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.login.LoginActivity
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserListBinding

class UserSelectFragment : BaseUserFragment<FragmentUserListBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserListBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.account_change

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        autoStoppedFlow(userViewModel.userListDataStateFlow) { result ->
            result.controlLoading(this)
            initUserInfo(result.data)
//            if (result.isSuccessWithData()) {
//                val userList = result.data!!
//                val currentUser = userList.find { it. }
//            }
        }
        binding.layoutCacheUser.setOnClickListener(this)
        binding.layoutAddUser.setOnClickListener(this)
    }

    private fun initUserInfo(userList: List<UserData>?) {
        if (userList.isNullOrEmpty()) {
            binding.layoutCurrentUser.visibility = View.GONE
            binding.layoutCacheUser.visibility = View.GONE
            binding.layoutAddUser.visibility = View.GONE
        } else {
            val currentUser = userList.find { it.selected }
            if (currentUser != null) {
                binding.layoutCurrentUser.visibility = View.VISIBLE
            } else {
                binding.layoutCurrentUser.visibility = View.GONE
            }
            binding.imgCurrentUser.showImage(
                currentUser?.headPortrait,
                placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default,
                isCircleCrop = true
            )
            binding.tvCurrentUser.text = currentUser?.nickName ?: ""

            val cacheUser = userList.find { !it.selected }
            if (cacheUser == null) {
                binding.layoutCacheUser.visibility = View.GONE
                binding.layoutAddUser.visibility = View.VISIBLE
            } else {
                binding.layoutCacheUser.visibility = View.VISIBLE
                binding.layoutAddUser.visibility = View.GONE
                binding.imgCacheUser.showImage(
                    cacheUser.headPortrait,
                    placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default,
                    isCircleCrop = true
                )
                binding.tvCacheUser.text = cacheUser.nickName
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutAddUser -> {
                val loginIntent = Intent(requireContext(), LoginActivity::class.java)
                startActivity(loginIntent)
            }

            binding.layoutCacheUser -> {
                val cacheUser =
                    userViewModel.userListDataStateFlow.value.data?.find { !it.selected } ?: return
                val token = cacheUser.token
                if (token.isNullOrEmpty()) {

                } else {
                    autoRemoveFlow(userViewModel.selectUser(cacheUser.uid, token)) { result ->
                        result.controlLoading(this)
                        if (result.isSuccessWithData()) {
                            finishNavPage()
                        }
                    }
                }
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}