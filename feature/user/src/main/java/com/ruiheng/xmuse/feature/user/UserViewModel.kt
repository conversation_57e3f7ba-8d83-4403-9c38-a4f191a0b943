package com.ruiheng.xmuse.feature.user

import android.view.View
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_EMAIL_SMS_TYPE_DELETE_USER
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_EMAIL_SMS_TYPE_RESET
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_SMS_TYPE_DELETE_USER
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository.Companion.SEND_SMS_TYPE_RESET_PHONE
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.model.data.UserThirdParty
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.ui.custom.PublicMenuImp
import com.ruiheng.xmuse.core.ui.px
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import javax.inject.Inject

@HiltViewModel
class UserViewModel @Inject constructor(
    private val userDataRepository: UserDataRepository,
    private val netResourcesUserRepository: NetResourcesUserRepository
) : ViewModel() {

    private val _userDataStateFlow = MutableStateFlow<Result<UserData>>(Result.Loading())
    val userDataStateFlow: StateFlow<Result<UserData>> = _userDataStateFlow

    private val _userListDataStateFlow = MutableStateFlow<Result<List<UserData>>>(Result.Loading())
    val userListDataStateFlow: StateFlow<Result<List<UserData>>> = _userListDataStateFlow

    init {
        viewModelScope.launch {
            userDataRepository.userData.collect { user ->
                if (user != null) {
                    _userDataStateFlow.value = Result.Success(user)
                } else {
                    _userDataStateFlow.value = Result.Error(Exception())
                }
            }
        }
    }

    init {
        viewModelScope.launch {
            userDataRepository.userListData.collect { userList ->
                if (!userList.isNullOrEmpty()) {
                    _userListDataStateFlow.value = Result.Success(userList)
                } else {
                    _userListDataStateFlow.value = Result.Error(Exception())
                }
            }
        }
    }

    /**
     * 设置当前需要验证的手机号码
     */
    private val _verifyPageTipMutableLiveData = MutableStateFlow<String>("")
    val verifyPageTipMutableLiveData = _verifyPageTipMutableLiveData as StateFlow<String>
    private var currentVerifyPhone: Pair<String, String>? = null
    private var currentVerifyEmail: String? = null
    fun startSMSCodePage(zoneCode: String = "+86", phone: String) {
        currentVerifyPhone = Pair(zoneCode, phone)
        _verifyPageTipMutableLiveData.value =
            "短信已发送至${currentVerifyPhone!!.first} ${currentVerifyPhone!!.second}"
    }

    fun startEmailSMSCodePage(email: String) {
        currentVerifyEmail = email
        _verifyPageTipMutableLiveData.value =
            "验证码已发送至:${email}"
    }

    /**
     * 绑定手机号码验证码请求
     */
    fun startSendSMS(phone: String) =
        netResourcesUserRepository.sendSMS(phone, SEND_SMS_TYPE_RESET_PHONE).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading(),
        )

    /**
     * 删除账号请求短信验证码
     */
    fun startSendDeleteUserSMS(phone: String) =
        netResourcesUserRepository.sendSMS(phone, SEND_SMS_TYPE_DELETE_USER).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading(),
        )

    /**
     * 绑定邮箱验证码请求
     */
    fun startSendEmailSMS(email: String) =
        netResourcesUserRepository.sendEmailSMS(email, SEND_EMAIL_SMS_TYPE_RESET).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading(),
        )

    /**
     * 删除账号邮箱验证码
     */
    fun startSendDeleteUserEmailSMS(email: String) =
        netResourcesUserRepository.sendEmailSMS(email, SEND_EMAIL_SMS_TYPE_DELETE_USER).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading(),
        )

//    fun bindPhone(smsCode: String): StateFlow<Result<RequestBindPhoneResult>>? {
//        if (currentVerifyPhone == null) return null
//        return netResourcesUserRepository.bindPhone(currentVerifyPhone!!.second, smsCode).stateIn(
//            scope = viewModelScope,
//            started = SharingStarted.WhileSubscribed(),
//            initialValue = Result.Loading(),
//        )
//    }

    /**
     * 更新用户手机号码请求
     */
    fun forceUpdateUserPhone(): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyPhone == null) return null
        val currentUser = userDataStateFlow.value.data ?: return null
        val code = userConflictDataStateFlow.value?.switchBindCode ?: return null
        return netResourcesUserRepository.forceUpdatePhone(
            currentUser = currentUser,
            code
        ).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    /**
     * 强制换绑邮箱
     */
    fun forceUpdateUserEmail(): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyEmail == null) return null
        val currentUser = userDataStateFlow.value.data ?: return null
        val code = userConflictDataStateFlow.value?.switchBindCode ?: return null
        return netResourcesUserRepository.forceUpdateEmail(
            currentUser = currentUser,
            code
        ).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    /**
     * 换绑手机号
     */
    fun updateUserPhone(smsCode: String): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyPhone == null) return null
        val currentUser = userDataStateFlow.value.data ?: return null
        return netResourcesUserRepository.updateUserPhone(
            currentUser = currentUser,
            currentVerifyPhone!!.second,
            smsCode
        ).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    fun updateUserEmail(smsCode: String): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyEmail == null) return null
        val currentUser = userDataStateFlow.value.data ?: return null
        return netResourcesUserRepository.updateUserEmail(
            currentUser = currentUser,
            currentVerifyEmail!!,
            smsCode
        ).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    /**
     * 解决更改手机号冲突的Result
     */
    private val _userConflictDataStateFlow = MutableStateFlow<RequestBindPhoneResult?>(null)
    val userConflictDataStateFlow: StateFlow<RequestBindPhoneResult?> = _userConflictDataStateFlow
    fun startModifyPhoneConflictFragment(result: RequestBindPhoneResult?) {
        _userConflictDataStateFlow.value = result
    }

    fun startModifyEmailConflictFragment(result: RequestBindPhoneResult?) {
        _userConflictDataStateFlow.value = result
    }

    private val _thirdPartyConflictDataStateFlow =
        MutableStateFlow<RequestBindThirdPartyResult?>(null)
    val thirdPartyConflictDataStateFlow: StateFlow<RequestBindThirdPartyResult?> =
        _thirdPartyConflictDataStateFlow

    fun startBindThirdPartyConflictFragment(result: RequestBindThirdPartyResult?) {
        _thirdPartyConflictDataStateFlow.value = result
    }

    /**
     * 绑定手机号
     */
    fun bindPhone(smsCode: String): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyPhone == null) return null
        return netResourcesUserRepository.bindPhone(currentVerifyPhone!!.second, smsCode).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    /**
     * 绑定邮箱
     */
    fun bindEmail(smsCode: String): StateFlow<Result<RequestBindPhoneResult>>? {
        if (currentVerifyEmail == null) return null
        return netResourcesUserRepository.bindEmail(currentVerifyEmail!!, smsCode).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading(),
        )
    }

    val thirdPartyStateFlow = userDataRepository.thirdPartyBondList.map { thirdPartyList ->
        thirdPartyList.map { it.asMenuItem() }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = listOf()
    )

    val userMangerInfoListStateFlow: StateFlow<List<UserMangerInfoMenuItem>> =
        userDataRepository.userData.map { user ->

            val genderFemaleText = Utils.getApp().getString(R.string.gender_female)
            val genderMaleText = Utils.getApp().getString(R.string.gender_man)
            val genderDisplay =
                if (user?.gender == null) Utils.getApp()
                    .getString(R.string.not_set) else if (user.gender == 0) genderFemaleText else genderMaleText
            val displayBirthdayYear =
                if (user?.birthday.isNullOrEmpty()) Utils.getApp()
                    .getString(R.string.not_set) else "${user!!.parseBirthYear()}"

            val userInfoList = listOf(
                UserMangerInfoMenuItem(R.string.change_login_account, null),
                UserMangerInfoMenuItem(R.string.nickname, user?.nickName),
                UserMangerInfoMenuItem(R.string.gender, genderDisplay),
                UserMangerInfoMenuItem(
                    R.string.modify_avatar,
                    null,
                    contentImage = user?.headPortrait
                ),
                UserMangerInfoMenuItem(R.string.birthday_year, displayBirthdayYear),
            )
            userInfoList
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = listOf()
        )
    val userRelateListStateFlow: StateFlow<List<UserMangerInfoMenuItem>> =
        userDataRepository.userData.map { user ->
            val userInfoList = listOf(
                UserMangerInfoMenuItem(R.string.bond_phone, user?.bindDisplayPhoneNum() ?: ""),
                UserMangerInfoMenuItem(R.string.bond_email, user?.email ?: ""),
                UserMangerInfoMenuItem(R.string.bond_third_account, null),
            )
            userInfoList
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = listOf()
        )

    val userSafeListStateFlow: StateFlow<List<UserMangerInfoMenuItem>> =
        userDataRepository.userData.map { user ->
            val userInfoList = listOf(
                UserMangerInfoMenuItem(R.string.modify_account_password, null),
                UserMangerInfoMenuItem(R.string.account_termination, content = null)
            )
            userInfoList
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = listOf()
        )

    fun uploadImageFile(file: String) = netResourcesUserRepository.uploadImageFile(file).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = Result.Loading()
    )

    fun updateUserInfo(
        nickName: String? = null, headPortrait: String? = null, gender: Int? = null,
        birthdayYear: Int? = null
    ) =
        netResourcesUserRepository.updateUserInfo(nickName, headPortrait, gender, birthdayYear)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(),
                initialValue = Result.Loading()
            )

    fun loadThirdPartyList() = netResourcesUserRepository.loadThirdPartyList().stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = Result.Loading()
    )

    fun updateUserPassword(oldPassword: String, newPassword: String, againPassword: String) =
        netResourcesUserRepository.updatePassword(oldPassword, newPassword, againPassword).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )

    fun configurePassword(
        password: String,
        againPassword: String
    ): StateFlow<Result<Boolean>> {
        return netResourcesUserRepository.configurePassword(password, againPassword).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Result.Loading(),
        )
    }

    /**
     * 绑定第三方账号
     */
    fun bindThirdParty(code: String, thirdType: Int) =
        netResourcesUserRepository.bindThirdParty(code, thirdType).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )

    fun forceBindThirdParty(): StateFlow<Result<List<UserThirdParty>>>? {
        val code = thirdPartyConflictDataStateFlow.value?.thirdBindCode ?: return null
        val thirdType = thirdPartyConflictDataStateFlow.value?.thirdPartyType ?: return null
        return netResourcesUserRepository.forceUpdateThirdParty(code, thirdType).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )
    }

    fun startWxAuthor() = netResourcesUserRepository.startWxAuthor()

    /**
     * 解除第三方关联关系
     */
    fun unbindThirdParty(unionId: String, thirdType: Int) =
        netResourcesUserRepository.unbindThirdPartyList(unionId, thirdType).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )

    fun selectUser(uid: String, token: String) =
        netResourcesUserRepository.selectUser(uid, token).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )

    fun quitUser() = netResourcesUserRepository.userLogout().stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = Result.Loading()
    )

    fun deleteUser(codeType: Int, smsCode: String) =
        netResourcesUserRepository.userDelete(codeType, smsCode).stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(),
            initialValue = Result.Loading()
        )
}

data class UserMangerInfoMenuItem(
    @StringRes val title: Int,
    val content: String?,
    val showArrow: Int = View.VISIBLE,
    val contentImage: Any? = null
) : PublicMenuImp {
    override fun bindTitle() = title
    override fun bindContent() = content
    override fun bindArrowShow(): Int = showArrow
    override fun bindBgResources() = android.R.color.transparent
    override fun bindContentImage() = contentImage

}

fun UserThirdParty.asMenuItem(): UserThirdPartyMenuItem {
    var title: Int = com.ruiheng.xmuse.core.ui.R.string.Wechat
    var imgRes: Int = com.ruiheng.xmuse.core.ui.R.drawable.ico_third_party_wechat
    when (thirdType) {
        UserDataRepository.ThirdPartyLoginType.QQ.requestType -> {
            title = com.ruiheng.xmuse.core.ui.R.string.QQ
            imgRes = com.ruiheng.xmuse.core.ui.R.drawable.ico_thid_party_qq
        }

        UserDataRepository.ThirdPartyLoginType.Sina.requestType -> {
            title = com.ruiheng.xmuse.core.ui.R.string.Sina
            imgRes = com.ruiheng.xmuse.core.ui.R.drawable.ico_third_party_sina
        }
    }
    val name = if (nickName.isNullOrEmpty()) "未绑定" else nickName
    return UserThirdPartyMenuItem(requestKey = unionId, thirdType = thirdType, title, imgRes, name)
}

data class UserThirdPartyMenuItem(
    val requestKey: String?,
    val thirdType: Int?,
    @StringRes val title: Int,
    @DrawableRes val image: Int,
    val content: String?,
    val showArrow: Int = View.VISIBLE
) : PublicMenuImp {
    override fun bindImage() = image
    override fun bindImageWidth() = (24.px).toInt()
    override fun bindImageHeight() = (24.px).toInt()
    override fun bindTitle() = title
    override fun bindContent() = content
    override fun bindArrowShow(): Int = showArrow
    override fun bindBgResources() = android.R.color.transparent
}

//fun RequestUserBondThird.asMenuItem() = UserThirdPartyMenuItem(
//
//)