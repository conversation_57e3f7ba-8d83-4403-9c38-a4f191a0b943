package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.network.model.user.RequestUploadFile
import com.ruiheng.xmuse.core.ui.bottommenu.BottomPhotoDialog
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.UserMangerInfoMenuItem
import com.ruiheng.xmuse.feature.user.databinding.FragmentUserBinding

class UserHomeFragment : BaseUserFragment<FragmentUserBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentUserBinding.inflate(inflater, container, false)

    override fun bindTitleRes() = R.string.account_setting

    private var infoAdapter: PublicNormalMenuRVAdapter<UserMangerInfoMenuItem> by autoCleared()
    private var relateAdapter: PublicNormalMenuRVAdapter<UserMangerInfoMenuItem> by autoCleared()
    private var userSafeAdapter: PublicNormalMenuRVAdapter<UserMangerInfoMenuItem> by autoCleared()

    private val bottomPhotoDialog by lazy {
        val title =
            getString(com.ruiheng.xmuse.core.ui.R.string.choose_photo_type)
        BottomPhotoDialog(title, onPhotoPathCallback)
    }
    private val onPhotoPathCallback: (String) -> Unit = { path ->
        autoRemoveFlow(userViewModel.uploadImageFile(path)) { result ->
            result.showResult()
            if (result.isLoading()) {
                result.controlLoading(this)
            }
            if (result.isError()) {
                result.controlLoading(this)
            }
            if (result.isSuccessWithData()) {
                updateUserAvatar(result.data!!)
            }
        }
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btnQuitUser.setOnClickListener(this)
        infoAdapter = PublicNormalMenuRVAdapter { menu ->
            when (menu.title) {
                R.string.change_login_account -> {
                    startNavPage(R.id.actionHomeToChangeUserPage)
                }

                R.string.modify_avatar -> {
                    bottomPhotoDialog.startShow(childFragmentManager)
                }

                R.string.gender -> {
                    showModifyGenderDialog()
                }

                R.string.birthday_year -> {
                    showModifyBirthdayYearDialog()
                }

                R.string.nickname -> {
                    showModifyNameDialog()
                }
            }
        }
        relateAdapter = PublicNormalMenuRVAdapter { menu ->
            when (menu.title) {
                R.string.bond_third_account -> {
                    startNavPage(R.id.actionHomeToThirdPartyPage)
                }

                R.string.bond_email -> {
                    showModifyEmailDialog()
                }

                R.string.bond_phone -> {
                    showModifyPhoneDialog()
                }
            }
        }
        userSafeAdapter = PublicNormalMenuRVAdapter { menu ->
            when (menu.title) {
                R.string.modify_account_password -> {
                    val currentUser = userViewModel.userDataStateFlow.value.data
                    BottomUserPwdModifyDialog(currentUser) { old, new, again ->
                        updatePwd(old, new, again)
                    }.startShow(childFragmentManager)
                }

                R.string.account_termination -> {
                    BottomUserDeleteDialog {
                        startSendAccountTerminationSMS()
                    }.startShow(childFragmentManager)
                }
            }
        }

        binding.rvAccountSafe.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvAccountSafe.adapter = userSafeAdapter
        binding.rvAccountSafe.isNestedScrollingEnabled = false

//        binding.rvAccountManager.addItemDecoration(PublicOptionNormalTextItemDecoration())
        autoStoppedFlow(userViewModel.userSafeListStateFlow) {
            userSafeAdapter.submitList(it)
        }

        binding.rvAccountManager.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvAccountManager.adapter = infoAdapter

//        binding.rvAccountManager.addItemDecoration(PublicOptionNormalTextItemDecoration())
        autoStoppedFlow(userViewModel.userMangerInfoListStateFlow) {
            infoAdapter.submitList(it)
        }

        binding.rvAccountRelate.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvAccountRelate.adapter = relateAdapter
        autoStoppedFlow(userViewModel.userRelateListStateFlow) {
            relateAdapter.submitList(it)
        }
        binding.rvAccountRelate.isNestedScrollingEnabled = false
        binding.rvAccountManager.isNestedScrollingEnabled = false
    }

    private fun updateUserAvatar(imageData: RequestUploadFile) {
        autoRemoveFlow(userViewModel.updateUserInfo(headPortrait = imageData.fileUrl)) { result ->
            result.showResult()
            if (!result.isLoading()) {
                result.controlLoading(this)
            }
        }
    }

    private fun showModifyGenderDialog(){
        val currentUser = userViewModel.userDataStateFlow.value.data ?: return
        BottomUserGenderModifyDialog(currentUser) { gender ->
            updateGender(gender)
        }.showDialog(childFragmentManager)
    }

    private fun showModifyNameDialog() {
        val currentUserName = userViewModel.userDataStateFlow.value.data?.nickName ?: ""
        BottomUserNameModifyDialog(currentUserName) { name ->
            updateUserName(name)
        }.startShow(childFragmentManager)
    }

    private fun showModifyBirthdayYearDialog() {
        val currentUser = userViewModel.userDataStateFlow.value.data ?: return
        BottomUserBirthdayModifyDialog(currentUser) { birthdayYear ->
            updateBirthDayYear(birthdayYear)
        }.showDialog(childFragmentManager)
    }

    private fun updateUserName(name: String) {
        autoRemoveFlow(userViewModel.updateUserInfo(nickName = name)) { result ->
            result.showResult()
            if (!result.isLoading()) {
                result.controlLoading(this)
            }
        }
    }

    private fun updateBirthDayYear(birthdayYear: Int) {
        autoRemoveFlow(userViewModel.updateUserInfo(birthdayYear = birthdayYear)) { result ->
            result.controlLoading(this)
            result.showResult()
        }
    }

    private fun updateGender(gender: Int) {
        autoRemoveFlow(userViewModel.updateUserInfo(gender = gender)) { result ->
            result.controlLoading(this)
            result.showResult()
        }
    }

    private fun updatePwd(oldPwd: String, newPwd: String, againPwd: String) {
        val user = userViewModel.userDataStateFlow.value.data
        if (user?.judgeInputPassword == true) {
            autoRemoveFlow(userViewModel.configurePassword(newPwd, againPwd)) { result ->
                result.controlLoading(this)
                result.showResult()
            }
        } else {
            autoRemoveFlow(userViewModel.updateUserPassword(oldPwd, newPwd, againPwd)) { result ->
                result.controlLoading(this)
                result.showResult()
            }
        }
    }

    private fun showModifyPhoneDialog() {
        val user = userViewModel.userDataStateFlow.value.data ?: return
        fun showBottomSmsVerifyDialog() {
            fun startVerify(code: String) {
                autoRemoveFlow(userViewModel.updateUserPhone(code)) { result ->
                    result.controlLoading(this)
                    result.showResult()
                    if (result.isError() && result.data != null) {
                        userViewModel.startModifyPhoneConflictFragment(result.data)
                        startNavPage(R.id.actionHomeToUpdatePhoneConflictPage)
                    }
                }
            }

            val phone = userViewModel.verifyPageTipMutableLiveData.value
            BottomUserSMSDialog(phone) { code ->
                startVerify(code)
            }.showDialog(childFragmentManager)
        }
        BottomUserPhoneModifyDialog(user) { phone ->
            autoRemoveFlow(userViewModel.startSendSMS(phone)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result.isSuccessWithData()) {
                    userViewModel.startSMSCodePage(phone = phone)
                    showBottomSmsVerifyDialog()
                }
            }
        }.showDialog(childFragmentManager)
    }

    private fun showModifyEmailDialog() {
        val user = userViewModel.userDataStateFlow.value.data ?: return
        fun showBottomSmsVerifyDialog() {
            fun startVerify(code: String) {
                autoRemoveFlow(userViewModel.updateUserEmail(code)) { result ->
                    result.controlLoading(this)
                    result.showResult()
                    if (result.isError() && result.data != null) {
                        userViewModel.startModifyEmailConflictFragment(result.data)
                        startNavPage(R.id.actionHomeToUpdateEmailConflictPage)
                    }
                }
            }

            val tip = userViewModel.verifyPageTipMutableLiveData.value
            BottomUserEmailSMSDialog(tip) { code ->
                startVerify(code)
            }.showDialog(childFragmentManager)
        }
        BottomUserEmailModifyDialog(user) { email ->
            autoRemoveFlow(userViewModel.startSendEmailSMS(email)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    userViewModel.startEmailSMSCodePage(email = email)
                    showBottomSmsVerifyDialog()
                }
            }
        }.showDialog(childFragmentManager)
    }

    private fun startSendAccountTerminationSMS() {
        fun startSendSMS(phone: String) {
            autoRemoveFlow(userViewModel.startSendDeleteUserSMS(phone)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    userViewModel.startSMSCodePage(phone = phone)
                    BottomUserSMSDialog(phone) { code ->
                        startSMSDeleteUser(1, code)
                    }.showDialog(childFragmentManager)
                }
            }
        }

        fun startEmailSMS(email: String) {
            autoRemoveFlow(userViewModel.startSendDeleteUserEmailSMS(email)) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result is Result.Success) {
                    userViewModel.startEmailSMSCodePage(email = email)
                    val tip = userViewModel.verifyPageTipMutableLiveData.value
                    BottomUserEmailSMSDialog(tip) { code ->
                        startSMSDeleteUser(2, code)
                    }.showDialog(childFragmentManager)
                }
            }
        }

        val userInfo = userViewModel.userDataStateFlow.value.data ?: return
        if (!userInfo.phone.isNullOrEmpty()) {
            startSendSMS(userInfo.phone!!)
        } else if (!userInfo.email.isNullOrEmpty()) {
            startEmailSMS(userInfo.email!!)
        }
    }

    private fun startSMSDeleteUser(codeType: Int, smsCode: String) {
        autoRemoveFlow(userViewModel.deleteUser(codeType, smsCode)) { result ->
            result.controlLoading(this)
            result.showResult()
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btnQuitUser -> {
                autoRemoveFlow(userViewModel.quitUser()) { result ->
                    result.controlLoading(this)
                    if (!result.isLoading()) {
                        requireActivity().finish()
                    }
                }
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}