package com.ruiheng.xmuse.feature.user.ui.phone

import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.viewbinding.ViewBinding
import com.google.android.material.tabs.TabLayout
import com.ruiheng.xmuse.core.ui.helper.isNightMode
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseNavFragment
import com.ruiheng.xmuse.feature.user.UserViewModel

abstract class BaseBindPhoneFragment<T : ViewBinding> : BaseNavFragment<T>() {

    protected val userViewModel: UserViewModel by lazy {
        (requireActivity() as BindAccountActivity).userViewModel
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
    }

    private val imageTableBg by lazy {
        arrayOf(
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top1,
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top2
        )
    }

    private val imageTableDarkBg by lazy {
        arrayOf(
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top_dark_01,
            com.ruiheng.xmuse.core.ui.R.drawable.img_login_top_dark02
        )
    }

    fun initLoginTab(
        tabLayout: TabLayout,
        tabTitle: Array<String> = arrayOf("绑定手机号", "绑定邮箱"),
        tabImageBg: AppCompatImageView?,
        onSelectedCallback: (Int) -> Unit
    ) {
        tabTitle.forEachIndexed { index, s ->
            tabLayout.getTabAt(index)?.text = s
        }
        fun updateTab() {
            val position = tabLayout.selectedTabPosition
            val imageBgList = if (requireContext().isNightMode()) imageTableDarkBg else imageTableBg
            tabImageBg?.showImage(imageBgList[position])
            onSelectedCallback.invoke(position)
        }
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTab()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })
        updateTab()
    }
}