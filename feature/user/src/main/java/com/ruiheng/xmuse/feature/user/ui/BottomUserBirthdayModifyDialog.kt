package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.feature.user.R
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserBirthdayModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserEmailModifyBinding
import com.ruiheng.xmuse.feature.user.databinding.DialogBottomUserPhoneModifyBinding
import java.util.Calendar

class BottomUserBirthdayModifyDialog(
    private val user: UserData,
    private val onInputCallback: (Int) -> Unit
) : BaseBindingBottomDialogFragment<DialogBottomUserBirthdayModifyBinding>() {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomUserBirthdayModifyBinding.inflate(inflater, container, false)

    private val calendar by lazy { Calendar.getInstance() }
    private val currentYear by lazy {
        calendar.get(Calendar.YEAR) + 1
    }

    private val optionYearCount = 100
    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        val yearData = Array(optionYearCount) { index ->
            "${currentYear - optionYearCount + index}年"
        }
        val userBirthdayYear = user.parseBirthYear()
        val selectedIndex = if (userBirthdayYear != -1) {
            optionYearCount - (currentYear - userBirthdayYear) + 1
        } else {
            optionYearCount
        }
        binding.pickerViewYear.minValue = 1
        binding.pickerViewYear.maxValue = yearData.size
        binding.pickerViewYear.displayedValues = yearData
        binding.pickerViewYear.value = selectedIndex
        binding.pickerViewYear.setOnValueChangedListener { picker, oldVal, newVal ->
        }

        binding.btSureAction.setOnClickListener(this)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btSureAction -> {
                val birthdayYear = currentYear - (optionYearCount - binding.pickerViewYear.value) - 1
                onInputCallback.invoke(birthdayYear)
                dismiss()
            }
        }
        super.onWidgetClick(view)
    }
}