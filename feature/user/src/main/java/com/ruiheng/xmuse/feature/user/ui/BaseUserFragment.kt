package com.ruiheng.xmuse.feature.user.ui

import android.os.Bundle
import android.view.View
import androidx.viewbinding.ViewBinding
import com.ruiheng.xmuse.core.ui.ui.BaseNavFragment
import com.ruiheng.xmuse.feature.user.UserActivity
import com.ruiheng.xmuse.feature.user.UserViewModel

abstract class BaseUserFragment<T : ViewBinding> : BaseNavFragment<T>() {

    protected val userViewModel: UserViewModel by lazy {
        (requireActivity() as UserActivity).userViewModel
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
    }

}