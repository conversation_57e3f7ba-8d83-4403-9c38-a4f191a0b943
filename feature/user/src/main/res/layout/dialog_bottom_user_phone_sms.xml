<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/half_normal_margin"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space24"
        android:paddingEnd="@dimen/space24"
        android:paddingBottom="@dimen/space36"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/modify_phone"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_sub_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/half_normal_margin"
            android:textSize="@dimen/font_small"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="验证码已发送至：18149561495" />

        <com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout
            android:id="@+id/layout_verification_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:code_number="4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_sub_content" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_sure_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:text="@string/Sure"
            android:textColor="?colorOnPrimary"
            android:textSize="@dimen/font_medium"
            app:backgroundTint="?colorPrimary"
            app:cornerRadius="@dimen/space12"
            app:layout_constraintDimensionRatio="6.2:1"
            app:layout_constraintTop_toBottomOf="@id/layout_verification_code" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_bottom_cancel"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/half_normal_margin"
            android:text="@string/Cancel"
            android:textColor="?colorAccent"
            android:textSize="16sp"
            android:visibility="gone"
            app:backgroundTint="@android:color/transparent"
            app:layout_constraintDimensionRatio="6.5:1"
            app:layout_constraintTop_toBottomOf="@id/bt_sure_action" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>