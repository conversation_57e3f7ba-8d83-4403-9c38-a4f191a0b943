<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurfaceDim">


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/activity_vertical_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/font_normal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="邮箱****************已经绑定了账号：麻薯芋子，一个邮箱只能绑定一个账号。" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_conflict_account_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:text="@string/conflicting_accounts"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_medium"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_current_account" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_conflict_account"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/tv_conflict_account_title">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/img_conflict_account_avatar"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/ico_avatar_default" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_conflict_account"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/half_normal_margin"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_medium"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@id/img_conflict_account_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/img_conflict_account_avatar"
                        app:layout_constraintTop_toTopOf="@id/img_conflict_account_avatar"
                        tools:text="麻薯" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_conflict_account_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/activity_vertical_margin"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintTop_toBottomOf="@id/img_conflict_account_avatar"
                        tools:text="账号注册方式：通过手机号注册\n当前绑定手机：185****0516\n上次登录时间：2025年02月11日" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_current_account_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:text="@string/current_account"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_medium"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_content" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_current_account"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/tv_current_account_title">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/img_current_account_avatar"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/ico_avatar_default" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_current_account"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/half_normal_margin"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_medium"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="@id/img_current_account_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/img_current_account_avatar"
                        app:layout_constraintTop_toTopOf="@id/img_current_account_avatar"
                        tools:text="麻薯" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_current_account_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/activity_vertical_margin"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintTop_toBottomOf="@id/img_current_account_avatar"
                        tools:text="账号注册方式：通过手机号注册\n当前绑定手机：185****0516\n上次登录时间：2025年02月11日" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_action_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space24"
                android:text="@string/how_change_bind_account"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_medium"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_conflict_account" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_change_phone"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/tv_action_title">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_change_phone_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/bind_with_another_email_address"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_normal"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/half_normal_margin"
                        android:text="@string/both_accounts_can_be_retained"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_change_phone_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_force_bind_phone"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/layout_change_phone">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_force_bind_phone_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_normal"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="继续用****************绑定" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_force_bind_phone_content"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/half_normal_margin"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_force_bind_phone_title"
                        tools:text="需注销账号“麻薯芋子”" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>