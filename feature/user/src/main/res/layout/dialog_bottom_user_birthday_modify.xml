<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space24"
        android:paddingEnd="@dimen/space24"
        android:paddingBottom="@dimen/space36"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <com.shawnlin.numberpicker.NumberPicker
            android:id="@+id/picker_view_year"
            style="@style/PetivityPickerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_weight="3"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_sure_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:text="@string/Sure"
            android:textColor="?colorOnPrimary"
            android:textSize="@dimen/font_medium"
            app:backgroundTint="?colorPrimary"
            app:cornerRadius="@dimen/space12"
            app:layout_constraintDimensionRatio="6.2:1"
            app:layout_constraintTop_toBottomOf="@id/picker_view_year" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>