<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/activity_vertical_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_account_manager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tap_the_avatar_to_switch_account_login"
            android:textSize="@dimen/font_normal"
            android:textColor="?colorOnSurface"
            app:layout_constraintBottom_toTopOf="@id/layout_current_user"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_current_user"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/layout_more_user"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_current_user"
                android:layout_width="88dp"
                android:layout_height="88dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ps_ic_audio_placeholder" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_current_user"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:textSize="@dimen/font_medium"
                android:textStyle="bold"
                android:textColor="?colorOnSurface"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/img_current_user"
                tools:text="草莓哈哈" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_checked"
                android:layout_width="14dp"
                android:layout_height="14dp"
                app:layout_constraintBottom_toBottomOf="@id/tv_current"
                app:layout_constraintEnd_toStartOf="@id/tv_current"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@id/tv_current_user"
                app:layout_constraintTop_toTopOf="@id/tv_current"
                app:srcCompat="@drawable/ico_checkbox_checked" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_current"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:text="@string/current_usage"
                android:textColor="?colorPrimary"
                android:textSize="@dimen/font_small"
                app:layout_constraintEnd_toEndOf="@id/tv_current_user"
                app:layout_constraintStart_toEndOf="@id/img_checked"
                app:layout_constraintTop_toBottomOf="@id/tv_current_user" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_more_user"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/layout_current_user"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/layout_current_user"
            app:layout_constraintTop_toTopOf="@id/layout_current_user">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_cache_user"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="28dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_cache_user"
                    android:layout_width="72dp"
                    android:layout_height="72dp"
                    android:layout_marginBottom="@dimen/activity_vertical_margin"
                    app:layout_constraintBottom_toTopOf="@id/tv_cache_user"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:src="@drawable/ps_ic_audio_placeholder" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_cache_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?colorOnSurfaceVariant"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="草莓哈哈" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_add_user"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="28dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_add_user"
                    android:layout_width="72dp"
                    android:layout_height="72dp"
                    android:layout_marginBottom="@dimen/activity_vertical_margin"
                    android:background="@drawable/shape_circle_surface_bg"
                    android:backgroundTint="@color/grey50"
                    android:padding="@dimen/space24"
                    app:layout_constraintBottom_toTopOf="@id/tv_add_user"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:srcCompat="@drawable/ico_add" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_add_user"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/add_account"
                    android:textColor="?colorOnSurfaceVariant"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>