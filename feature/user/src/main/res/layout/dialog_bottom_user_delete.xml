<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingBottom="@dimen/space24"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space24"
        android:paddingEnd="@dimen/space24"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel_the_account"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:lineSpacingExtra="4dp"
            android:text="@string/cancel_account_tip"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_normal"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_check_container"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/container"
        android:layout_height="wrap_content">
        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/check_box"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/sel_checkbox"
            app:buttonTint="@null"
            android:layout_marginStart="@dimen/half_normal_margin"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_check_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel_account_confirm"
            android:textSize="@dimen/font_small"
            app:layout_constraintBottom_toBottomOf="@id/check_box"
            app:layout_constraintStart_toEndOf="@id/check_box"
            app:layout_constraintTop_toTopOf="@id/check_box" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_sure_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/half_normal_margin"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        android:text="@string/confirm_cancellation"
        android:textColor="?colorError"
        android:textSize="@dimen/font_normal"

        app:backgroundTint="?colorSurface"
        app:cornerRadius="@dimen/space12"
        app:layout_constraintEnd_toStartOf="@id/bt_cancel_action"
        app:layout_constraintTop_toBottomOf="@id/layout_check_container" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_cancel_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/Cancel"
        android:layout_marginEnd="@dimen/space24"
        android:textColor="?colorOnPrimary"
        android:textSize="@dimen/font_normal"
        app:backgroundTint="?colorPrimary"
        app:cornerRadius="@dimen/space12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/bt_sure_action" />
</androidx.constraintlayout.widget.ConstraintLayout>