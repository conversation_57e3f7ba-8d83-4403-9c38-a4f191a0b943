<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space36"
        android:text="@string/enter_the_verification_code"
        android:textColor="?colorOnSurface"
        android:textSize="20dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/layout_verification_code"
        app:layout_constraintTop_toBottomOf="@id/app_bar" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_small"
        app:layout_constraintStart_toStartOf="@id/tv_login_title"
        app:layout_constraintTop_toBottomOf="@id/tv_login_title"
        tools:text="短信已发送至+8618149561495" />


    <com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeLayout
        android:id="@+id/layout_verification_code"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="@dimen/space24"
        android:layout_marginEnd="36dp"
        app:code_number="4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_login_tip" />

    <com.ruiheng.xmuse.core.ui.widget.verify.CountDownTextView
        android:id="@+id/tv_count_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/space12"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_small"
        app:layout_constraintStart_toStartOf="@id/layout_verification_code"
        app:layout_constraintTop_toBottomOf="@id/layout_verification_code" />

</androidx.constraintlayout.widget.ConstraintLayout>