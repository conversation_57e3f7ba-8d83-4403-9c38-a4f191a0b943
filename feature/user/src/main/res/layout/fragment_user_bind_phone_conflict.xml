<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurfaceDim">


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/activity_vertical_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/font_normal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="手机号185****0516已经绑定了账号：麻薯芋子，一个手机号只能绑定一个账号。" />


            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_change_phone"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/tv_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_change_phone_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/bind_other_phone_number"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_normal"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/half_normal_margin"
                        android:text="@string/bind_other_phone_number_tip"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_change_phone_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/layout_force_bind_phone"
                style="?attr/materialCardViewFilledStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                app:layout_constraintTop_toBottomOf="@id/layout_change_phone">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/activity_vertical_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_force_bind_phone_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/continue_bind_phone"
                        android:textColor="?colorOnSurface"
                        android:textSize="@dimen/font_normal"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_force_bind_phone_content"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/half_normal_margin"
                        android:text="@string/continue_bind_phone_tip"
                        android:textSize="@dimen/font_small"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_force_bind_phone_title" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>