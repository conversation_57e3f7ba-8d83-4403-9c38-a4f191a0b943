<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurfaceDim">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_login_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1125:836"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/img_login_top_bg" />

    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:paddingStart="@dimen/space24"
        android:paddingTop="@dimen/space36"
        android:paddingEnd="@dimen/space24"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_height="0dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space12"
            android:text="@string/bind_phone"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space24"
            app:layout_constraintTop_toBottomOf="@id/tv_login_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/tab_bg"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="509:112"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/img_login_top01" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabRippleColor="@android:color/transparent"
                app:tabSelectedTextAppearance="@style/TextAppearance.AppCompat.Large"
                app:tabSelectedTextColor="?colorOnSurface"
                app:tabTextAppearance="@style/TextAppearance.AppCompat.Body1">

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_with_mobile_phone_number" />

                <com.google.android.material.tabs.TabItem
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/email_login" />
            </com.google.android.material.tabs.TabLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_input_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_bottom_corner_surface_bg"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin"
                android:paddingBottom="@dimen/space24"
                app:layout_constraintTop_toBottomOf="@id/tab_bg">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/input_layout_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:boxBackgroundColor="?colorSurfaceContainer"
                    app:hintAnimationEnabled="false"
                    app:hintEnabled="false"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_account"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/enter_account"
                        android:inputType="text"
                        tools:text="***********" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/layout_phone_number_container"
                    style="?attr/materialCardViewFilledStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="?colorSurfaceContainer"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_bind_phone_zone_num"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:background="@null"
                            android:maxLines="1"
                            android:padding="@dimen/activity_horizontal_margin"
                            android:singleLine="true"
                            android:text="+86"
                            android:textSize="@dimen/font_normal"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="RtlSymmetry"
                            tools:text="+86" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:boxBackgroundMode="none"
                            app:endIconDrawable="@drawable/ic_close"
                            app:endIconMode="clear_text"
                            app:endIconTint="?colorOnSurface"
                            app:hintEnabled="false"
                            app:layout_constraintBottom_toBottomOf="@id/tv_bind_phone_zone_num"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/tv_bind_phone_zone_num"
                            app:layout_constraintTop_toTopOf="@id/tv_bind_phone_zone_num">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_bind_phone_number"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical|start"
                                android:inputType="phone"
                                android:maxLines="1"
                                android:singleLine="true"

                                android:textSize="@dimen/font_normal"
                                tools:ignore="RtlSymmetry"
                                tools:text="***********" />

                        </com.google.android.material.textfield.TextInputLayout>


                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_next"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="@dimen/space24"
                    android:text="@string/get_verification_code"
                    app:layout_constraintTop_toBottomOf="@id/layout_phone_number_container" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_email_next"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="@dimen/space24"
                    android:text="@string/get_verification_code"
                    app:layout_constraintTop_toBottomOf="@id/input_layout_account" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_account"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/space24"
            android:padding="@dimen/half_normal_margin"
            android:text="@string/switch_to_another_account"
            android:textColor="?colorOnSurfaceVariant"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/check_box"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/space36"
            android:button="@drawable/sel_checkbox"
            android:visibility="gone"
            app:buttonTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_check_content"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_check_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/check_and_agree"
            android:textSize="@dimen/font_small"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/check_box"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/check_box"
            app:layout_constraintTop_toTopOf="@id/check_box" />
    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>