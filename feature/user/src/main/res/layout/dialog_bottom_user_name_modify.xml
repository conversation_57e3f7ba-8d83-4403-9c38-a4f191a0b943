<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space24"
        android:paddingEnd="@dimen/space24"
        android:paddingBottom="@dimen/space36"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/modify_nickname"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            app:boxBackgroundColor="?colorSurfaceContainer"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/modify_nickname" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_sure_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:text="@string/Sure"
            android:textColor="?colorOnPrimary"
            android:textSize="@dimen/font_medium"
            app:backgroundTint="?colorPrimary"
            app:cornerRadius="@dimen/space12"
            app:layout_constraintDimensionRatio="6.2:1"
            app:layout_constraintTop_toBottomOf="@id/input_name" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>