<resources>
    <!-- Strings used for fragments for navigation -->


    <string name="account_setting">账号设置</string>
    <string name="account_change">切换账号</string>
    <string name="account_manager">账号管理</string>

    <string name="account_safe">账号与安全</string>
    <string name="change_login_account">切换账号登录</string>
    <string name="nickname">昵称</string>
    <string name="gender">性别</string>
    <string name="birthday_year">出生年份</string>
    <string name="modify_account_password">登录密码修改</string>
    <string name="modify_avatar">头像修改</string>
    <string name="bond_phone">手机号绑定</string>
    <string name="bond_email">邮箱绑定</string>
    <string name="account_termination">账号注销</string>
    <string name="bond_third_account">第三方账号绑定</string>
    <string name="sign_out">退出登录</string>
    <string name="modify_nickname">修改昵称</string>
    <string name="modify_phone">换绑手机号</string>
    <string name="enter_new_phone">请输入新的手机号</string>
    <string name="current_phone_string">当前手机号：%s</string>
    <string name="current_email_string">当前邮箱：%s</string>
    <string name="code_send_to_phone_already">短信已发送至手机：%s</string>
    <string name="phone_modify_conflict">换绑手机号冲突</string>
    <string name="email_modify_conflict">换绑邮箱号冲突</string>
    <string name="phone_bind_conflict">绑定手机号冲突</string>
    <string name="email_bind_conflict">绑定邮箱冲突</string>
    <string name="third_party_modify_conflict">绑定第三方账号冲突</string>

    <string name="phone_modify_conflict_content">手机号%s已经绑定了账号：%s，一个手机号只能绑定一个账号。</string>
    <string name="phone_bind_thirdparty_conflict_content">手机号%s绑定的主体账号“%s”已绑定同类型第三方账号，一个主体账号只能绑定一个第三方类型账号。</string>
    <string name="thirdparty_conflict_content">%s"%s"已经绑定了主体账号：%s，一个%s只能绑定一个主体账号。</string>

    <string name="email_modify_conflict_content">邮箱%s已经绑定了账号：%s，一个邮箱只能绑定一个账号。</string>
    <string name="email_bind_thirdparty_conflict_content">邮箱%s绑定的主体账号“%s”已绑定同类型第三方账号，一个主体账号只能绑定一个第三方类型账号。</string>


    <string name="logout_conflict_account">需注销账号：%s</string>
    <string name="force_to_bind_phone">继续使用%s绑定</string>
    <string name="force_to_bind_email">继续使用%s绑定</string>
    <string name="force_to_bind_third_party">继续使用%s"%s"绑定</string>
    <string name="cancel_the_account">注销账号</string>
    <string name="cancel_account_tip">尊敬的用户，您好，为了保障您的账号安全，注销账号需要得到您本人的同意，注销账号的风险如下，请知悉：\n1.妙诗主体账号注销后，所有与此主体账号绑定关系皆失效，用户不能再通过账号关联关系登录到此主体账号使用相关产品功能。\n2.注销后的主体账号的相关数据，用户将无法再通过其它方式恢复或找回。\n3.用户需要同意并知晓账号注销的风险，并在输入验证码后执行注销操作。</string>
    <string name="cancel_account_confirm">我已经确认，继续注销</string>
    <string name="confirm_cancellation">确认注销</string>
    <string name="change_email">邮箱换绑</string>
    <string name="enter_new_email">请输入新的邮箱地址</string>
    <string name="change_password">修改密码</string>
    <string name="old_pwd">原密码</string>
    <string name="new_password">新密码</string>
    <string name="enter_new_password_again">再次输入新密码</string>
    <string name="switch_to_another_account">切换其他账号</string>
    <string name="bind_with_another_email_address">换个邮箱绑定</string>
    <string name="bind_other_email_tip">该主体账号已绑定的第三方同类型账号不变</string>
    <string name="continue_to_use_this_email">继续使用该邮箱强制绑定</string>
    <string name="continue_use_email_tip">将解绑该主体账号已绑定的第三方同类型账号</string>
    <string name="bind_other_phone_number">换个手机号绑定</string>
    <string name="bind_other_phone_number_tip">该主体账号已绑定的第三方同类型账号不变</string>
    <string name="continue_bind_phone">继续使用该手机号强制绑定</string>
    <string name="continue_bind_phone_tip">将解绑该主体账号已绑定的第三方同类型账号</string>
    <string name="conflicting_accounts">冲突账号：</string>
    <string name="current_account">当前账号：</string>
    <string name="how_change_bind_account">你要如何换绑当前账号？</string>
    <string name="both_accounts_can_be_retained">两个账号均可保留</string>
    <string name="tap_the_avatar_to_switch_account_login">轻触头像切换账号登录</string>
    <string name="current_usage">当前使用</string>
    <string name="add_account">添加账号</string>
    <string name="bind_other_third_party_account">换个第三方账号绑定</string>
    <string name="both_third_party_account_can_be_retained">两个第三方账号绑定关系均可保留</string>
    <string name="unbind_origin_third_party">需解除该第三方账号与原主体账号的绑定关系</string>
    <string name="gender_female">女</string>
    <string name="gender_man">男</string>
    <string name="not_set">未设置</string>

</resources>