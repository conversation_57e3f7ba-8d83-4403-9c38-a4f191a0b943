<resources>
    <!-- Strings used for fragments for navigation -->
    <string name="account_setting">Account</string>
    <string name="account_change">Switch Account</string>
    <string name="account_manager">Account Management</string>

    <string name="account_safe">Account Security</string>
    <string name="change_login_account">Switch Login Account</string>
    <string name="nickname">Nickname</string>
    <string name="gender">Gender</string>
    <string name="birthday_year">Birth Year</string>
    <string name="modify_account_password">Change Login Password</string>
    <string name="modify_avatar">Change Avatar</string>
    <string name="bond_phone">Bind Phone Number</string>
    <string name="bond_email">Bind Email</string>
    <string name="bond_third_account">Bind Third-Party Account</string>
    <string name="sign_out">Sign Out</string>
    <string name="modify_nickname">Modify Nickname</string>
    <string name="account_termination">Account Cancellation</string>
    <string name="modify_phone">Change Bound Phone Number</string>
    <string name="enter_new_phone">Please enter the new phone number</string>
    <string name="current_phone_string">Current phone number: %s</string>
    <string name="current_email_string">Current email: %s</string>
    <string name="code_send_to_phone_already">SMS has been sent to phone: %s</string>
    <string name="phone_modify_conflict">Phone Number Change Conflict</string>
    <string name="email_modify_conflict">Email Change Conflict</string>
    <string name="phone_bind_conflict">Phone Binding Conflict</string>
    <string name="third_party_modify_conflict">Third-Party Account Modification Conflict</string>
    <string name="phone_modify_conflict_content">Phone number %s is already bound to account: %s. A phone number can only be bound to one account.</string>
    <string name="phone_bind_thirdparty_conflict_content">The phone number %s is bound to the main account "%s", which has already bound a third-party account of the same type. A main account can only bind one third-party account of the same type.</string>
    <string name="thirdparty_conflict_content">%s "%s" is already bound to the main account: %s. One %s can only be bound to one main account.</string>
    <string name="email_modify_conflict_content">Email %s is already bound to account: %s. An email can only be bound to one account.</string>
    <string name="email_bind_thirdparty_conflict_content">The email %s is bound to the main account "%s", which has already bound a third-party account of the same type. A main account can only bind one third-party account of the same type.</string>
    <string name="email_bind_conflict">Email Binding Conflict</string>
    <string name="logout_conflict_account">Need to unlink account: %s</string>
    <string name="force_to_bind_phone">Continue to use %s to bind</string>
    <string name="force_to_bind_email">Continue to use %s to bind</string>
    <string name="force_to_bind_third_party">Continue to use %s "%s" to bind</string>
    <string name="cancel_the_account">Cancel the account</string>
    <string name="cancel_account_tip">Dear user, hello. To ensure the security of your account, canceling the account requires your personal consent. Please be aware of the following risks associated with account cancellation:\nAfter the main Miaoshi account is canceled, all binding relationships associated with this main account will become invalid. Users will no longer be able to log in to this main account through account association relationships to use related product functions.\nUsers will no longer be able to restore or retrieve the relevant data of the canceled main account through other means.\nUsers need to agree to and be aware of the risks of account cancellation and perform the cancellation operation after entering the verification code.</string>
    <string name="cancel_account_confirm">I have confirmed and will continue with the cancellation.</string>
    <string name="confirm_cancellation">Confirm cancellation</string>
    <string name="change_email">Change the email address</string>
    <string name="enter_new_email">Please enter a new email address.</string>
    <string name="change_password">Change password</string>
    <string name="old_pwd">Original password</string>
    <string name="new_password">New password</string>
    <string name="enter_new_password_again">Enter the new password again.</string>
    <string name="switch_to_another_account">Switch to another account</string>
    <string name="bind_with_another_email_address">Bind with another email address</string>
    <string name="bind_other_email_tip">The third - party accounts of the same type already bound to the main account remain unchanged.</string>
    <string name="continue_to_use_this_email">Continue to use this email for forced binding.</string>
    <string name="continue_use_email_tip">This will unbind the third - party accounts of the same type that have been bound to the main account.</string>
    <string name="bind_other_phone_number">Bind with another mobile phone number</string>
    <string name="bind_other_phone_number_tip">The third - party accounts of the same type already bound to the main account remain unchanged.</string>
    <string name="continue_bind_phone">Continue to use this mobile phone number for forced binding.</string>
    <string name="continue_bind_phone_tip">This will unbind the third - party accounts of the same type that have been bound to the main account.</string>
    <string name="conflicting_accounts">Conflicting accounts：</string>
    <string name="current_account">Current account：</string>
    <string name="how_change_bind_account">How do you change the binding of the current account？</string>
    <string name="both_accounts_can_be_retained">Both accounts can be retained.</string>
    <string name="tap_the_avatar_to_switch_account_login">Tap the avatar to switch account login.</string>
    <string name="current_usage">Current</string>
    <string name="add_account">Add account</string>
    <string name="bind_other_third_party_account">Bind with another third - party account</string>
    <string name="both_third_party_account_can_be_retained">The binding relationships of both third - party accounts can be retained.</string>
    <string name="unbind_origin_third_party">It is necessary to unbind the third - party account from the original main account.</string>
    <string name="gender_female">Female</string>
    <string name="gender_man">Male</string>
    <string name="not_set">Not set</string>
</resources>