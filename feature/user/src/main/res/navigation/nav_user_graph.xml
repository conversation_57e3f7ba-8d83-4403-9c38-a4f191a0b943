<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_cat_add_graph"
    app:startDestination="@id/userHome">

    <fragment
        android:id="@+id/userHome"
        android:name="com.ruiheng.xmuse.feature.user.ui.UserHomeFragment"
        android:label="@string/account_manager"
        tools:layout="@layout/fragment_user">
        <action
            android:id="@+id/actionHomeToThirdPartyPage"
            app:destination="@id/thirdPartyList" />
        <action
            android:id="@+id/actionHomeToChangeUserPage"
            app:destination="@id/userListFragment" />

        <action
            android:id="@+id/actionHomeToUpdatePhoneConflictPage"
            app:destination="@id/updatePhoneConflictFragment" />

        <action
            android:id="@+id/actionHomeToUpdateEmailConflictPage"
            app:destination="@id/updateEmailConflictFragment" />
    </fragment>

    <fragment
        android:id="@+id/thirdPartyList"
        android:name="com.ruiheng.xmuse.feature.user.ui.thirdparty.ThirdPartyListFragment"
        android:label="@string/bond_third_account"
        tools:layout="@layout/fragment_third_party_list">
        <action
            android:id="@+id/actionBindThirdPartyConflictPage"
            app:destination="@id/bindThirdPartyConflictFragment" />
    </fragment>

    <fragment
        android:id="@+id/userListFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.UserSelectFragment"
        android:label="@string/account_change"
        tools:layout="@layout/fragment_user_list">

    </fragment>

    <fragment
        android:id="@+id/updatePhoneConflictFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.UserPhoneModifyConflictFragment"
        android:label="@string/phone_modify_conflict"
        tools:layout="@layout/fragment_user_phone_modify_conflict">

    </fragment>

    <fragment
        android:id="@+id/updateEmailConflictFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.UserEmailModifyConflictFragment"
        android:label="@string/email_modify_conflict"
        tools:layout="@layout/fragment_user_email_modify_conflict">

    </fragment>

    <fragment
        android:id="@+id/bindThirdPartyConflictFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.thirdparty.UserThirdPartyModifyConflictFragment"
        android:label="@string/third_party_modify_conflict"
        tools:layout="@layout/fragment_user_third_party_conflict">

    </fragment>
</navigation>