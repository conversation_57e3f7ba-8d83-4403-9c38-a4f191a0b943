<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_cat_add_graph"
    app:startDestination="@id/bindPhoneFragment">

    <fragment
        android:id="@+id/bindPhoneFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.phone.UserBindAccountFragment"
        android:label="@string/login"
        tools:layout="@layout/fragment_user_bind_account">
        <action
            android:id="@+id/actionBindPhoneToSmsVerifyPage"
            app:destination="@id/smsVerifyFragment" />

        <action
            android:id="@+id/actionToEmailSmsVerifyPage"
            app:destination="@id/emailSmsVerifyFragment" />
    </fragment>

    <fragment
        android:id="@+id/smsVerifyFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.phone.UserSMSVerifyFragment"
        tools:layout="@layout/fragment_user_sms_verify">
        <action
            android:id="@+id/actionSmsVerifyToConflictPage"
            app:destination="@id/conflictFragment" />
    </fragment>


    <fragment
        android:id="@+id/emailSmsVerifyFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.phone.UserEmailSMSVerifyFragment"
        tools:layout="@layout/fragment_user_email_sms_verify">
        <action
            android:id="@+id/actionEmailSmsVerifyToConflictPage"
            app:destination="@id/emailConflictFragment" />
    </fragment>

    <fragment
        android:id="@+id/conflictFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.phone.UserThirdPartyBindPhoneConflictFragment"
        tools:layout="@layout/fragment_user_bind_phone_conflict">

    </fragment>
    <fragment
        android:id="@+id/emailConflictFragment"
        android:name="com.ruiheng.xmuse.feature.user.ui.phone.UserThirdPartyBindEmailConflictFragment"
        tools:layout="@layout/fragment_user_bind_email_conflict">

    </fragment>
</navigation>