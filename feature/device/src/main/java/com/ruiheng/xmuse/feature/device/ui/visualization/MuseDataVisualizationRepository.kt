package com.ruiheng.xmuse.feature.device.ui.visualization

import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.MuseDataPacketType
import com.ruiheng.xmuse.core.data.AppExecutors
import com.ruiheng.xmuse.core.data.postLiveDataChange
import com.ruiheng.xmuse.core.data.repository.muse.MuseDeviceRepository
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQuality
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalValueQueue
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.reactivex.rxjava3.subjects.BehaviorSubject
import org.threeten.bp.Instant
import timber.log.Timber
import java.util.Collections
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

@Singleton
class MuseDataVisualizationRepository @Inject constructor(
    private val appExecutors: AppExecutors,
    private val museDeviceRepository: MuseDeviceRepository
) {
    companion object {
        val visualizationViewSize: Int = 120
        val visualizationViewHeartSize: Int = 20
        val visualizationViewBloodOxygen: Int = 20
        private const val PeriodTime = 120L
        private const val bloodOxygenPeriodTime = 1000L
    }

    val latestQueue = hashMapOf(
        MuseDataPacketType.ALPHA_RELATIVE to SignalValueQueue(
            Collections.nCopies(
                visualizationViewSize,
                0f
            )
        ),
        MuseDataPacketType.BETA_RELATIVE to SignalValueQueue(
            Collections.nCopies(
                visualizationViewSize,
                0f
            )
        ),
        MuseDataPacketType.GAMMA_RELATIVE to SignalValueQueue(
            Collections.nCopies(
                visualizationViewSize,
                0f
            )
        ),
        MuseDataPacketType.THETA_RELATIVE to SignalValueQueue(
            Collections.nCopies(
                visualizationViewSize,
                0f
            )
        ),
        MuseDataPacketType.DELTA_RELATIVE to SignalValueQueue(
            Collections.nCopies(
                visualizationViewSize,
                0f
            )
        ),
    )
    private val latestAvgPowsRawRelativeValueMap = hashMapOf(
        MuseDataPacketType.ALPHA_RELATIVE to 0f,
        MuseDataPacketType.BETA_RELATIVE to 0f,
        MuseDataPacketType.GAMMA_RELATIVE to 0f,
        MuseDataPacketType.THETA_RELATIVE to 0f,
        MuseDataPacketType.DELTA_RELATIVE to 0f,
    )

    private var latestHeartBeat: Double = 0.0

    private val _latestHeartBeatLiveData = MutableLiveData(latestHeartBeat)
    val latestHeartBeatLiveData = _latestHeartBeatLiveData as LiveData<Double>


    private val _latestAvgPowsRawRelativeValueMapLiveData =
        MutableLiveData<Map<MuseDataPacketType, Float>>(latestAvgPowsRawRelativeValueMap)
    val latestAvgPowsRawRelativeValueMapLiveData =
        _latestAvgPowsRawRelativeValueMapLiveData as LiveData<Map<MuseDataPacketType, Float>>
    private var frameRateEpochMillisClockDisposable = CompositeDisposable()
    private val frameRateEpochMillisClock = Observable.interval(
        PeriodTime,
        TimeUnit.MILLISECONDS
    )


    private var lastBloodOxygen: Int = 0
    private val _lastBloodOxygenLiveData = MutableLiveData(0)
    val lastBloodOxygenLiveData = _lastBloodOxygenLiveData as LiveData<Int>

    init {
        fun updateAvgRelativeValue(type: MuseDataPacketType, values: Array<Double>?) {
            val valueResult = values?.filter { !it.isNaN() }
            var average = if (valueResult.isNullOrEmpty()) 0f else valueResult.average().toFloat()
            if (average !in 0.0..1.0) {
                average = 0f
            }
            latestAvgPowsRawRelativeValueMap[type] = average
            latestQueue[type]?.enqueue(average)

        }
        museDeviceRepository.observerAlphaRelative().observeForever {
            updateAvgRelativeValue(MuseDataPacketType.ALPHA_RELATIVE, it)
        }
        museDeviceRepository.observerBetaRelative().observeForever {
            updateAvgRelativeValue(MuseDataPacketType.BETA_RELATIVE, it)

        }
        museDeviceRepository.observerGammaRelative().observeForever {
            updateAvgRelativeValue(MuseDataPacketType.GAMMA_RELATIVE, it)

        }
        museDeviceRepository.observerThetaRelative().observeForever {
            updateAvgRelativeValue(MuseDataPacketType.THETA_RELATIVE, it)

        }
        museDeviceRepository.observerDeltaRelative().observeForever {
            updateAvgRelativeValue(MuseDataPacketType.DELTA_RELATIVE, it)
        }

        museDeviceRepository.nfbPackets.subscribe { nfbpacket ->
            if (nfbpacket.isHeartUpdated && nfbpacket.isPpgGood) {
                latestHeartBeat = nfbpacket.heartScore
//                lastBloodOxygen = Random.nextInt(4) + 95 //TODO 暂时用模拟数据
//                lastBloodOxygen = if (nfbpacket.isPpgGood) lastBloodOxygen else -1
            }else if (!nfbpacket.isPpgGood){
                latestHeartBeat = 0.0
            }
        }.let { frameRateEpochMillisClockDisposable.add(it) }

        frameRateEpochMillisClock
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { now ->
                if ((now % 5).toInt() == 0) {
                    postLiveDataChange(
                        appExecutors,
                        _latestHeartBeatLiveData,
                        latestHeartBeat
                    )
                }
                if ((now % 10).toInt() == 0) {
                    postLiveDataChange(
                        appExecutors,
                        _lastBloodOxygenLiveData,
                        lastBloodOxygen
                    )

                }
                postLiveDataChange(
                    appExecutors,
                    _latestAvgPowsRawRelativeValueMapLiveData,
                    latestAvgPowsRawRelativeValueMap.toMap()
                )
            }.let { frameRateEpochMillisClockDisposable.add(it) }
    }

}

fun MuseDataPacketType.loadVisualizationLineColor(): Int {
    val color = when (this) {
        MuseDataPacketType.THETA_RELATIVE -> {
            com.ruiheng.xmuse.core.ui.R.color.color1
        }

        MuseDataPacketType.ALPHA_RELATIVE -> {
            com.ruiheng.xmuse.core.ui.R.color.color2
        }

        MuseDataPacketType.BETA_RELATIVE -> {
            com.ruiheng.xmuse.core.ui.R.color.color3
        }

        MuseDataPacketType.GAMMA_RELATIVE -> {
            com.ruiheng.xmuse.core.ui.R.color.color4
        }

        MuseDataPacketType.DELTA_RELATIVE -> {
            com.ruiheng.xmuse.core.ui.R.color.color5
        }

        else -> {
            com.ruiheng.xmuse.core.ui.R.color.color5
        }
    }
    return ContextCompat.getColor(Utils.getApp(), color)
}