package com.ruiheng.xmuse.feature.device.ui.sqc

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.PowerManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.BluetoothConnectionViewOwner
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.BluetoothConnectionView
import com.ruiheng.xmuse.core.ui.ui.BaseNavFragment
import com.ruiheng.xmuse.feature.device.SignalQualityViewModel
import com.ruiheng.xmuse.feature.device.databinding.FragmentSqcPageBinding
//import io.github.douglasjunior.androidSimpleTooltip.ArrowDrawable
//import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip

class SqcFragment : BaseNavFragment<FragmentSqcPageBinding>(), BluetoothConnectionViewOwner {

    companion object {
        const val TAG = "SqcFragment"
    }

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentSqcPageBinding.inflate(inflater, container, false)

    interface Callback {
        fun onSqcExited()
        fun onSqcPassed()
    }

    private var callback: Callback? = null

    private val viewModel by lazy {
        ( requireActivity() as SqcActivity).sqcViewModel
    }

    protected val signalQualityViewModel: SignalQualityViewModel by lazy {
        (requireActivity() as SqcActivity).signalQualityViewModel
    }

//    private var batteryTooltip: SimpleTooltip? = null

//    private val viewModel: SqcViewModel by lazy {
//        injectSelf()
//        ViewModelProvider(this, vmFactory)[SqcViewModel::class.java].apply {
//            onboarding = when (args.onPassBehaviour) {
//                PassBehaviour.SESSION -> false
//                PassBehaviour.ONBOARDING,
//                PassBehaviour.HEADBAND_SETUP_INSTRUCTIONS -> true
//            }
//        }
//    }

//    private val args: SqcFragmentArgs by lazy {
//        SqcFragmentArgs.fromBundle(requireArguments())
//    }

    private var alertDialog: AlertDialog? = null

    private var wakeLock: PowerManager.WakeLock? = null

    override val btBtnPopupRootView get() = null
    override fun leeroySelected() {

    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        viewModel.playAudio()
//        setupToolbar()
//        setupHelpDrawer()
//        setupScreenBackground()
//        setupBattery()
//        setupSignalTest(args.onPassBehaviour == PassBehaviour.SESSION)
        setupSignalTest()

//        if (args.showSkip) {
//            setupResumeButton()
//        } else if (args.onPassBehaviour != PassBehaviour.SESSION) {
//            setupOnboarding(args.onPassBehaviour)
//        } else if (viewModel.lenientSqcEnabled) {
//            setupLenientSqc()
//        }
//        setupBluetoothButton()
//        setupDisconnectionAlert()
//        if (Device.INSTANCE.isDebugModeOn) {
//            setupDebugControls()
//        }

    }

    override fun onResume() {
        super.onResume()
        obtainScreenWakeLock()

        viewModel.startSqc()
        viewModel.resume()

//        if (viewModel.showSqcPassed.value == true && args.onPassBehaviour == PassBehaviour.SESSION) {
//            viewModel.stopTrackingAlertRecovery(BAD_SIGNAL, PASSED_SQC)
//            callback?.onSqcPassed()
//            binding.btnBluetoothConnection.dismissBluetoothPopovers()
//        }
//
//        if (Device.INSTANCE.isDebugModeOn) {
//            binding.debugTouchView.addSwitchStateAction { newState: Boolean ->
//                binding.debugControls.visibility = if (newState) View.GONE else View.VISIBLE
//            }
//        }
    }

    override fun onPause() {
        super.onPause()
//        binding.debugTouchView.clearAllActions()
        releaseScreenWakeLock()
        viewModel.pause()
//        batteryTooltip?.dismiss()
    }

    override fun onStart() {
        super.onStart()
        viewModel.resumeAudio()
    }

    override fun onStop() {
        super.onStop()
        viewModel.pauseAudio()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.stopAudio()
//        drawerBehaviour.removeBottomSheetCallback(bottomSheetCallback)
//        _binding = null
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        when (context) {
            is Callback -> callback = context
        }

        when (context) {
            is FragmentActivity -> {
                context.onBackPressedDispatcher.addCallback(
                    this,
                    object : OnBackPressedCallback(true) {
                        override fun handleOnBackPressed() {
//                            viewModel.stopTrace(SqcPerformanceTrace.Result.EXIT_SQC)
                            callback?.onSqcExited()
                        }
                    })
            }
        }
    }

    private fun setupSignalTest(callbackOnPass: Boolean = true) {

        viewModel.showSqcPassed.observe(viewLifecycleOwner) {
            if (it) {
                binding.sqcLayoutTest.drawCheckmark {
//                    viewModel.stopTrackingAlertRecovery(BAD_SIGNAL, PASSED_SQC)
                    if (callbackOnPass) {
                        callback?.onSqcPassed()
//                        _binding?.btnBluetoothConnection?.dismissBluetoothPopovers()
                    }
                }
            }
        }
    }

//    private fun setupResumeButton() {
//        binding.resumeButton.visibility = View.VISIBLE
//        binding.resumeButton.setOnClickListener {
//            viewModel.stopTrace(SqcPerformanceTrace.Result.SKIP)
//            viewModel.stopTrackingAlertRecovery(BAD_SIGNAL, SKIPPED_SQC)
//            binding.signalQualityTest.cancelAnimation()
//            callback?.onSqcPassed()
//        }
//        binding.continueButton.visibility = View.INVISIBLE
//    }
//
//    private fun setupOnboarding(passBehaviour: PassBehaviour) {
//        binding.skipButton.text = FontUtils.underlineSubstring(
//            binding.skipButton.text,
//            binding.skipButton.text.toString()
//        )
//        binding.skipButton.visibility = View.INVISIBLE
//        binding.skipButton.setOnClickListener {
//            Analytics.instance.logOnboardingSkipButtonTapped(Analytics.Button.SKIP)
//            viewModel.stopTrace(SqcPerformanceTrace.Result.SKIP)
//            binding.signalQualityTest.cancelAnimation()
//            callback?.onSqcPassed()
//        }
//
//        binding.onboardingFeedbackText.visibility = View.VISIBLE
//
//        setupContinueAnywayButtonOnClick(onboarding = true)
//        binding.continueButton.text = getString(
//            if (passBehaviour == PassBehaviour.HEADBAND_SETUP_INSTRUCTIONS) {
//                R.string.session_flow_sqc_return_to_settings
//            } else {
//                R.string.session_flow_sqc_explore_app
//            }
//        )
//
//        viewModel.skipButtonVisible.observe(viewLifecycleOwner) {
//            binding.skipButton.visibility = if (it) {
//                View.VISIBLE
//            } else {
//                View.INVISIBLE
//            }
//        }
//
//        viewModel.onboardingContinueVisible.observe(viewLifecycleOwner) {
//            binding.onboardingFeedbackText.text =
//                getString(
//                    if (it) {
//                        R.string.session_flow_sqc_onboarding_congrats
//                    } else {
//                        R.string.session_flow_sqc_onboarding_now_testing
//                    }
//                )
//
//            if (it) {
//                showContinueButton()
//            } else {
//                binding.continueButton.visibility = View.INVISIBLE
//                binding.maxSignalNoteLayout.visibility = View.INVISIBLE
//            }
//        }
//    }

//    private fun setupLenientSqc() {
//        setupContinueAnywayButtonOnClick(onboarding = false)
//        viewModel.continueButtonVisible.observe(viewLifecycleOwner) {
//            if (it) {
//                showContinueButton()
//            } else {
//                binding.continueButton.visibility = View.INVISIBLE
//                binding.maxSignalNoteLayout.visibility = View.INVISIBLE
//            }
//        }
//    }

//    private fun showContinueButton() {
//        Glide.with(this)
//            .load(R.drawable.sqc_animation)
//            .into(binding.fillSqcExampleImage)
//
//        binding.continueButton.visibility = View.VISIBLE
//        binding.maxSignalNoteLayout.visibility = View.VISIBLE
//
//        binding.resumeButton.visibility = View.INVISIBLE
//    }

//    private fun setupContinueAnywayButtonOnClick(onboarding: Boolean) {
//        binding.continueButton.setOnClickListener {
//            if (onboarding) {
//                Analytics.instance.logButtonTapped(
//                    Analytics.Button.EXPLORE_MUSE,
//                    Analytics.Location.SQC
//                )
//            } else {
//                Analytics.instance.logSQCContinueAnywayButtonTapped(Analytics.Button.CONTINUE_ANYWAY)
//            }
////            viewModel.stopTrace(SqcPerformanceTrace.Result.MIN_SENSORS_GOOD)
////            binding.signalQualityTest.cancelAnimation()
//            callback?.onSqcPassed()
//        }
//    }

//    private fun setupToolbar() {
//        binding.toolbar.tvToolbarTitle.text = getString(R.string.session_flow_sqc_screen_title)
//        binding.toolbar.toolbarActionLeft.setOnClickListener {
//            binding.toolbar.toolbarActionLeft.isEnabled = false
//            viewModel.stopTrace(SqcPerformanceTrace.Result.EXIT_SQC)
//            callback?.onSqcExited()
//        }
//
//        binding.toolbar.toolbarActionRight.visibility = View.INVISIBLE
//        binding.toolbar.root.setBackgroundResource(android.R.color.transparent)
//        ViewUtils.adjustMarginsForInsets(
//            binding.toolbar.root,
//            WindowInsetsCompat.Type.statusBars()
//        )
//    }

//    private fun setupScreenBackground() {
//        when (args.onPassBehaviour) {
//            PassBehaviour.SESSION -> {
//                binding.ivSqcBackground.setupBackground(
//                    args.backgroundImageUrl,
//                    R.drawable.img_default_play_pause_screen,
//                    50,
//                    resources.getInteger(android.R.integer.config_shortAnimTime)
//                )
//            }
//
//            PassBehaviour.ONBOARDING,
//            PassBehaviour.HEADBAND_SETUP_INSTRUCTIONS -> {
//                binding.ivSqcBackground.background =
//                    ResourcesCompat.getDrawable(
//                        resources,
//                        R.drawable.onboarding_sqc_background,
//                        null
//                    )
//            }
//        }
//    }

//    private fun setupBattery() {
//        if (!args.batteryWarningVisible || args.showSkip) {
//            return
//        }
//
//        viewModel.lowBatteryWarning.observe(viewLifecycleOwner) {
//            if (it.getContentIfNotHandled() == true) {
//                val layout = PopoverLowBatteryBinding.inflate(LayoutInflater.from(requireContext()))
//                batteryTooltip = SimpleTooltip.Builder(requireContext())
//                    .margin(0.0f)
//                    .padding(0.0f)
//                    .arrowWidth(requireContext().resources.getDimension(R.dimen._8sdp))
//                    .arrowHeight(requireContext().resources.getDimension(R.dimen._5sdp))
//                    .arrowColor(
//                        ContextCompat.getColor(
//                            requireContext(),
//                            R.color.sleep_stages_graph_awake_color
//                        )
//                    )
//                    .dismissOnInsideTouch(false)
//                    .dismissOnOutsideTouch(true)
//                    .anchorView(binding.btnBluetoothConnection)
//                    .contentView(layout.root, 0)
//                    .arrowDirection(ArrowDrawable.TOP)
//                    .gravity(Gravity.BOTTOM)
//                    .onShowListener {
//                        val tooltipContentDescription =
//                            StringBuilder().append(layout.messageText.text)
//                        layout.root.announceForAccessibility(tooltipContentDescription)
//                    }
//                    .build()
//                batteryTooltip?.show()
//            } else {
//                if (batteryTooltip?.isShowing == true) {
//                    batteryTooltip!!.dismiss()
//                }
//            }
//        }
//    }

//    private val startActivityForLeeroyConnectResult =
//        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
//            if (it.resultCode == Activity.RESULT_OK) {
//                binding.btnBluetoothConnection.dismissDeviceListPopover()
//            }
//        }

//    private fun setupBluetoothButton() {
//        binding.btnBluetoothConnection.apply {
//            allowOnboardingStart = false
//            bluetoothStateCallback = {
//                if (it is BluetoothState.Connected) {
//                    if (alertDialog != null) {
//                        if (alertDialog!!.isShowing) {
//                            alertDialog!!.dismiss()
//                        }
//
//                        viewModel.resume()
//                        alertDialog = null
//                    }
//                }
//            }
//            viewOwner = this@SqcFragment
//        }.registerLifecycle(lifecycle)
//    }

//    override fun leeroySelected() {
//        val intent = ConnectLeeroyActivity.newIntent(requireContext())
//        startActivityForLeeroyConnectResult.launch(intent)
//    }

    override fun setupBluetoothButtonPermissionHandling(view: BluetoothConnectionView) {
//        TODO("Not yet implemented")
    }

//    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
//        override fun onSlide(bottomSheet: View, slideOffset: Float) {
//            binding.helpDrawer.ivArrowCollapseExpand.rotation = slideOffset * 180
//        }
//
//        override fun onStateChanged(bottomSheet: View, newState: Int) {
//            if (newState == BottomSheetBehavior.STATE_COLLAPSED || newState == BottomSheetBehavior.STATE_HIDDEN) {
//                binding.helpDrawer.tvNeedHelpTitle.visibility = View.VISIBLE
//                binding.helpDrawer.vpHelpPages.visibility = View.INVISIBLE
//                binding.helpDrawer.circleIndicatorHelpPages.visibility = View.INVISIBLE
//            } else {
//                binding.helpDrawer.vpHelpPages.visibility = View.VISIBLE
//                binding.helpDrawer.circleIndicatorHelpPages.visibility = View.VISIBLE
//                binding.helpDrawer.tvNeedHelpTitle.visibility = View.INVISIBLE
//            }
//        }
//    }

//    private fun setupHelpDrawer() {
//        drawerBehaviour = BottomSheetBehavior.from(binding.helpDrawer.bottomSheetHelp)
//        drawerBehaviour.addBottomSheetCallback(bottomSheetCallback)
//
//        listOf(
//            binding.helpDrawer.circleIndicatorHelpPages,
//            binding.helpDrawer.btnPrevTip,
//            binding.helpDrawer.btnNextTip
//        ).forEach {
//            ViewUtils.adjustMarginsForInsets(it, WindowInsetsCompat.Type.navigationBars())
//        }
//
//        binding.helpDrawer.arrowButton.setOnClickListener {
//            when (drawerBehaviour.state) {
//                BottomSheetBehavior.STATE_COLLAPSED -> {
//                    binding.helpDrawer.arrowButton.contentDescription =
//                        getString(R.string.hide_signal_quality_tips_accessibility)
//                    drawerBehaviour.state =
//                        BottomSheetBehavior.STATE_EXPANDED
//                    Analytics.instance.logViewSqcTip(1)
//                }
//
//                BottomSheetBehavior.STATE_EXPANDED -> {
//                    binding.helpDrawer.arrowButton.contentDescription =
//                        getString(R.string.show_signal_quality_tips_accessibility)
//                    drawerBehaviour.state =
//                        BottomSheetBehavior.STATE_COLLAPSED
//                }
//
//                else -> {
//                }
//            }
//        }
//
//        with(binding.helpDrawer.vpHelpPages) {
//            addOnPageChangeListener(object : ViewPager.SimpleOnPageChangeListener() {
//                override fun onPageSelected(position: Int) {
//                    binding.helpDrawer.btnPrevTip.visibility =
//                        if (position != 0) View.VISIBLE else View.INVISIBLE
//                    binding.helpDrawer.btnNextTip.visibility =
//                        if (position != (drawerHelpPages.size - 1)) View.VISIBLE else View.INVISIBLE
//                    Analytics.instance.logViewSqcTip(position + 1)
//                }
//            })
//            adapter = SqcHelpPagerAdapter(
//                childFragmentManager,
//                drawerHelpPages
//            )
//            binding.helpDrawer.circleIndicatorHelpPages.setViewPager(this)
//        }
//
//        binding.helpDrawer.btnPrevTip.setOnClickListener { --binding.helpDrawer.vpHelpPages.currentItem }
//        binding.helpDrawer.btnNextTip.setOnClickListener { ++binding.helpDrawer.vpHelpPages.currentItem }
//    }

//    private fun setupDebugControls() {
//        with(binding.debugSwitch) {
//            setOnCheckedChangeListener { _, isChecked ->
//                viewModel.isDebugModeOn = isChecked
//            }
//            isChecked = viewModel.isDebugModeOn
//        }
//
//        binding.debugBusymindMode.visibility = View.VISIBLE
//        binding.debugBusymindModeLabel.visibility = View.VISIBLE
//
//        binding.debugDrlrefSwitch.setOnCheckedChangeListener { _, isChecked ->
//            viewModel.onTestDrlRefConnectionStatusChanged(isChecked)
//        }
//
//        binding.debugPpgSwitch.setOnCheckedChangeListener { _, isChecked ->
//            viewModel.onTestPpgSensorStatusChanged(isChecked)
//        }
//
//        binding.debugHeartbeatButton.setOnClickListener { viewModel.onTestHeartbeatDetected() }
//
//        val debugEegSliders = mapOf(
//            SqcSensor.EEG_LEFT_BACK to binding.leftBackEegSeekbar,
//            SqcSensor.EEG_LEFT_FRONT to binding.leftFrontEegSeekbar,
//            SqcSensor.EEG_RIGHT_FRONT to binding.rightFrontEegSeekbar,
//            SqcSensor.EEG_RIGHT_BACK to binding.rightBackEegSeekbar
//        )
//        debugEegSliders.forEach { (_, seekbar) ->
//            seekbar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
//                override fun onStartTrackingTouch(seekBar: SeekBar?) {
//                }
//
//                override fun onStopTrackingTouch(seekBar: SeekBar?) {
//                }
//
//                override fun onProgressChanged(
//                    seekBar: SeekBar?,
//                    progress: Int,
//                    fromUser: Boolean
//                ) {
//                    viewModel.onTestEegSignalChanged(debugEegSliders.mapValues {
//                        min(1.0, max(0.0, it.value.progress / 100.0))
//                    })
//                }
//            })
//        }
//
//        viewModel.dataTrackingState.observe(viewLifecycleOwner) {
//            binding.debugDataTrackingState.text = it.toString()
//        }
//        viewModel.durationTrackingState.observe(viewLifecycleOwner) {
//            binding.debugDurationTrackingState.text = it.toString()
//        }
//
//        binding.debugResetButton.setOnClickListener {
//            viewModel.resetSqcValues()
//            binding.debugDrlrefSwitch.isChecked = false
//            binding.debugPpgSwitch.isChecked = false
//            binding.leftBackEegSeekbar.progress = 0
//            binding.leftFrontEegSeekbar.progress = 0
//            binding.rightFrontEegSeekbar.progress = 0
//            binding.rightBackEegSeekbar.progress = 0
//        }
//
//        binding.debugContinueButton.setOnClickListener {
//            binding.signalQualityTest.cancelAnimation()
//            callback?.onSqcPassed()
//        }
//
//        viewModel.busymindMode.observe(viewLifecycleOwner) {
//            binding.debugBusymindMode.text = when (it) {
//                BusymindMode.CALIBRATION -> "CALIBRATION"
//                BusymindMode.FEEDBACK -> "FEEDBACK"
//                BusymindMode.IDLE -> "IDLE"
//                BusymindMode.SIGNAL_TEST -> "SIGNAL TEST"
//                else -> "UNKNOWN"
//            }
//        }
//    }

//    private fun setupDisconnectionAlert() {
//        viewModel.showDisconnectionAlert.observe(viewLifecycleOwner) {
//            it.getContentIfNotHandled()?.let {
//                val muse = viewModel.sessionMuse
//                if (muse != null) {
//                    Analytics.instance.logMuseAdverseEvent(
//                        viewModel.sessionId,
//                        Analytics.MuseAdverseEventLocation.SQC,
//                        Analytics.MuseAdverseEventType.DISCONNECT,
//                        muse,
//                        viewModel.museBatteryPercentage
//                    )
//                }
//                viewModel.pause()
//                alertDialog = AlertDialog.Builder(requireActivity(), R.style.AlertDialogTheme)
//                    .setTitle(R.string.session_flow_bluetooth_alert_title)
//                    .setMessage(
//                        getString(
//                            R.string.session_flow_bluetooth_alert_message,
//                            if (Device.DEVICE_TYPE_TABLET == Device.deviceType()) {
//                                getString(R.string.tablet)
//                            } else {
//                                getString(R.string.phone)
//                            }
//                        )
//                    )
//                    .setPositiveButton(R.string.session_flow_bluetooth_disconnected_alert_reconnect_button)
//                    { _, _ -> binding.btnBluetoothConnection.showBluetoothConnectionPopover() }
//                    .setNegativeButton(R.string.session_flow_bluetooth_disconnected_alert_exit_session_button)
//                    { _, _ -> callback?.onSqcExited() }
//                    .setCancelable(false)
//                    .setOnKeyListener { _, i, event ->
//                        if (i == KeyEvent.KEYCODE_BACK) {
//                            if (event.action != KeyEvent.ACTION_DOWN) {
//                                callback?.onSqcExited()
//                            }
//                            true
//                        } else {
//                            false
//                        }
//                    }
//                    .create()
//                alertDialog!!.show()
//            }
//        }
//    }

    @SuppressLint("WakelockTimeout")
    private fun obtainScreenWakeLock() {
        val p = requireActivity().getSystemService(Context.POWER_SERVICE) as PowerManager
        @Suppress("DEPRECATION")
        wakeLock = p.newWakeLock(
            PowerManager.SCREEN_DIM_WAKE_LOCK or
                    PowerManager.ON_AFTER_RELEASE, "SqcFragment:wakeLock"
        ).apply {
            acquire()
        }
    }

    private fun releaseScreenWakeLock() {
        if (wakeLock?.isHeld == true) {
            wakeLock?.release()
            wakeLock = null
        }
    }
}
