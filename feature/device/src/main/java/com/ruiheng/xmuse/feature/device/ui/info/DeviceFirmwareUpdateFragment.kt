//package com.ruiheng.xmuse.feature.device.ui.info
//
//import android.os.Bundle
//import android.view.LayoutInflater
//import android.view.View
//import android.view.ViewGroup
//import com.blankj.utilcode.util.ResourceUtils
//import com.choosemuse.libmuse.internal.FirmwareUpdater
//import com.google.gson.JsonArray
//import com.google.gson.JsonObject
//import com.google.gson.JsonPrimitive
//import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
//import com.ruiheng.xmuse.feature.device.R
//import com.ruiheng.xmuse.feature.device.databinding.FragmentDeviceFirmwareUpdateBinding
//import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
//import io.reactivex.rxjava3.core.Observable
//import io.reactivex.rxjava3.disposables.Disposable
//import timber.log.Timber
//import java.util.concurrent.TimeUnit
//import java.util.zip.CRC32
//
//class DeviceFirmwareUpdateFragment : BaseDeviceFragment<FragmentDeviceFirmwareUpdateBinding>(),
//    IBasePageErrorView {
//    override fun bindDataBindingView(
//        inflater: LayoutInflater,
//        container: ViewGroup?
//    ) = FragmentDeviceFirmwareUpdateBinding.inflate(inflater, container, false)
//
//    override fun bindTitleRes() = R.string.scan_device
//
//    override fun initView(saveInsBundle: Bundle?, contentView: View) {
//        super.initView(saveInsBundle, contentView)
//        binding.btnUpdate.setOnClickListener(this)
//    }
//
//
//    override fun onWidgetClick(view: View) {
//        when (view) {
//            binding.btnUpdate -> {
////                loadFirmwareData()
//            }
//
//            else -> {
//                super.onWidgetClick(view)
//            }
//        }
//    }
//
//    private var firmwareUpdateDisposable: Disposable? = null
////    private fun loadFirmwareData() {
////        val firmwareOriginData = ResourceUtils.readAssets2String("2.2.5-1.hex")
////        val imagePackList =
////            firmwareOriginData.split(":").filter { it.isNotEmpty() }
////        val crc = calculateCRC32ForHexList(imagePackList).toInt()
////
////        val imagePackJsonArray = JsonArray()
////        imagePackList.forEach { imageItem ->
////            imagePackJsonArray.add(imageItem)
////        }
////        val selectedMuse = deviceViewModel.connectedMuseLiveData.value
////        if (selectedMuse?.isSuccessWithData() == true) {
////            val update = JsonObject().apply {
////                add("pages", imagePackJsonArray)
////                add("crc", JsonPrimitive(Integer.toHexString(crc)))
////            }
////            val muse = selectedMuse.data
////            val updater = FirmwareUpdater.getUpdater(muse).apply {
////                loadFromData(update.toString())
////                startUpdate()
////            }
////            Observable.interval(25, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
////                .doOnDispose { updater.finish() }
////                .subscribe({
////                    if (updater.isInProgress) {
//////                        mutableUpdateProgress.postValue(updater.progress)
////                        updater.wake()
////                    } else {
////                        if (updater.hasError()) {
////                            Timber.d("Update:ErrorCallback${updater.errorMessage}")
////                        } else {
////                            Timber.d("Update:Success")
////                        }
////                        firmwareUpdateDisposable?.dispose()
////                        firmwareUpdateDisposable = null
////                    }
////                }, {
////                    Timber.d("Update:Error${it}")
////                    firmwareUpdateDisposable?.dispose()
////                    firmwareUpdateDisposable = null
////                })
////                .let { firmwareUpdateDisposable = it }
////        }
////    }
//
//    private fun calculateCRC32ForHexList(imagePackList: List<String>): Long {
//        val combinedHex = imagePackList.joinToString("")
//        val imageByteArray = hexStringToByteArray(combinedHex)
//        return calculateCRC32(imageByteArray)
//    }
//
//    private fun calculateCRC32(data: ByteArray): Long {
//        val crc32 = CRC32()
//        crc32.update(data)
//        return crc32.value
//    }
//
//    private fun hexStringToByteArray(hex: String): ByteArray {
//        val len = hex.length
//        val data = ByteArray(len / 2)
//        var i = 0
//        while (i < len) {
//            data[i / 2] =
//                ((Character.digit(hex[i], 16) shl 4) + Character.digit(hex[i + 1], 16)).toByte()
//            i += 2
//        }
//        return data
//    }
//}