package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import com.choosemuse.libmuse.MuseModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor


enum class SqcConfiguration {
    EEG,
    EEG_PPG,
    EEG_PPG_FNIRS;

    val sqcConveyorSensors: Set<SqcSensor>
        get() = when (this) {
            EEG -> setOf(
                SqcSensor.EEG_LEFT_BACK,
                SqcSensor.EEG_LEFT_FRONT,
                SqcSensor.EEG_RIGHT_FRONT,
                SqcSensor.EEG_RIGHT_BACK,
                SqcSensor.DRL_REF
            )

            EEG_PPG -> setOf(
                SqcSensor.EEG_LEFT_BACK,
                SqcSensor.EEG_LEFT_FRONT,
                SqcSensor.EEG_RIGHT_FRONT,
                SqcSensor.EEG_RIGHT_BACK,
                SqcSensor.DRL_REF,
                SqcSensor.PPG
            )

            EEG_PPG_FNIRS -> setOf(
                SqcSensor.EEG_LEFT_BACK,
                SqcSensor.EEG_LEFT_FRONT,
                SqcSensor.EEG_RIGHT_FRONT,
                SqcSensor.EEG_RIGHT_BACK,
                SqcSensor.DRL_REF
            )
        }

    companion object {
        fun getConfiguration(model: MuseModel?): SqcConfiguration {
            return when (model) {
                MuseModel.MU_03,
                MuseModel.MU_04,
                MuseModel.MU_06,
                MuseModel.MU_05 -> EEG_PPG

                null,
                MuseModel.MU_01,
                MuseModel.MU_02-> EEG

                MuseModel.MS_03 -> EEG_PPG_FNIRS
            }
        }
    }
}