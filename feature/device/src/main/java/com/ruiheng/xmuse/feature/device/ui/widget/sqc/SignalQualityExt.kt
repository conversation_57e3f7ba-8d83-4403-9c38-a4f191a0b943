package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQuality
import com.ruiheng.xmuse.feature.device.R


fun toSignalQuality(value: Int): SignalQuality {
    return when (value) {
        1 -> SignalQuality.GOOD
        2 -> SignalQuality.MEDIOCRE
        else -> SignalQuality.BAD
    }
}

val SignalQuality.color: Int
    get() = when (this) {
        SignalQuality.GOOD -> R.color.session_sqc_color_good
        SignalQuality.MEDIOCRE -> R.color.session_sqc_color_mediocre
        SignalQuality.BAD -> R.color.session_sqc_color_bad
    }

val SignalQuality.drlRefColor: Int
    get() = when (this) {
        SignalQuality.GOOD -> R.color.session_sqc_color_good
        else -> R.color.session_sqc_color_bad
    }

val SignalQuality.ppgColor: Int
    get() = when (this) {
        SignalQuality.GOOD -> R.color.heart_red
        else -> R.color.session_sqc_color_bad
    }

fun sqcValueToSignalQuality(value: Double): SignalQuality {
    return when {
        value < 0.25 -> SignalQuality.BAD
        value in 0.25..0.99 -> SignalQuality.MEDIOCRE
        else -> SignalQuality.GOOD
    }
}