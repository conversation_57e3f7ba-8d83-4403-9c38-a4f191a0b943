package com.ruiheng.xmuse.feature.device.ui.sqc

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.choosemuse.libmuse.internal.BusymindMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.BusymindSignalQualityProcessor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.LiveEvent
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseBatteryPercentageMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseSignalQualityMonitor
import com.ruiheng.xmuse.core.data.repository.muse.isOnline
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.FULL_BATTERY_IMAGE_THRESHOLD
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcConfiguration
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcPassRequirementsChecker
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.sqcValueToSignalQuality
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class SqcViewModel
@Inject constructor(
//    private val audioPlayer: SqcAudioPlayer,
    private val sqMonitor: MuseSignalQualityMonitor,
    private val sqProcessor: BusymindSignalQualityProcessor,
//    private val museConnector: MuseDeviceRepository,
    private val museConnector: MuseConnector,
//    private val museDeviceSelector: MuseDeviceSelector,
//    @Named(SQC_DISCONNECTION_MONITOR)
//    private val disconnectionMonitor: MuseDisconnectionMonitor?,
    private val busymind: BusymindMonitor,

//    private val alertRecoveryAnalytics: AlertRecoveryAnalytics,
//    config: DataTrackingConfig,
    private val museBatteryMonitor: MuseBatteryPercentageMonitor?,
//    private val dataTracker: SessionDataTracker,
//    private val trace: SqcPerformanceTrace
) : ViewModel() {

    val lenientSqcEnabled: Boolean = true

    companion object {
        private const val DISCONNECTION_TIMEOUT_SECONDS = 15
        private const val MIN_MS_IN_SQC = 5000L
    }

//    val sessionId = config.sessionId
//    val sessionMuse = config.muse

    val showSqcPassed: LiveData<Boolean>
        get() = sqcPassedAudioMediator
    private val sqcPassedAudioMediator: MediatorLiveData<Boolean> by lazy {
        MediatorLiveData<Boolean>().apply {
            addSource(sqcPassed) {
                sqcPassedAudioMediator.value = it
                if (it) {
//                    disconnectionMonitor?.stopMonitoring()
//                    audioPlayer.playSqcPassed()
                }
            }
        }
    }
    private val sqcPassed = MutableLiveData<Boolean>()

    val lowBatteryWarning: LiveData<LiveEvent<Boolean>>
        get() = mutableLowBatteryWarning
    private val mutableLowBatteryWarning = MutableLiveData<LiveEvent<Boolean>>()

    val continueButtonVisible: LiveData<Boolean>
        get() = mutableContinueButtonVisible
    private val mutableContinueButtonVisible = MutableLiveData<Boolean>()

    val showDisconnectionAlert: LiveData<LiveEvent<Any>>
        get() = showDisconnectionAlertAudioMediator
    private val showDisconnectionAlertAudioMediator: MediatorLiveData<LiveEvent<Any>> by lazy {
        MediatorLiveData<LiveEvent<Any>>().apply {
            addSource(mutableShowDisconnectionAlert) {
//                audioPlayer.playBluetoothDisconnectionAlert()
                showDisconnectionAlertAudioMediator.value = it
            }
        }
    }
    private val mutableShowDisconnectionAlert = MutableLiveData<LiveEvent<Any>>()

    val skipButtonVisible: LiveData<Boolean>
        get() = mutableSkipButtonVisible
    private val mutableSkipButtonVisible = MutableLiveData<Boolean>()

    val onboardingContinueVisible: LiveData<Boolean>
        get() = mutableOnboardingContinueVisible
    private val mutableOnboardingContinueVisible = MutableLiveData<Boolean>()

    val busymindMode: LiveData<BusymindMode>
        get() = mutableBusymindMode
    private val mutableBusymindMode = MutableLiveData<BusymindMode>()

//    val dataTrackingState: LiveData<SessionDataTracker.TrackingState>
//        get() = mutableDataTrackingState
//    private val mutableDataTrackingState =
//        MutableLiveData(
//            dataTracker.dataTrackingState.value ?: SessionDataTracker.TrackingState.INIT
//        )
//
//    val durationTrackingState: LiveData<PlaybackState>
//        get() = mutableDurationTrackingState
//    private val mutableDurationTrackingState =
//        MutableLiveData(dataTracker.durationTrackingState.value ?: PlaybackState.INIT)

//    val museBatteryPercentage: Long
//        get() {
//            return museDeviceSelector.batteryPercentageRemaining ?: 0
//        }

    private val sqcPassRequirementsChecker = SqcPassRequirementsChecker()

    var isDebugModeOn = sqProcessor.debugOn
        set(value) {
            field = value
            if (value) {
//                disconnectionMonitor?.stopMonitoring()
            } else {
//                disconnectionMonitor?.startMonitoring(DISCONNECTION_TIMEOUT_SECONDS)
            }
            sqProcessor.debugOn = value
        }

    private val disposableBag = CompositeDisposable()
    var onboarding = false
    private var sqcStarted = false

    fun startSqc() {
        if (sqcStarted) return

        sqcStarted = true
//        trace.start()

        sqMonitor.sensorSignalQualityProgress
            .map {
                sqcPassRequirementsChecker.areAllSensorsGood(
                    SqcConfiguration.getConfiguration(museConnector.selectedMuseModel()),
                    it
                )
            }.filter { it }
            .take(1)
            .subscribe {
//                trace.stop(SqcPerformanceTrace.Result.ALL_SENSORS_GOOD)
                sqcPassed.postValue(true)
                if (it) {
                    mutableOnboardingContinueVisible.postValue(true)
                }
            }
            .let { disposableBag.add(it) }

        val startTimeMs = Date().time
        Observable.combineLatest(
            sqMonitor.sensorSignalQualityProgress,
            sqMonitor.sensorSignalQualityAntiprogress
        ) { progress, antiprogress ->
            val now = Date().time
            sqcPassRequirementsChecker.areMinSensorsGood(
                progress,
                antiprogress
            ) && (now - startTimeMs) > MIN_MS_IN_SQC
        }.distinctUntilChanged()
            .subscribe {
                if (lenientSqcEnabled && it
//                    && sessionMuse?.museModel?.isMuseS == true
                    ) {
//                    Analytics.instance.logPassMinSqc(onboarding, sessionId)
                    mutableContinueButtonVisible.value = true
                    mutableOnboardingContinueVisible.postValue(true)
                } else {
                    mutableContinueButtonVisible.value = false
                }
            }.let { disposableBag.add(it) }

        museBatteryMonitor?.batteryPercentage
            ?.map { LiveEvent(it <= FULL_BATTERY_IMAGE_THRESHOLD) }
            ?.distinct()
            ?.subscribe(mutableLowBatteryWarning::postValue)
            ?.let { disposableBag.add(it) }

        museConnector.museConnectionState.distinctUntilChanged()
            .subscribe { state ->
                if (!state.isOnline()) {
                    mutableShowDisconnectionAlert.postValue(LiveEvent(Any()))
                }
            }?.let { disposableBag.add(it) }

//        disconnectionMonitor?.disconnectionStatus
//            ?.distinctUntilChanged()
//            ?.subscribe { (disconnected, _) ->
//                if (disconnected) {
//                    mutableShowDisconnectionAlert.postValue(LiveEvent(Any()))
//                }
//            }?.let { disposableBag.add(it) }

        busymind.busymindModeObservable
            .subscribe {
                mutableBusymindMode.postValue(it)
            }.let { disposableBag.add(it) }

//        dataTracker.dataTrackingState.subscribe {
//            mutableDataTrackingState.postValue(it)
//        }.let { disposableBag.add(it) }
//
//        dataTracker.durationTrackingState.subscribe {
//            mutableDurationTrackingState.postValue(it)
//        }.let { disposableBag.add(it) }

        Observable.timer(10, TimeUnit.SECONDS)
            .subscribe {
                mutableSkipButtonVisible.postValue(true)
            }.let { disposableBag.add(it) }
    }

//    fun stopTrace(result: SqcPerformanceTrace.Result) = trace.stop(result)

    override fun onCleared() {
        super.onCleared()
        disposableBag.clear()
//        audioPlayer.stop()
//        audioPlayer.unload()
//        trace.stop(SqcPerformanceTrace.Result.EXIT_SQC)
    }

    fun onTestEegSignalChanged(values: Map<SqcSensor, Double>) {
        if (isDebugModeOn) {
            sqProcessor.eegSensorIndicatorValues.onNext(values)
            sqProcessor.eegConveyorValues.onNext(
                values.mapValues { sqcValueToSignalQuality(it.value) }
            )
        }
    }

    fun onTestDrlRefConnectionStatusChanged(connected: Boolean) {
        if (isDebugModeOn) {
            sqProcessor.drlRefGood.onNext(connected)
        }
    }

    fun onTestPpgSensorStatusChanged(isGood: Boolean) {
        if (isDebugModeOn) {
            sqProcessor.ppgConveyorValue.onNext(isGood)
            sqProcessor.ppgSensorIndicatorValue.onNext(isGood)
        }
    }

    fun onTestHeartbeatDetected() {
        if (isDebugModeOn) {
            sqProcessor.heartbeatDetected.onNext(true)
        }
    }

    fun resume() {
        busymind.busymindMode = BusymindMode.SIGNAL_TEST
//        disconnectionMonitor?.startMonitoring(DISCONNECTION_TIMEOUT_SECONDS)
    }

    fun pause() {
        busymind.busymindMode = BusymindMode.IDLE
//        disconnectionMonitor?.stopMonitoring()
    }


    fun playAudio() {
//        audioPlayer.load()
//        audioPlayer.play()
    }

    fun stopAudio() {
//        audioPlayer.stop()
//        audioPlayer.unload()
    }

    fun pauseAudio() {
//        audioPlayer.pause()
    }
    fun resumeAudio(){
//        audioPlayer.resume()
    }

    fun resetSqcValues() {
        sqMonitor.restart()
    }
//
//    fun stopTrackingAlertRecovery(
//        type: Analytics.MuseAdverseEventType,
//        how: Analytics.ResumedAdverseEventTrigger
//    ) = alertRecoveryAnalytics.stopTrackingAlertRecovery(type, how)
}