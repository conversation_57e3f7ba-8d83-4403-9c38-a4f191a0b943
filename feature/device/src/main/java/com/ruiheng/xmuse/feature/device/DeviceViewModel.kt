package com.ruiheng.xmuse.feature.device

import androidx.lifecycle.ViewModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class DeviceViewModel @Inject constructor(
    private val netResourcesUserRepository: NetResourcesUserRepository,
    private val museConnector: MuseConnector
) :
    ViewModel() {

    var latestConnected: RequestDeviceBindResult? = null
    fun updateCurrentDevice(deviceBindResult: RequestDeviceBindResult) {
        latestConnected = deviceBindResult
    }

//    val museListScanLiveData = museDeviceRepository.observerScanList()

//    @SuppressLint("MissingPermission")
//    fun startMuseDeviceScan():Boolean{
//        if (Utils.getApp().isBluetoothEnabled()){
//            museConnector.turnOnDiscovery()
//            return true
//        }else{
//            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
//            ActivityUtils.getTopActivity().startActivity(enableBtIntent)
//            return false
//        }
//    }


//    fun disconnectMuseDevice() = museDeviceRepository.disconnectMuseDevice()
//
//    val connectedXmuseInfoLiveData = museDeviceRepository.obseverXmuseInfoLiveData()
//    val connectedMuseLiveData = museDeviceRepository.obseverXmuseConnectLiveData()
//
//    fun startMuseFileCache() = museDeviceRepository.startMuseFileCache()
//
//    fun saveMuseFileCache() = museDeviceRepository.saveMuseFileCache()

//    fun initMuseRealtimeModel() = museDeviceRepository.initMuseRealtimeModel().stateIn(
//        scope = viewModelScope,
//        started = SharingStarted.Eagerly,
//        initialValue = Result.Loading(),
//    )

//    fun startBindDevice(
//        name: String,
//        sn: String,
//        firmwareVersion: String,
//        model: String,
//        bluetoothMac: String
//    ) = netResourcesUserRepository.bindDevice(name, sn, firmwareVersion, model, bluetoothMac)
//        .stateIn(
//            scope = viewModelScope,
//            started = SharingStarted.Eagerly,
//            initialValue = Result.Loading(),
//        )
//
//    fun getCurrentMuse() = museConnector.getCurrentMuse()
}
