package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor

fun SqcSensor.startAngle(hasPpg: Boolean): Float {
    return if (hasPpg) {
        when (this) {
            SqcSensor.EEG_LEFT_BACK -> 162f
            SqcSensor.EEG_LEFT_FRONT -> 205f
            SqcSensor.EEG_RIGHT_BACK -> 336f
            SqcSensor.EEG_RIGHT_FRONT -> 299f
            SqcSensor.PPG -> 288f
            SqcSensor.DRL_REF -> 247f
            SqcSensor.FNIRS_LEFT,
            SqcSensor.FNIRS_RIGHT -> 0f
        }
    } else {
        when (this) {
            SqcSensor.EEG_LEFT_BACK -> 162f
            SqcSensor.EEG_LEFT_FRONT -> 205f
            SqcSensor.EEG_RIGHT_BACK -> 336f
            SqcSensor.EEG_RIGHT_FRONT -> 291f
            SqcSensor.PPG -> 0f
            SqcSensor.DRL_REF -> 250f
            SqcSensor.FNIRS_LEFT,
            SqcSensor.FNIRS_RIGHT -> 0f
        }
    }
}

fun SqcSensor.endAngle(hasPpg: Boolean): Float {
    return if (hasPpg) {
        when (this) {
            SqcSensor.EEG_LEFT_BACK -> 204f
            SqcSensor.EEG_LEFT_FRONT -> 247f
            SqcSensor.EEG_RIGHT_BACK -> 378f
            SqcSensor.EEG_RIGHT_FRONT -> 335f
            SqcSensor.PPG -> 298f
            SqcSensor.DRL_REF -> 287f
            SqcSensor.FNIRS_LEFT,
            SqcSensor.FNIRS_RIGHT -> 0f
        }
    } else {
        when (this) {
            SqcSensor.EEG_LEFT_BACK -> 204f
            SqcSensor.EEG_LEFT_FRONT -> 247f
            SqcSensor.EEG_RIGHT_BACK -> 378f
            SqcSensor.EEG_RIGHT_FRONT -> 335f
            SqcSensor.PPG -> 0f
            SqcSensor.DRL_REF -> 290f
            SqcSensor.FNIRS_LEFT,
            SqcSensor.FNIRS_RIGHT -> 0f
        }
    }
}

fun SqcSensor.sweepAngle(hasPpg: Boolean): Float {
    return endAngle(hasPpg) - startAngle(hasPpg)
}