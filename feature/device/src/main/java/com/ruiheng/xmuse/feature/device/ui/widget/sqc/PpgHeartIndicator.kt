package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat
import com.ruiheng.xmuse.feature.device.R


class PpgHeartIndicator @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val ppgOnAnimation by lazy {
        AnimatedVectorDrawableCompat.create(context, R.drawable.ppg_heart_from_off_to_on)
    }

    private val pulseAnimation by lazy {
        ObjectAnimator.ofPropertyValuesHolder(
            this,
            PropertyValuesHolder.ofFloat("scaleX", 1.2f),
            PropertyValuesHolder.ofFloat("scaleY", 1.2f)
        ).apply {
            duration = 150L
            repeatCount = 1
            repeatMode = ObjectAnimator.REVERSE
        }
    }

    private var heartOn: Boolean = false

    init {
        setImageResource(R.drawable.img_sqc_ppg_heart)
    }

    fun animateHeartbeat() {
        if (!heartOn) { // turn the heart red on the first beat
            setImageDrawable(ppgOnAnimation)
            drawable?.animateAsVectorDrawable({ heartOn = true })
            return
        }

        pulseAnimation.start()
    }

    fun onSqcPassed() {
        stopPulseAnimation()
    }

    private fun stopPulseAnimation() {
        pulseAnimation.cancel()
        scaleX = 1.0F
        scaleY = 1.0F
    }

}