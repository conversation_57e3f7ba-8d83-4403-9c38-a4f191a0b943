package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import com.ruiheng.xmuse.feature.device.databinding.LayoutSqcSensorIndicatorsBinding


class SensorSignalQualityIndicatorsLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding =
        LayoutSqcSensorIndicatorsBinding.inflate(LayoutInflater.from(context), this, true)

    override fun onLayout(changed: <PERSON><PERSON><PERSON>, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        val size = ((right - left) * 0.05).toInt()
        listOf(
            binding.vSensorDrlRef,
            binding.vSensorLeftBack,
            binding.vSensorLeftFront,
            binding.vSensorRightBack,
            binding.vSensorRightFront,
            binding.vSensorPpg
        ).forEach {
            if (it.layoutParams.width != size || it.layoutParams.height != size) {
                it.layoutParams = it.layoutParams.apply {
                    width = size
                    height = size
                }
            }
        }
    }

    fun setupIndicators(hasPpg: Boolean) {
        ConstraintSet().apply {
            clone(binding.clSqcSensorIndicatorsParent)

            listOf(
                binding.vSensorDrlRef,
                binding.vSensorLeftBack,
                binding.vSensorLeftFront,
                binding.vSensorRightBack,
                binding.vSensorRightFront,
                binding.vSensorPpg
            ).forEach {
                tintBackground(it, com.ruiheng.xmuse.core.ui.R.color.white)
            }

            if (hasPpg) {
                setVisibility(binding.vSensorPpg.id, VISIBLE)
                mapOf(
                    binding.vSensorDrlRef to Pair(0.488F, 0.34F),
                    binding.vSensorLeftBack to Pair(0.246F, 0.745F),
                    binding.vSensorLeftFront to Pair(0.33F, 0.435F),
                    binding.vSensorRightFront to Pair(0.68F, 0.46F),
                    binding.vSensorRightBack to Pair(0.755F, 0.742F),
                    binding.vSensorPpg to Pair(0.61F, 0.375F)
                )
            } else {
                setVisibility(binding.vSensorPpg.id, GONE)
                mapOf(
                    binding.vSensorDrlRef to Pair(0.503F, 0.34F),
                    binding.vSensorLeftBack to Pair(0.248F, 0.744F),
                    binding.vSensorLeftFront to Pair(0.335F, 0.42F),
                    binding.vSensorRightFront to Pair(0.67F, 0.438F),
                    binding.vSensorRightBack to Pair(0.753F, 0.744F)
                )
            }.forEach {
                setHorizontalBias(it.key.id, it.value.first)
                setVerticalBias(it.key.id, it.value.second)
            }
            applyTo(binding.clSqcSensorIndicatorsParent)
        }
    }

    private fun tintBackground(indicator: SqcSensorIndicator, colorRes: Int) {
        indicator.background.setTint(ContextCompat.getColor(context, colorRes))
    }

    fun update(hsiValues: Map<SqcSensor, Double>) {
        hsiValues.forEach {
            updateSensor(it.key, it.value)
        }
    }

    private fun updateSensor(sensor: SqcSensor, signalValue: Double) {
        when (sensor) {
            SqcSensor.EEG_LEFT_BACK -> binding.vSensorLeftBack
            SqcSensor.EEG_LEFT_FRONT -> binding.vSensorLeftFront
            SqcSensor.EEG_RIGHT_BACK -> binding.vSensorRightBack
            SqcSensor.EEG_RIGHT_FRONT -> binding.vSensorRightFront
            SqcSensor.DRL_REF -> binding.vSensorDrlRef
            SqcSensor.PPG -> binding.vSensorPpg
            else -> null
        }?.setLatestValue(signalValue)
    }

}