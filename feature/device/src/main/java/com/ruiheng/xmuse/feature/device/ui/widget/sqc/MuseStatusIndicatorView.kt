package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.ruiheng.xmuse.core.common.result.findActivity
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.bluetooth_button.BluetoothState
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.status_indicator.MuseStatusIndicatorViewModel
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.databinding.ViewMuseStatusIndicatorBinding
import timber.log.Timber


open class MuseStatusIndicatorView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), LifecycleOwner, DefaultLifecycleObserver,
    BluetoothConnectionView {

    protected val binding =
        ViewMuseStatusIndicatorBinding.inflate(LayoutInflater.from(context), this, true)

    private val viewModel: MuseStatusIndicatorViewModel? by lazy {
        try {
            // 获取当前 Activity
            val activity = context.findActivity() ?: throw IllegalStateException("Activity not found")
            // 通过 Activity 获取 ViewModel
            ViewModelProvider(activity).get(MuseStatusIndicatorViewModel::class.java)
        }catch (e:Exception){
            null
        }
    }

    private val connectViewModel: BluetoothConnectionViewModel? by lazy {
        try {
            // 获取当前 Activity
            val activity = context.findActivity() ?: throw IllegalStateException("Activity not found")
            // 通过 Activity 获取 ViewModel
            ViewModelProvider(activity).get(BluetoothConnectionViewModel::class.java)
        }catch (e:Exception){
            null
        }
    }

    var onBluetoothClick: (() -> Unit)? = null

    var signalQualityOnClick: (() -> Unit)? = null

//    private var viewModel: SignalQualityViewModel? = null

    override var bluetoothStateCallback: ((state: BluetoothState) -> Unit)? = null

//    override var allowOnboardingStart: Boolean
//            by binding.bluetoothConnection::allowOnboardingStart

//    override var popupRootView: ViewGroup?
//            by binding.bluetoothConnection::popupRootView

//    override var leeroySelectedCallback: (() -> Unit)?
//            by binding.bluetoothConnection::leeroySelectedCallback

//    override var bluetoothAccessor: ActivityBluetoothAccessor?
//            by binding.bluetoothConnection::bluetoothAccessor

    //    override var checkHeadbandRequired: Boolean
//            by binding.bluetoothConnection::checkHeadbandRequired
//
//    override var btnPosition: BluetoothConnectionButton.ButtonPosition
//            by binding.bluetoothConnection::btnPosition
//
    private var _lifecycle: Lifecycle? = null

    override val lifecycle: Lifecycle
        get() = _lifecycle!!

    private var initialized = false
    val attach: OnAttachStateChangeListener by lazy {
        object : OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                if (!initialized) {
                    initialize()
                    removeOnAttachStateChangeListener(attach)
                    initialized = true
                }
            }

            override fun onViewDetachedFromWindow(v: View) {

            }
        }
    }

    init {
        addOnAttachStateChangeListener(attach)
    }

    private fun initialize() {
        // 获取 View 的 LifecycleOwner 并添加观察者
        val lifecycleOwner = findViewTreeLifecycleOwner()
        _lifecycle = lifecycleOwner?.lifecycle
        _lifecycle?.addObserver(this)
    }

    override fun onCreate(owner: LifecycleOwner) {
        binding.btnBluetoothConnection.showImage(R.drawable.ico_device_connect_btn)

        viewModel?.batteryPercentage?.observe(this) {
            binding.battery.setImageResource(getBatteryImage(it))
        }

        connectViewModel?.bluetoothState?.observe(this) { bluetoothState ->
            setupBluetoothButtonVisibility(bluetoothState)
        }

        binding.signalQuality.setOnClickListener {
            val connectState = connectViewModel?.bluetoothState?.value
            if (connectState is BluetoothState.Connected) {
                signalQualityOnClick?.invoke()
            } else {
                onBluetoothClick?.invoke()
            }
        }
        binding.btnBluetoothConnection.setOnClickListener {
            onBluetoothClick?.invoke()
        }

    }

    override fun onDestroy(owner: LifecycleOwner) {
        _lifecycle = null
    }

    private fun setupBluetoothButtonVisibility(bluetoothState: BluetoothState) {

        //判断当前是否有设备列表弹框或者连接引导的弹框
//        val isPopoverVisible =
//            binding.bluetoothConnection.bluetoothConnectPopover?.isPopoverVisible == true ||
//                    binding.bluetoothConnection.troubleshootMuseModelPopover?.isPopoverVisible == true
        val isPopoverVisible = false
        if (bluetoothState is BluetoothState.Connected && !isPopoverVisible) {
            binding.btnBluetoothConnection.visibility = View.INVISIBLE
            binding.signalQuality.visibility = View.VISIBLE
            binding.battery.visibility = View.VISIBLE
        } else {
            binding.btnBluetoothConnection.visibility = View.VISIBLE
            binding.battery.visibility = View.GONE
            binding.signalQuality.visibility = View.INVISIBLE
        }
    }

//    override fun doIfMuseRequirementsFulfilled(
//        requirement: MuseRequirement,
//        fragmentManager: FragmentManager,
//        actionIfFulfilled: () -> Unit
//    ) = binding.bluetoothConnection.doIfMuseRequirementsFulfilled(
//        requirement,
//        fragmentManager,
//        actionIfFulfilled
//    )
//
//    override fun dismissDeviceListPopover() = binding.bluetoothConnection.dismissDeviceListPopover()

//    override fun showBluetoothConnectionPopover(
//        popoverDismissedCallback: (() -> Unit)?,
//        deviceConnectedCallback: (() -> Unit)?
//    ) = binding.bluetoothConnection.showBluetoothConnectionPopover(
//        popoverDismissedCallback,
//        deviceConnectedCallback
//    )
//
//    override fun setSessionMuseToFilterFor(sessionMuse: UserSessionMuse?) =
//        binding.bluetoothConnection.setSessionMuseToFilterFor(sessionMuse)
}