package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.content.ContextWrapper
import android.content.res.ColorStateList
import android.graphics.drawable.AnimatedVectorDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.findViewTreeViewModelStoreOwner
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat
import com.ruiheng.xmuse.core.common.result.findActivity
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQualityQueue
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import com.ruiheng.xmuse.core.ui.helper.getColorSurface
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.SignalQualityViewModel
import com.ruiheng.xmuse.feature.device.databinding.LayoutSqcGraphBinding
import timber.log.Timber

class SignalQualityLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver, LifecycleOwner {

    private val binding = LayoutSqcGraphBinding.inflate(LayoutInflater.from(context), this, true)

    // 使用 lazy 初始化 ViewModel
    private val viewModel: SignalQualityViewModel? by lazy {
        try {
            // 获取当前 Activity
            val activity = context.findActivity() ?: throw IllegalStateException("Activity not found")
            // 通过 Activity 获取 ViewModel
            ViewModelProvider(activity).get(SignalQualityViewModel::class.java)
        }catch (e:Exception){
            null
        }
    }

    private var _lifecycle: Lifecycle? = null

    override val lifecycle: Lifecycle
        get() = _lifecycle!!
    private var initialized = false
    val attach: OnAttachStateChangeListener by lazy {
        object : OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                if (!initialized) {
                    initialize()
                    removeOnAttachStateChangeListener(attach)
                    initialized = true
                }
            }

            override fun onViewDetachedFromWindow(v: View) {

            }
        }
    }

    init {
        addOnAttachStateChangeListener(attach)
    }

    private fun initialize() {
        // 获取 View 的 LifecycleOwner 并添加观察者
        val lifecycleOwner = findViewTreeLifecycleOwner()
        _lifecycle = lifecycleOwner?.lifecycle
        _lifecycle?.addObserver(this)
    }


    override fun onCreate(owner: LifecycleOwner) {
//        visibility = VISIBLE
        viewModel?.heartbeatDetected?.observe(this) {
            showHeartbeat()
//            if (it.value == true){
//
//            }
//            it.getContentIfNotHandled()
        }

        viewModel?.sqcConfiguration?.observe(this) { sqcConfiguration ->
            setupConveyorBelt(sqcConfiguration)
        }

        viewModel?.sensorSignalQualityQueues?.observe(this) {
            updateConveyorQueues(it)
        }
        viewModel?.sensorContactStrengths?.observe(this) {
            updateSensorIndicators(it)
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        _lifecycle = null
        cancelAnimation()
        viewModel?.heartbeatDetected?.removeObservers(this)
        viewModel?.sqcConfiguration?.removeObservers(this)
        viewModel?.sensorSignalQualityQueues?.removeObservers(this)
        viewModel?.sensorContactStrengths?.removeObservers(this)
//        viewModel = null
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)

        val scale = right - left
        binding.ppgHeartIndicator.apply {
            val size = (scale * 0.075).toInt()
            if (layoutParams.width != size || layoutParams.height != size) {
                layoutParams = layoutParams.apply {
                    width = size
                    height = size
                }
            }
        }
        binding.ivAnimatedCheckmark.apply {
            val size = (scale * 0.3F).toInt()
            if (layoutParams.width != size || layoutParams.height != size) {
                layoutParams = layoutParams.apply {
                    width = size
                    height = size
                }
            }
        }
    }

    private fun updateConveyorQueues(queues: Map<SqcSensor, SignalQualityQueue>) {
        binding.layoutConveyorBelt.conveyorQueues = queues
    }

    private fun updateSensorIndicators(values: Map<SqcSensor, Double>) {
        binding.layoutSensorIndicators.update(values)
        values.map { (sqcSensor, value) ->
            val originalColor = ContextCompat.getColor(
                context,
                when (sqcSensor) {
                    SqcSensor.PPG -> R.color.heart_red
                    SqcSensor.FNIRS_LEFT,
                    SqcSensor.FNIRS_RIGHT -> R.color.session_sqc_color_fnirs

                    else -> com.ruiheng.xmuse.core.ui.R.color.white
                }
            )
            val alphaValue = (255.0 * value).toInt()
            val colorWithAlpha = ColorUtils.setAlphaComponent(originalColor, alphaValue)
            Pair(sqcSensor, colorWithAlpha)
        }.map { (sqcSensor, colour) ->
            Pair(
                when (sqcSensor) {
                    SqcSensor.PPG -> binding.fnirsMiddleImage
                    SqcSensor.FNIRS_LEFT -> binding.fnirsLeftImage
                    SqcSensor.FNIRS_RIGHT -> binding.fnirsRightImage
                    else -> null
                }, colour
            )
        }.forEach { (view, colour) ->
            view?.imageTintList = ColorStateList.valueOf(colour)
        }
    }

    private fun setupConveyorBelt(sqcConfiguration: SqcConfiguration) {
        val displayConveyorsWithPpg = when (sqcConfiguration) {
            SqcConfiguration.EEG,
            SqcConfiguration.EEG_PPG_FNIRS -> false

            SqcConfiguration.EEG_PPG -> true
        }

        val backImage = if (displayConveyorsWithPpg) {
            R.drawable.sqc_outline_with_ppg01
        } else {
            R.drawable.sqc_outline01
        }

        binding.layoutConveyorBelt.hasPpg = displayConveyorsWithPpg
        binding.ivConveyorBeltFrame.setImageResource(backImage)
        binding.layoutSensorIndicators.setupIndicators(displayConveyorsWithPpg)
        binding.ppgHeartIndicator.visibility =
            if (displayConveyorsWithPpg) View.VISIBLE else View.GONE

        binding.fnirsRightImage.showImage(R.drawable.img_ppg_fnirs_right)
        binding.fnirsLeftImage.showImage(R.drawable.img_ppg_fnirs_left)
        binding.fnirsMiddleImage.showImage(R.drawable.img_ppg_fnirs_middle)
        binding.fnirsRightImageBg.showImage(R.drawable.img_ppg_fnirs_right)
        binding.fnirsLeftImageBg.showImage(R.drawable.img_ppg_fnirs_left)
        binding.fnirsMiddleImageBg.showImage(R.drawable.img_ppg_fnirs_middle)
        listOf(
            binding.fnirsLeftImage,
            binding.fnirsMiddleImage,
            binding.fnirsRightImage,
            binding.fnirsLeftImageBg,
            binding.fnirsMiddleImageBg,
            binding.fnirsRightImageBg
        ).forEach {
            it.visibility = when (sqcConfiguration) {
                SqcConfiguration.EEG,
                SqcConfiguration.EEG_PPG -> View.GONE

                SqcConfiguration.EEG_PPG_FNIRS -> View.VISIBLE
            }
        }

    }

    private fun showHeartbeat() {
        binding.ppgHeartIndicator.animateHeartbeat()
    }

    fun drawCheckmark(animationEndListener: () -> Unit) {
        val drawable = binding.ivAnimatedCheckmark.drawable
        drawable.applyColorFilter(R.color.session_sqc_color_good, context)
        binding.ivAnimatedCheckmark.visibility = View.VISIBLE
        drawable.animateAsVectorDrawable(animationEndListener)
        if (binding.ppgHeartIndicator.visibility == View.VISIBLE) {
            binding.ppgHeartIndicator.onSqcPassed()
        }
    }

    private fun cancelAnimation() {
        when (val drawable = binding.ivAnimatedCheckmark.drawable) {
            is AnimatedVectorDrawableCompat -> {
                drawable.clearAnimationCallbacks()
                drawable.stop()
            }

            is AnimatedVectorDrawable -> {
                drawable.clearAnimationCallbacks()
                drawable.stop()
            }
        }
    }

}