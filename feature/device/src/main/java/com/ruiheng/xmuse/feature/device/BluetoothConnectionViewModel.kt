package com.ruiheng.xmuse.feature.device

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseModel
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.LiveEvent
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseBatteryPercentageMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MusePresetSetter
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlatformFeatureFlags
import dagger.hilt.android.lifecycle.HiltViewModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.bluetooth_button.BluetoothState
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.feature.device.vo.DeviceInfoMenuItem
import com.ruiheng.xmuse.feature.device.vo.getDeviceInfoPageList
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class BluetoothConnectionViewModel @Inject constructor(
    private val museManager: MuseManager,
    private val museConnector: MuseConnector,
    private val museBatteryPercentageMonitor: MuseBatteryPercentageMonitor,
    private val netResourcesUserRepository: NetResourcesUserRepository,
//    private var sessionMuse: UserSessionMuse?,
//    private val userPrefsRepo: UserPreferencesRepository,
    private val musePresetSetter: MusePresetSetter,
//    private val simulatedMuseController: SimulatedMuseController
) : ViewModel() {
//    private val featureFlags: PlatformFeatureFlags =

    enum class FacadeAction { ShowMuseConnected }

    val facadeAction: LiveData<LiveEvent<FacadeAction>>
        get() = mutableFacadeAction
    private val mutableFacadeAction = MutableLiveData<LiveEvent<FacadeAction>>()

    val bluetoothState: LiveData<BluetoothState>
        get() = mutableBluetoothState
    private val mutableBluetoothState = MutableLiveData<BluetoothState>(BluetoothState.Select)

    val museInfo: LiveData<List<DeviceInfoMenuItem>> get() = _museInfo

    private val _museInfo = MutableLiveData<List<DeviceInfoMenuItem>>(emptyList())

    val allMusesDiscovered: LiveData<Result<List<Muse>>>
        get() = mutableAllMusesDiscovered
    private val mutableAllMusesDiscovered = MutableLiveData<Result<List<Muse>>>(Result.Loading())

//    val selectedMuseModel: MuseDeviceModel?
//        get() = museDeviceSelector.selectedMuseModel

//    val isMuseNeedingUpdate: Boolean
//        get() {
//            return !simulatedMuseController.isSimulatedMuseOn &&
//                    (museDeviceSelector.isNeedingUpdate() || !isMuseUsingMinimumFirmwareVersion())
//        }

    var launchOnboardingOnClick: Boolean = false
        private set

    var hasSeenDeviceDisconnectedPopover = true

    private val disposables = CompositeDisposable()

    init {
        museConnector.bluetoothState
            .distinctUntilChanged()
            .subscribe {
                if (it == com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.CONNECTED) {
                    mutableFacadeAction.postValue(LiveEvent(FacadeAction.ShowMuseConnected))
                }
            }.let { disposables.add(it) }

        Observable.combineLatest(
            museConnector.bluetoothState,
            museBatteryPercentageMonitor.batteryPercentage
        ) { legacyState, batteryPercentage ->
            when (legacyState) {
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.SELECT -> BluetoothState.Select
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.SEARCHING -> BluetoothState.Searching
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.CONNECTED -> BluetoothState.Connected(
                    batteryPercentage
                )
            }
        }.distinctUntilChanged()
            .subscribe { mutableBluetoothState.postValue(it) }
            .let { disposables.add(it) }

        Observable.combineLatest(
            museConnector.bluetoothState,
            museBatteryPercentageMonitor.batteryPercentage
        ) { legacyState, batteryPercentage ->
            when (legacyState) {
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.SELECT -> {
                    val muse = museConnector.getCurrentMuse()
                    muse?.getDeviceInfoPageList()?: emptyList()
                }
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.SEARCHING -> emptyList()
                com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState.CONNECTED ->{
                    val muse = museConnector.getCurrentMuse()
                    muse?.getDeviceInfoPageList(batteryPercentage)?: emptyList()
                }
            }
        }.distinctUntilChanged()
            .subscribe { _museInfo.postValue(it) }
            .let { disposables.add(it) }

        museConnector.muses
            .subscribe { musesResult ->
                val result = when (musesResult) {
                    is Result.Success -> Result.Success(filterMuse(musesResult.data))
                    is Result.Error -> Result.Error(musesResult.exception, musesResult.data)
                    else -> Result.Loading(musesResult.data)
                }
                mutableAllMusesDiscovered.postValue(result)
            }.let { disposables.add(it) }

//        userPrefsRepo.getMeditateBanner()
//            .subscribe {
//                launchOnboardingOnClick = it == BannerType.NO_HEADBAND
//            }.let { disposables.add(it) }
//
//        userPrefsRepo.getFlags()
//            .subscribe {
//                hasSeenDeviceDisconnectedPopover =
//                    it.contains(Flag.SEEN_DEVICE_DISCONNECTED_POPOVER.key())
//            }.let { disposables.add(it) }
    }

    fun createConnectionAttempt(muse: Muse): LiveData<Result<Muse?>> {
        val museConnectResult = MutableLiveData<Result<Muse?>>(Result.Loading())
        museConnector.createConnectionAttempt(muse).subscribe { connectResult ->
            museConnectResult.postValue(connectResult)
        }.let { disposables.add(it) }
        return museConnectResult
    }

    fun turnOnDiscovery() {
        museConnector.turnOnDiscovery()
    }

    override fun onCleared() {
        disposables.clear()
    }


//    fun connectMuseByName(name: String?) {
//        name ?: return
//        museConnector.connectMuse(name)
//    }

    fun logUserBtOpenMenu() {
//        Analytics.instance.logBtOpenMenu()
    }

//    fun filterMusesForSession(sessionMuse: UserSessionMuse?) {
//        this.sessionMuse = sessionMuse
//        mutableAllMusesDiscovered.postValue(ArrayList(filterMuse(museConnector.getAllMuses())))
//    }

    private fun filterMuse(muses: List<Muse>?): List<Muse> {
//        val sessionMuse = null
//        val filteredMuse = if (sessionMuse != null) {
//            ArrayList(muses?.filter { muse ->
//                val alphanumericMac = muse.macAddress.filter { it.isLetterOrDigit() }.uppercase()
//                alphanumericMac == sessionMuse.macAddress?.uppercase()
//            } ?: arrayListOf())
//        } else {
//            muses ?: arrayListOf()
//        }
        val filteredMuse = muses ?: arrayListOf()
        return filteredMuse
    }

//    fun setSeenDeviceDisconnectedPopover() {
//        userPrefsRepo.addFlag(Flag.SEEN_DEVICE_DISCONNECTED_POPOVER.key())
//    }

//    private fun isMuseUsingMinimumFirmwareVersion(): Boolean {
//        val muse = museConnector.getCurrentMuse()
//        return if (muse?.model == MuseModel.MS_03) {
//            val firmwareVersion = muse.museVersion.firmwareVersion
//            val defaultMinimumAthenaVersion = "3.0.19"
//            val minVersion = featureFlags.getFeatureConfiguration(
//                "min_athena_fw_version",
//                defaultMinimumAthenaVersion
//            )
//            compareVersions(firmwareVersion, minVersion) >= 0
//        } else {
//            true
//        }
//    }

    private fun compareVersions(version1: String, version2: String): Int {
        val parts1 = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val parts2 = version2.split(".").map { it.toIntOrNull() ?: 0 }

        val maxLength = maxOf(parts1.size, parts2.size)
        for (i in 0 until maxLength) {
            val v1 = parts1.getOrElse(i) { 0 }
            val v2 = parts2.getOrElse(i) { 0 }
            if (v1 != v2) {
                return v1.compareTo(v2)
            }
        }
        return 0
    }

    fun setMusePresetMode(presetMode: MusePresetSetter.PresetMode) {
        musePresetSetter.setPresetMode(presetMode)
    }

    fun isMuseUsingCorrectPreset(presetMode: MusePresetSetter.PresetMode): Boolean {
        val muse = museConnector.getCurrentMuse()
        return (muse != null &&
                muse.museConfiguration.preset ==
                musePresetSetter.getSelectedPreset(muse.model, presetMode))
//       || simulatedMuseController.isSimulatedMuseOn

    }

    fun getSelectedMuse() = museConnector.getCurrentMuse()

    fun enableMuseUsage() = museManager.enableMuseUsage()


    fun startBindAndCheckDevice(muse: Muse) = netResourcesUserRepository.bindDevice(
        muse.name,
        muse.museConfiguration.serialNumber,
        muse.museVersion.firmwareVersion,
        muse.model.name,
        muse.macAddress
    ).stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = Result.Loading(),
    )
}