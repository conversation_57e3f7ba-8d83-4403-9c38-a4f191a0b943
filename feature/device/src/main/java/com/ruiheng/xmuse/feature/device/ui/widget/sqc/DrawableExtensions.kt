package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedVectorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import androidx.annotation.ColorRes
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat


fun Drawable.applyColorFilter(@ColorRes colorResId: Int, context: Context) {
    colorFilter =
        PorterDuffColorFilter(ContextCompat.getColor(context, colorResId), PorterDuff.Mode.SRC_IN)
}

fun Drawable.animateAsVectorDrawable(
    animationEndListener: (() -> Unit)? = null,
    loop: Boolean = false
) {

    when (this) {
        is AnimatedVectorDrawableCompat -> {
            registerAnimationCallback(object : Animatable2Compat.AnimationCallback() {
                override fun onAnimationEnd(drawable: Drawable?) {
                    super.onAnimationEnd(drawable)
                    animationEndListener?.invoke()
                    if (loop) {
                        start()
                    } else {
                        unregisterAnimationCallback(this)
                    }
                }
            })
            start()
        }
        is AnimatedVectorDrawable -> {
            registerAnimationCallback(object : Animatable2.AnimationCallback() {
                @RequiresApi(Build.VERSION_CODES.M)
                override fun onAnimationEnd(drawable: Drawable?) {
                    super.onAnimationEnd(drawable)
                    animationEndListener?.invoke()
                    if (loop) {
                        start()
                    } else {
                        unregisterAnimationCallback(this)
                    }
                }
            })
            start()
        }
    }
}