package com.ruiheng.xmuse.feature.device.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.ui.BaseLazyFragment
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.databinding.FragmentXmuseInfoBinding
import com.ruiheng.xmuse.feature.device.vo.DeviceInfoMenuItem
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class XMuseInfoFragment :
    BaseLazyFragment<FragmentXmuseInfoBinding>() {
    override fun doLazyBusiness() {

    }

    private val bluetoothConnectionViewModel: BluetoothConnectionViewModel by viewModels()

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentXmuseInfoBinding.inflate(inflater, container, false)

    private var infoAdapter: PublicNormalMenuRVAdapter<DeviceInfoMenuItem> by autoCleared()
    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        infoAdapter = PublicNormalMenuRVAdapter() { menu ->
//            when (menu.title) {
//                R.string.firmware_version -> {
//                    startNavPage(R.id.actionHomeToFirmwareUpdate)
//                }
//            }
        }
        binding.btnDisconnect.setOnClickListener(this)
        binding.btnChange.setOnClickListener(this)
        binding.rvDeviceInfo.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvDeviceInfo.adapter = infoAdapter

        bluetoothConnectionViewModel.museInfo.observe(this) { infoList ->
            infoAdapter.submitList(infoList)
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btnChange -> {
                (parentFragment as BottomMuseDialog).onChangeMuseClick()
            }

            binding.btnDisconnect -> {
                (parentFragment as BottomMuseDialog).onDisconnectMuseClick()
            }
        }
        super.onWidgetClick(view)
    }
}