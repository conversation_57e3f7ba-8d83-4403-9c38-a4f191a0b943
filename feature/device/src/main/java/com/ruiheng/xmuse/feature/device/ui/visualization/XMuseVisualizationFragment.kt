package com.ruiheng.xmuse.feature.device.ui.visualization

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.MuseDataPacketType
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.YAxis.AxisDependency
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.google.android.material.chip.Chip
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseLazyFragment
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.databinding.FragmentBottomVisualizationBinding
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository.Companion.visualizationViewBloodOxygen
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository.Companion.visualizationViewHeartSize
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository.Companion.visualizationViewSize
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class XMuseVisualizationFragment : BaseLazyFragment<FragmentBottomVisualizationBinding>() {
    override fun doLazyBusiness() {

    }

    interface HeightCallback {
        fun onHeightReady(height: Int)
    }

    private var heightCallback: HeightCallback? = null

    fun setHeightCallback(callback: HeightCallback) {
        heightCallback = callback
    }

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentBottomVisualizationBinding.inflate(inflater, container, false)


    private val xmuseVisualizationViewViewModel: XmuseVisualizationViewViewModel by viewModels()

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btnRecord.setOnClickListener(this)
        binding.btnRecordStop.setOnClickListener(this)

        binding.imgCurrentState.showImage(R.drawable.ico_tab_brain)
        binding.imgHeartRate.showImage(R.drawable.ico_tab_heart_beat)
        binding.imgBloodOxygen.showImage(R.drawable.ico_tab_blood_oxygen)
        binding.layoutHeartRateContainer.setOnClickListener(this)
        binding.layoutCurrentStateContainer.setOnClickListener(this)
//        binding.layoutBloodOxygenContainer.setOnClickListener(this)
        updateCurrentTab(0)

        binding.chartBloodOxygen.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(false)
            isScaleXEnabled = true
            isScaleYEnabled = false
            setDrawGridBackground(false)
            val data = LineData()
            data.setValueTextColor(Color.BLACK)
            setData(data)
            legend.isEnabled = false
            xAxis.apply {

                isEnabled = false
            }
            axisLeft.apply {
                val color = getThemeColor(com.google.android.material.R.attr.colorOnSurfaceVariant)
                    ?: Color.GRAY
                textColor = color
                axisMaximum = 100f
                axisMinimum = 50f
                setDrawGridLines(true)
            }
            axisRight.isEnabled = false
        }

        binding.chartHeartRate.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(false)
            isScaleXEnabled = true
            isScaleYEnabled = false
            setDrawGridBackground(false)
            val data = LineData()
            data.setValueTextColor(Color.BLACK)
            setData(data)
            legend.isEnabled = false
            xAxis.apply {

                isEnabled = false
            }
            axisLeft.apply {
                val color = getThemeColor(com.google.android.material.R.attr.colorOnSurfaceVariant)
                    ?: Color.GRAY
                textColor = color
                axisMaximum = 200f
                axisMinimum = 0f
                setDrawGridLines(true)
            }
            axisRight.isEnabled = false
        }

        binding.chart.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(false)
            isScaleXEnabled = true
            isScaleYEnabled = false
            setDrawGridBackground(false)

            val data = LineData()
            data.setValueTextColor(Color.BLACK)
            setData(data)
            legend.isEnabled = false
//            legend.apply {
//
//                form = Legend.LegendForm.LINE
//                textColor = Color.BLUE
//            }
            xAxis.apply {
//                textColor = Color.BLUE
//                setDrawGridLines(false)
//                setAvoidFirstLastClipping(true)
                isEnabled = false
            }
            axisLeft.apply {
                val color = getThemeColor(com.google.android.material.R.attr.colorOnSurfaceVariant)
                    ?: Color.GRAY
                textColor = color
                axisMaximum = 0.8f
                axisMinimum = 0f
                setDrawGridLines(true)
            }
            axisRight.isEnabled = false
        }

        var count: Int = visualizationViewSize
        var countHeart: Int = visualizationViewHeartSize
        var countBloodOxygen: Int = visualizationViewBloodOxygen
        xmuseVisualizationViewViewModel.latestAvgPowsRawValueMapLiveData.observe(this) { valueMap ->
        }
//        xmuseVisualizationViewViewModel.lastBloodOxygenLiveData.observe(this) { blood ->
//            val text = if (blood <= 90) "准备中" else "${blood.toInt()}%"
//            binding.tvTitleBloodOxygen.text = text
//            binding.tvBloodOxygen.text = text
//            val data = binding.chartBloodOxygen.data ?: return@observe
//            val set = loadSet(
//                null,
//                binding.chartBloodOxygen,
//                visualizationViewBloodOxygen,
//                "bloodChart",
//                ContextCompat.getColor(Utils.getApp(), com.ruiheng.xmuse.core.ui.R.color.color5)
//            )
//            if ((set?.entryCount ?: 0) > visualizationViewBloodOxygen) {
//                set?.removeFirst()
//            }
//            set?.addEntry(Entry(countBloodOxygen.toFloat(), blood.toFloat()))
//            data.notifyDataChanged()
//            countBloodOxygen++
//            binding.chartBloodOxygen.notifyDataSetChanged()
//            binding.chartBloodOxygen.setVisibleXRangeMaximum(visualizationViewBloodOxygen.toFloat())
//            binding.chartBloodOxygen.moveViewToX((data.entryCount).toFloat())
//        }
//
        xmuseVisualizationViewViewModel.latestHeartBeatLiveData.observe(this) { heart ->
            val text =
                if (heart <= 45) getString(R.string.prepare) else getString(R.string.heart_beat_rate, heart.toInt())
            binding.tvTitleRate.text = text
            binding.tvHeartRate.text = text
            val data = binding.chartHeartRate.data ?: return@observe
            val set = loadSet(
                null,
                binding.chartHeartRate,
                visualizationViewHeartSize,
                "heartChart",
                ContextCompat.getColor(Utils.getApp(), com.ruiheng.xmuse.core.ui.R.color.color5)
            )
            if ((set?.entryCount ?: 0) > visualizationViewHeartSize) {
                set?.removeFirst()
            }
            set?.addEntry(Entry(countHeart.toFloat(), heart.toFloat()))
            data.notifyDataChanged()
            countHeart++
            binding.chartHeartRate.notifyDataSetChanged()
            binding.chartHeartRate.setVisibleXRangeMaximum(visualizationViewHeartSize.toFloat())
            binding.chartHeartRate.moveViewToX((data.entryCount).toFloat())
        }

//        xmuseVisualizationViewViewModel.latestAvgPowsRawRelativeValueMapLiveData.observe(this) { relativeMap ->
//            val data = binding.chart.data ?: return@observe
//            relativeMap.forEach { entry ->
//                val key = entry.key
//                val value = entry.value
//                val set = loadSet(
//                    key,
//                    binding.chart,
//                    visualizationViewSize,
//                    key.name,
//                    key.loadVisualizationLineColor()
//                )
//                val entryCount = (set?.entryCount ?: 0)
//                if (entryCount > visualizationViewSize) {
//                    set?.removeFirst()
//                }
//                set?.addEntry(Entry(count.toFloat(), value))
//                data.notifyDataChanged()
//            }
//            count++
//            binding.chart.notifyDataSetChanged()
//            binding.chart.setVisibleXRangeMaximum(visualizationViewSize.toFloat())
//            binding.chart.moveViewToX((data.entryCount).toFloat())
//        }
        val chipIdMap = mapOf(
            binding.chipAlpha.id to MuseDataPacketType.ALPHA_RELATIVE,
            binding.chipBeta.id to MuseDataPacketType.BETA_RELATIVE,
            binding.chipDelta.id to MuseDataPacketType.DELTA_RELATIVE,
            binding.chipGamma.id to MuseDataPacketType.GAMMA_RELATIVE,
            binding.chipTheta.id to MuseDataPacketType.THETA_RELATIVE
        )
        val width = (ScreenUtils.getScreenWidth() - 72.px) / 5
        chipIdMap.forEach { entry ->
            val key = entry.key
            val chip = binding.chipGroup.findViewById<Chip>(key)
            val layoutParam = chip.layoutParams
            layoutParam.width = width.toInt()
            chip.layoutParams = layoutParam
        }
        binding.chipGroup.setOnCheckedStateChangeListener { group, checkedIds ->
            val data = binding.chart.data ?: return@setOnCheckedStateChangeListener
            chipIdMap.forEach { entry ->
                val key = entry.key
                val value = entry.value
                val set = data.getDataSetByLabel(value.name, true)
                set?.isVisible = checkedIds.contains(key)
            }
            binding.chart.notifyDataSetChanged()
            binding.chart.invalidate()
        }

        contentView.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                contentView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val height = contentView.height
                heightCallback?.onHeightReady(height)
            }
        })
    }

    private fun loadSet(
        type: MuseDataPacketType?,
        char: LineChart,
        size: Int,
        name: String,
        lineColor: Int
    ): ILineDataSet? {
        val data = char.data ?: return null
        fun createSet() = LineDataSet(null, name).apply {
            axisDependency = AxisDependency.LEFT
            color = lineColor
            setCircleColor(Color.WHITE)
            lineWidth = 1.5f
            circleRadius = 4f
            fillAlpha = 65

//            fillColor = lineColor
            highLightColor = lineColor
            valueTextColor = Color.WHITE
            valueTextSize = 9f
//            if (type != null) {
//                val cacheQueue = xmuseVisualizationViewViewModel.latestQueue[type]
//                cacheQueue?.elements?.forEachIndexed { index, fl ->
//                    addEntry(Entry(index.toFloat(), fl))
//                }
//            }
            val drawable = ContextCompat.getDrawable(
                requireContext(),
                com.ruiheng.xmuse.core.ui.R.drawable.fade_chart
            )
            fillDrawable = drawable
            fillDrawable.setTint(lineColor)
//            setGradientColor(lineColor ,endColor)
            setDrawFilled(true)

            setDrawValues(false)
            setDrawCircles(false)
            data.addDataSet(this)
        }

        val set = data.getDataSetByLabel(name, true) ?: createSet()

        return set
    }

    private val tabCardView by lazy {
        arrayOf(
            binding.layoutCurrentStateContainer,
            binding.layoutHeartRateContainer,
            binding.layoutBloodOxygenContainer
        )
    }
    private val chartView by lazy {
        arrayOf(
            binding.chart,
            binding.chartHeartRate,
            binding.chartBloodOxygen
        )
    }
    private val titleView by lazy {
        arrayOf(
            binding.tvTitle,
            binding.tvTitleRate,
            binding.tvTitleBloodOxygen
        )
    }

    private fun updateCurrentTab(position: Int) {
        tabCardView.forEachIndexed { index, materialCardView ->
            materialCardView.strokeWidth = if (position == index) (2.px).toInt() else 0
        }
        chartView.forEachIndexed { index, lineChart ->
            lineChart.visibility = if (index == position) View.VISIBLE else View.GONE
        }
        titleView.forEachIndexed { index, appCompatTextView ->
            appCompatTextView.visibility = if (index == position) View.VISIBLE else View.INVISIBLE
        }
        binding.chipGroup.visibility = if (position == 0) View.VISIBLE else View.GONE

    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.layoutCurrentStateContainer -> {
                updateCurrentTab(0)
            }

            binding.layoutHeartRateContainer -> {
                updateCurrentTab(1)
            }

            binding.layoutBloodOxygenContainer -> {
                updateCurrentTab(2)
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}