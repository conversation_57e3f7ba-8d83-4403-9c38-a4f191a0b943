package com.ruiheng.xmuse.feature.device.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.tabs.TabLayoutMediator
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.feature.device.databinding.DialogBottomDeviceBinding
import com.ruiheng.xmuse.feature.device.ui.visualization.XMuseVisualizationFragment

class BottomMuseDialog :
    BaseBindingBottomDialogFragment<DialogBottomDeviceBinding>(),
    XMuseVisualizationFragment.HeightCallback {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomDeviceBinding.inflate(inflater, container, false)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        val titles = arrayOf("实时数据", "设备详情")
        binding.viewpager.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount() = 2
            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    0 -> {
                        val fragment = XMuseVisualizationFragment()
                        fragment.setHeightCallback(this@BottomMuseDialog) // 设置回调
                        fragment
                    }

                    1 -> {
                        XMuseInfoFragment()
                    }

                    else -> Fragment()
                }
            }
        }
        TabLayoutMediator(binding.tabTitle, binding.viewpager) { tab, position ->
            tab.text = titles[position]
        }.attach()
    }

    fun onChangeMuseClick(){
        //TODO 变更连接的设备
//        requireContext().requestDeviceBlePermission {
//            if (xmuseViewModel.startMuseDeviceScan()){
////                BottomMuseListDialog(xmuseViewModel).showMuseListDialog(parentFragmentManager)
//            }
//            dismiss()
//        }
    }

    fun onDisconnectMuseClick(){
        //TODO 断开设备连接
//        xmuseViewModel.disconnectMuseDevice()
//        dismiss()
    }

    override fun onHeightReady(height: Int) {
        val param = binding.viewpager.layoutParams
        param.height = height
        binding.viewpager.layoutParams = param
        (this.dialog as BottomSheetDialog).behavior.peekHeight =
            height + binding.layoutTopBar.height + (24.px).toInt()
    }

}