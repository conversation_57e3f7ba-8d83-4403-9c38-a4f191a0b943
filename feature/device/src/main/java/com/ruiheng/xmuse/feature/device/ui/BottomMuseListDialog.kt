package com.ruiheng.xmuse.feature.device.ui

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ruiheng.xmuse.core.common.result.bluetooth.isBluetoothEnabled
import com.ruiheng.xmuse.core.common.result.bluetooth.openBluetoothSettings
import com.ruiheng.xmuse.core.common.result.permission.requestDeviceBlePermission
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.core.ui.custom.PublicOptionNormalTextItemDecoration
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.databinding.DialogBottomMuseListBinding
import com.ruiheng.xmuse.feature.device.ui.adapter.BottomMuseAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BottomMuseListDialog() :
    BaseBindingBottomDialogFragment<DialogBottomMuseListBinding>() {

    private val bluetoothConnectionViewModel: BluetoothConnectionViewModel by viewModels()

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomMuseListBinding.inflate(inflater, container, false)


    private var mueseAdapter: BottomMuseAdapter by autoCleared()
    private val rotateAnimator: ObjectAnimator by lazy {
        ObjectAnimator.ofFloat(binding.imgBtnScan, "rotation", 0f, 360f).apply {
            duration = 2000 // 设置动画持续时间为 2 秒
            repeatCount = ObjectAnimator.INFINITE // 设置动画无限循环
        }
    }

    @SuppressLint("MissingPermission")
    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        mueseAdapter = BottomMuseAdapter { muse ->
            BottomMuseConnectDialog(muse) { result ->
                if (result.isSuccessWithData()) {
                    dismiss()
                }
            }.showDialog(childFragmentManager)
        }

        binding.rvMenu.adapter = mueseAdapter
        binding.rvMenu.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
//        binding.rvMenu.isNestedScrollingEnabled = false
        binding.rvMenu.addItemDecoration(PublicOptionNormalTextItemDecoration())
        binding.tvTitle.setOnClickListener(this)
        bluetoothConnectionViewModel.allMusesDiscovered.observe(this) { result ->
            val data = result.data
            mueseAdapter.submitList(data)
            binding.layoutScanning.visibility =
                if (data.isNullOrEmpty()) View.VISIBLE else View.GONE
            binding.loading.visibility = if (data.isNullOrEmpty()) View.GONE else View.VISIBLE
        }

        binding.imgBtn.showImage(R.drawable.ico_bt_scan_01)
        binding.imgBtnScan.showImage(R.drawable.ico_bt_scan_02)
        rotateAnimator.start()
        requireContext().requestDeviceBlePermission {
            if (!requireContext().isBluetoothEnabled()) {
                requireContext().openBluetoothSettings()
            } else {
                bluetoothConnectionViewModel.turnOnDiscovery()
            }
        }

    }

//    private val observerConnect = Observer<Result<Muse?>> { result ->
//        if (!result.isLoading()) {
//            connectLiveData?.removeObservers(this)
//            if (result.isSuccessWithData()) {
//                dismiss()
//            }
//        }
//    }
//
//    private var connectLiveData: LiveData<Result<Muse?>>? = null

//    private fun startConnect(muse: Muse) {
//        connectLiveData?.removeObservers(this)
//        connectLiveData = deviceViewModel.startConnectDevice(muse)
//        connectLiveData?.observe(this, observerConnect)
//    }

    override fun onWidgetClick(view: View) {
        when (view) {
        }
        super.onWidgetClick(view)
    }

    fun showMuseListDialog(supportFragmentManager: FragmentManager) {
        show(supportFragmentManager, this.javaClass.simpleName)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 停止动画并清除动画引用，防止内存泄漏
        rotateAnimator.apply {
            cancel()
            removeAllListeners()
        }
    }
}