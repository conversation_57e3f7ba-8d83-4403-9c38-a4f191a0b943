package com.ruiheng.xmuse.feature.device.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.ui.rv.DataBoundListAdapter
import com.ruiheng.xmuse.feature.device.databinding.ItemMuseListRowBinding

class BottomMuseAdapter(val onItemClickListener: (Muse) -> Unit) :
    DataBoundListAdapter<Muse, ItemMuseListRowBinding>(diffCallback = object :
        DiffUtil.ItemCallback<Muse>() {
        override fun areItemsTheSame(oldItem: Muse, newItem: Muse): Boolean {
            return oldItem.macAddress == newItem.macAddress
        }

        override fun areContentsTheSame(oldItem: Muse, newItem: Muse) =
            oldItem.macAddress == newItem.macAddress
                    && oldItem.connectionState == newItem.connectionState

    }) {
    override fun createBinding(parent: ViewGroup): ItemMuseListRowBinding {
        val binding = ItemMuseListRowBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        binding.root.setOnClickListener {
            if (isFastClick()) return@setOnClickListener
            binding.muse.let { muse ->
                if (muse!!.connectionState == ConnectionState.CONNECTED){
                    return@setOnClickListener
                }
                onItemClickListener.invoke(muse!!)
            }
        }
        return binding
    }

    override fun bind(binding: ItemMuseListRowBinding, item: Muse, position: Int) {
        binding.muse = item
        binding.tvPublicOption.text = item.name
        binding.imgConnected.visibility = View.GONE
        binding.imgPublicMenuEnd.visibility = View.VISIBLE
        if (item.connectionState == ConnectionState.CONNECTED) {
            binding.imgConnected.visibility = View.VISIBLE
            binding.imgPublicMenuEnd.visibility = View.GONE
        }
        if (item.connectionState == ConnectionState.CONNECTING) {

        }
    }

}