package com.ruiheng.xmuse.feature.device.ui.visualization

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.data.repository.visualization.VisualizationDataTracker
import com.ruiheng.xmuse.feature.device.R
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class XmuseVisualizationViewViewModel @Inject constructor(
    val app: Application,
    val museDataVisualizationRepository: MuseDataVisualizationRepository,
    private val visualizationDataTracker: VisualizationDataTracker
) : AndroidViewModel(app) {

    companion object {
        val visualizationViewSize: Int = 120
        val visualizationViewHeartSize: Int = 20
        val visualizationViewBloodOxygen: Int = 20
        private const val PeriodTime = 120L
        private const val bloodOxygenPeriodTime = 1000L
        private const val HeartBeatPeriodTime = 600L

    }

    private var disposable = CompositeDisposable()
    private val frameRateEpochMillisClock = Observable.interval(
        PeriodTime,
        TimeUnit.MILLISECONDS
    )

    private val heartBeatFrameRateEpochMillisClock = Observable.interval(
        HeartBeatPeriodTime,
        TimeUnit.MILLISECONDS
    )

    //    val latestQueue = museDataVisualizationRepository.latestQueue
//
//    val latestHeartBeatLiveData = museDataVisualizationRepository.latestHeartBeatLiveData
//
//    val lastBloodOxygenLiveData = museDataVisualizationRepository.lastBloodOxygenLiveData


    private val _latestHeartBeatLiveData = MutableLiveData(0.0)
    val latestHeartBeatLiveData = _latestHeartBeatLiveData as LiveData<Double>

    private val _latestAvgPowsRawValueMapLiveData =
        MutableLiveData(visualizationDataTracker.loadLastValueMap())
    val latestAvgPowsRawValueMapLiveData =
        _latestAvgPowsRawValueMapLiveData as LiveData<Map<BusymindData, Double>>

    init {
        visualizationDataTracker.startTracking()
        frameRateEpochMillisClock
            .map {
                visualizationDataTracker.loadLastValueMap()
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { valueMap ->
                _latestAvgPowsRawValueMapLiveData.value = valueMap
            }.let { disposable.add(it) }
        heartBeatFrameRateEpochMillisClock.map {
            visualizationDataTracker.loadLastValueMap()?.get(BusymindData.HEART)?:0.0
        }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { value ->
                _latestHeartBeatLiveData.value = value
            }.let { disposable.add(it) }
    }

    override fun onCleared() {
        super.onCleared()
        visualizationDataTracker.stopTracking()
        disposable.clear()
    }

}

