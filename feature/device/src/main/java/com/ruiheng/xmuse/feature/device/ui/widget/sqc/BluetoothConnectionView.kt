package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.bluetooth_button.BluetoothState

interface BluetoothConnectionView {

//    var allowOnboardingStart: Boolean

    var bluetoothStateCallback: ((state: BluetoothState) -> Unit)?

//    var popupRootView: ViewGroup?

//    var leeroySelectedCallback: (() -> Unit)?

//    var bluetoothAccessor: ActivityBluetoothAccessor?

//    var checkHeadbandRequired: Boolean

//    var btnPosition: BluetoothConnectionButton.ButtonPosition

//    fun doIfMuseRequirementsFulfilled(
//        requirement: MuseRequirement,
//        fragmentManager: FragmentManager,
//        actionIfFulfilled: (() -> Unit)
//    )

//    fun dismissDeviceListPopover()

//    fun showBluetoothConnectionPopover(
//        popoverDismissedCallback: (() -> Unit)? = null,
//        deviceConnectedCallback: (() -> Unit)? = null
//    )

//    fun setSessionMuseToFilterFor(sessionMuse: UserSessionMuse?)
}