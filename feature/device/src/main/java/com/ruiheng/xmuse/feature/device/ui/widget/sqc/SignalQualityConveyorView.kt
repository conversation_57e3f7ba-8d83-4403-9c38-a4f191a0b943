package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQuality
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQualityQueue
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import com.ruiheng.xmuse.feature.device.R
import timber.log.Timber


class SignalQualityConveyorView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val innerCircleRadius: Float by lazy { midX * 0.45f }
    private val outerCircleRadius: Float by lazy { midX * 0.8f }
    private val maxStrokeWidth: Float by lazy { outerCircleRadius - innerCircleRadius }

    private val ovalPercent = RectF()
    private val drawPaint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.STROKE
//        xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    }
    private val bitmapPaint = Paint().apply {
        isAntiAlias = true
        isDither = true
        isFilterBitmap = true
    }

    private val midX: Float by lazy { measuredWidth / 2f }
    private val midY: Float by lazy { measuredHeight / 4f * 3f }

    var hasPpg: Boolean = true
        set(value) {
            field = value
            updateMask()
            invalidate()
        }

    var conveyorQueues = emptyMap<SqcSensor, SignalQualityQueue>()
        set(value) {
            field = value
            invalidate()
        }

    private var bufferBitmap: Bitmap? = null
    private var bufferCanvas: Canvas? = null
    private var maskBitmap: Bitmap? = null
    private val mMatrix = Matrix()
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateMask()
    }

    private fun updateMask() {
        if (width <= 0 || height <= 0) return

        bufferBitmap?.recycle()
        maskBitmap?.recycle()

        bufferBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888).also {
            bufferCanvas = Canvas(it)
        }

        maskBitmap = BitmapFactory.decodeResource(
            resources,
            if (hasPpg) R.drawable.sqc_mask_with_ppg01 else R.drawable.sqc_mask01,
            BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ALPHA_8
            }
        )
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val queues = conveyorQueues
        val maskBitmap = maskBitmap
        val bufferCanvas = bufferCanvas
        val bufferBitmap = bufferBitmap
        if (bufferBitmap == null || bufferCanvas == null || maskBitmap == null || queues.isEmpty()) return

//        maskBitmap.let {
//            val scaleX = width.toFloat() / it.width
//            val scaleY = height.toFloat() / it.height
//            // 设置缩放矩阵
//            mMatrix.setScale(scaleX, scaleY)
//            // 根据矩阵绘制图片
//            bufferCanvas.drawBitmap(it, mMatrix, bitmapPaint)
//        }

        queues.forEach { (sensor, queue) ->
            var strokeWidth = 0F
            var prevSignalQuality: SignalQuality? = null
            val strokeWidthPace = maxStrokeWidth / queue.count()
            queue.elements.forEachIndexed { index, signalQuality ->
                if (signalQuality == prevSignalQuality) {
                    strokeWidth += strokeWidthPace
                } else {
                    if (prevSignalQuality != null) {
                        drawArc(
                            bufferCanvas,
                            index,
                            sensor,
                            prevSignalQuality!!,
                            strokeWidth,
                            queue
                        )
                    }
                    strokeWidth = strokeWidthPace
                    prevSignalQuality = signalQuality
                }
            }
            drawArc(
                bufferCanvas,
                queue.elements.lastIndex,
                sensor,
                prevSignalQuality!!,
                strokeWidth,
                queue
            )

            canvas.drawBitmap(bufferBitmap, 0.0f, 0.0f, bitmapPaint)
        }
    }

    private fun drawArc(
        canvas: Canvas,
        index: Int,
        sensor: SqcSensor,
        signalQuality: SignalQuality,
        strokeWidth: Float,
        queue: SignalQualityQueue
    ) {

        val arcTimePercent = (index + 1) / queue.count().toFloat()

        val ovalFinalHalfWidth = innerCircleRadius + arcTimePercent * maxStrokeWidth

        with(ovalPercent) {
            left = midX - ovalFinalHalfWidth + strokeWidth * 0.5F
            top = midY - ovalFinalHalfWidth + strokeWidth * 0.5F
            right = midX + ovalFinalHalfWidth - strokeWidth * 0.5F
            bottom = midY + ovalFinalHalfWidth - strokeWidth * 0.5F
        }

        drawPaint.strokeWidth = strokeWidth
        drawPaint.color = when (sensor) {
            SqcSensor.PPG -> getColor(signalQuality.ppgColor)
            SqcSensor.DRL_REF -> getColor(signalQuality.drlRefColor)
            else -> getColor(signalQuality.color)
        }
        canvas.drawArc(
            ovalPercent,
            sensor.startAngle(hasPpg),
            sensor.sweepAngle(hasPpg),
            false,
            drawPaint
        )
    }

    private fun getColor(colorResId: Int): Int {
        return ContextCompat.getColor(context, colorResId)
    }
}
