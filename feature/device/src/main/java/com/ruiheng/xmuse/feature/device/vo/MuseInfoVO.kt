package com.ruiheng.xmuse.feature.device.vo

import android.view.View
import androidx.annotation.StringRes
import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.model.data.Xmuse
import com.ruiheng.xmuse.core.ui.custom.PublicMenuImp
import com.ruiheng.xmuse.feature.device.R

data class DeviceInfoMenuItem(
    @StringRes val title: Int,
    val content: String?,
    val showArrow: Int = View.VISIBLE
) : PublicMenuImp {
    override fun bindTitle() = title
    override fun bindContent() = content
    override fun bindArrowShow(): Int = showArrow
    override fun bindBgResources() = android.R.color.transparent
}

fun Muse.getDeviceInfoPageList(batteryPercentRemaining: Int? = museConfiguration.batteryPercentRemaining.toInt()) =
    listOf(
        DeviceInfoMenuItem(R.string.device_name, content = name, showArrow = View.GONE),
        DeviceInfoMenuItem(
            R.string.device_sn,
            content = museConfiguration.serialNumber,
            showArrow = View.GONE
        ),
        DeviceInfoMenuItem(R.string.device_mac, content = macAddress, showArrow = View.GONE),
        DeviceInfoMenuItem(
            R.string.power,
            content = if (batteryPercentRemaining != null && batteryPercentRemaining >= 0) "${batteryPercentRemaining}%" else "--",
            showArrow = View.GONE
        ),
        DeviceInfoMenuItem(R.string.firmware_version, content = museVersion.firmwareVersion, showArrow = View.VISIBLE)
    )