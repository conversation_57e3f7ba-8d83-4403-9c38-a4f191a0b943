package com.ruiheng.xmuse.feature.device.ui

import android.animation.ObjectAnimator
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.LiveData
import com.blankj.utilcode.util.ToastUtils
import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.ui.bottommenu.BaseBindingBottomDialogFragment
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoRemoveLiveData
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.databinding.DialogBottomMuseConnectBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BottomMuseConnectDialog(
    private val muse: Muse,
    private val onResultCallback: ((Result<RequestDeviceBindResult?>) -> Unit)
) : BaseBindingBottomDialogFragment<DialogBottomMuseConnectBinding>() {

    private val bluetoothConnectionViewModel: BluetoothConnectionViewModel by viewModels()

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = DialogBottomMuseConnectBinding.inflate(inflater, container, false)


    private val rotateAnimator: ObjectAnimator by lazy {
        ObjectAnimator.ofFloat(binding.imgBtnScan, "rotation", 0f, 360f).apply {
            duration = 2000 // 设置动画持续时间为 2 秒
            repeatCount = ObjectAnimator.INFINITE // 设置动画无限循环
        }
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
//        isCancelable = false
        binding.imgErrorContainer.showImage(com.ruiheng.xmuse.core.ui.R.drawable.img_connection_fail)
        binding.imgBtn.showImage(R.drawable.ico_bt_scan_01)
        binding.imgBtnScan.showImage(R.drawable.ico_bt_scan_02)
        binding.btnRetry.setOnClickListener(this)
        rotateAnimator.start()
        startConnect()
    }

    private var connectLiveData: LiveData<Result<Muse?>>? = null
    private fun startConnect(muse: Muse,connectSuccess:()->Unit) {
        connectLiveData?.removeObservers(this)
        connectLiveData = bluetoothConnectionViewModel.createConnectionAttempt(muse)
        autoRemoveLiveData(connectLiveData!!) { result ->
            binding.layoutErrorContainer.visibility = View.GONE
            binding.layoutScanning.visibility = View.VISIBLE
            isCancelable = !result.isLoading()
            if (!result.isLoading()) {
                if (result.isSuccessWithData()) {
                    connectSuccess.invoke()
                } else {
                    binding.layoutScanning.visibility = View.GONE
                    binding.layoutErrorContainer.visibility = View.VISIBLE
                }
            }

        }
    }

    private fun startOnlineCheckDevice(muse: Muse) {
        autoRemoveFlow(
            bluetoothConnectionViewModel.startBindAndCheckDevice(muse)
        ) { result ->
            isCancelable = !result.isLoading()
            if (result.isSuccessWithData()) {
                bluetoothConnectionViewModel.enableMuseUsage()
                ToastUtils.getDefaultMaker().apply {
                    setLeftIcon(com.ruiheng.xmuse.core.ui.R.drawable.ic_radio_button_checked)
                }.show(R.string.connect_successful)
            }
            if (result.isError()) {
                binding.layoutScanning.visibility = View.GONE
                binding.layoutErrorContainer.visibility = View.VISIBLE
            }
            //需放在最后调用，否则父Dialog调用dismiss后，将销毁该实例
            onResultCallback.invoke(result)
        }
    }

    private fun startConnect(){
        startConnect(muse){
            startOnlineCheckDevice(muse)
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btnRetry -> {
                startConnect()
            }
        }
        super.onWidgetClick(view)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 停止动画并清除动画引用，防止内存泄漏
        rotateAnimator.apply {
            cancel()
            removeAllListeners()
        }
    }
}