package com.ruiheng.xmuse.feature.device.ui.sqc

import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.feature.device.SignalQualityViewModel
import com.ruiheng.xmuse.feature.device.databinding.ActivitySqcBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SqcActivity : BaseBindingActivity<ActivitySqcBinding>(), SqcFragment.Callback {

//    companion object {
//
//        private const val ARGS_KEY = "args"
//
//        fun createIntent(context: Context, args: SqcFragmentArgs): Intent {
//            return Intent(context, SqcActivity::class.java).apply {
//                putExtra(ARGS_KEY, args.toBundle())
//            }
//        }
//    }

    val sqcViewModel:SqcViewModel by viewModels()

    val signalQualityViewModel: SignalQualityViewModel by viewModels()

    override fun onSqcExited() {
        setResult(0)
        finish()
    }

    override fun onSqcPassed() {
        setResult(1)
        finish()
    }

    override fun bindDataBindingView() = ActivitySqcBinding.inflate(layoutInflater)
}