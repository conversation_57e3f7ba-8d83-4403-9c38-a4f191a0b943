package com.ruiheng.xmuse.feature.device.ui.helper

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.ruiheng.xmuse.core.common.result.permission.requestDeviceBlePermission
import com.ruiheng.xmuse.feature.device.ui.BottomMuseDialog
import com.ruiheng.xmuse.feature.device.ui.BottomMuseListDialog
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.MuseStatusIndicatorView

interface HasSqcViewPage {

    fun initSqc(
        activity: FragmentActivity, sqcLayout: MuseStatusIndicatorView,
        checkUser: () -> Boolean = { true }
    ) {
        fun startDeviceDiscovery() {
            activity.requestDeviceBlePermission {
                BottomMuseListDialog().showMuseListDialog(activity.supportFragmentManager)
            }
        }

        sqcLayout.onBluetoothClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }

        sqcLayout.signalQualityOnClick = {
            BottomMuseDialog().showDialog(activity.supportFragmentManager)
        }
    }

    fun <T:MuseStatusIndicatorView>initSqc(
        fragment: Fragment,
        sqcLayout: T,
        checkUser: () -> Boolean = { true }
    ) {

        fun startDeviceDiscovery() {
            fragment.requireContext().requestDeviceBlePermission {
                BottomMuseListDialog().showMuseListDialog(fragment.childFragmentManager)
            }
        }

        sqcLayout.onBluetoothClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }

        sqcLayout.signalQualityOnClick = {
            BottomMuseDialog().showDialog(fragment.childFragmentManager)
        }
    }
}