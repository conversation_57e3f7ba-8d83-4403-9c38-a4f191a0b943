package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor


public class SqcPassRequirementsChecker {

    private var prevGoodSensors = HashSet<SqcSensor>()

    fun areAllSensorsGood(sqcConfiguration: SqcConfiguration, progress: Map<SqcSensor, Double>): Boolean {
        for ((sensor, value) in progress) {
            if (!sqcConfiguration.sqcConveyorSensors.contains(sensor)) continue

            if (value < 1.0) {
                return false
            }
        }
        return true
    }

    fun areMinSensorsGood(
        progress: Map<SqcSensor, Double>,
        antiprogress: Map<SqcSensor, Double>
    ): Boolean {
        val goodSensors = listOf(SqcSensor.EEG_RIGHT_BACK, SqcSensor.EEG_LEFT_BACK, SqcSensor.PPG)
            .associateWith { progress[it] }
            .filterValues { it != null && it >= 1.0 }
            .keys
        prevGoodSensors.addAll(goodSensors)

        val badSensors = listOf(SqcSensor.EEG_RIGHT_BACK, SqcSensor.EEG_LEFT_BACK, SqcSensor.PPG)
            .associateWith { antiprogress[it] }
            .filterValues { it != null && it >= 1.0 }
            .keys
        prevGoodSensors.removeAll(badSensors)

        return prevGoodSensors.contains(SqcSensor.EEG_LEFT_BACK) || prevGoodSensors.contains(
            SqcSensor.EEG_RIGHT_BACK)
    }
}