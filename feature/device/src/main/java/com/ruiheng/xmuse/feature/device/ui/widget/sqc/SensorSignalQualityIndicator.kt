package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQuality

open class SqcSensorIndicator @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val alphaMax = 255

    private val fillCircleRadius: Float by lazy {
        measuredWidth / 2.5F
    }
    private val midX: Float by lazy {
        measuredWidth / 2.0F
    }
    private val midY: Float by lazy {
        measuredHeight / 2.0F
    }

    private var curOpacity: Double = 0.0

    private val drawFillPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
            color = colorSolid
            alpha = (alphaMax * curOpacity).toInt()
        }
    }

    private val drawBackgroundPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
            color = colorWhite
        }
    }

    protected open val colorSolid by lazy {
        ContextCompat.getColor(context, SignalQuality.GOOD.color)
    }

    private val colorWhite by lazy {
        ContextCompat.getColor(context, SignalQuality.BAD.color)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawCircle(midX, midY, fillCircleRadius, drawBackgroundPaint)
        canvas.drawCircle(midX, midY, fillCircleRadius, drawFillPaint)
    }

    fun setLatestValue(newOpacity: Double) {

        if (newOpacity < 0 || newOpacity > 1) return

        if (curOpacity != newOpacity) {
            setAlpha((alphaMax * newOpacity).toInt())
            curOpacity = newOpacity
        }
    }

    private fun setAlpha(alphaValue: Int) {
        drawFillPaint.alpha = alphaValue
        invalidate()
    }
}

class PpgSensorIndicator @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : SqcSensorIndicator(context, attrs, defStyleAttr) {

    override val colorSolid by lazy {
        ContextCompat.getColor(context, SignalQuality.GOOD.ppgColor)
    }
}