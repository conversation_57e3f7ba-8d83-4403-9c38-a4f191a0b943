package com.ruiheng.xmuse.feature.device

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.choosemuse.libmuse.ConnectionState
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.LiveEvent
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseSignalQualityMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQualityQueue
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcConfiguration
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.disposables.CompositeDisposable
import javax.inject.Inject

@HiltViewModel
class SignalQualityViewModel
@Inject constructor(
    private val museConnector: MuseConnector,
    private val sqMonitor: MuseSignalQualityMonitor,
) : ViewModel() {

//    fun observerConnectMuse() = museConnector.obseverXmuseInfoLiveData()

    val sensorSignalQualityQueues: LiveData<Map<SqcSensor, SignalQualityQueue>>
        get() = mutableSensorSignalQualityQueues
    private val mutableSensorSignalQualityQueues =
        MutableLiveData(sqMonitor.sensorSignalQualityQueues.value ?: emptyMap())

    val sensorContactStrengths: LiveData<Map<SqcSensor, Double>>
        get() = mutableSensorContactStrengths
    private val mutableSensorContactStrengths =
        MutableLiveData(sqMonitor.sensorContactStrengths.value ?: emptyMap())

    val sqcConfiguration: LiveData<SqcConfiguration>
        get() = mutableSqcConfiguration
    private val mutableSqcConfiguration =
        MutableLiveData(SqcConfiguration.getConfiguration(museConnector.selectedMuseModel()))

    val heartbeatDetected: LiveData<LiveEvent<Any>>
        get() = mutableHeartbeatDetected
    private val mutableHeartbeatDetected = MutableLiveData<LiveEvent<Any>>()

    private val disposableBag = CompositeDisposable()

    init {
        sqMonitor.sensorSignalQualityQueues
            .subscribe(mutableSensorSignalQualityQueues::postValue)
            .let { disposableBag.add(it) }
        sqMonitor.sensorContactStrengths
            .subscribe(mutableSensorContactStrengths::postValue)
            .let { disposableBag.add(it) }
        sqMonitor.heartbeatDetected
            .subscribe { if (it) mutableHeartbeatDetected.postValue(LiveEvent(Any())) }
            .let { disposableBag.add(it) }
        museConnector.museConnectionState
            .filter { it == ConnectionState.CONNECTED }
            .map { SqcConfiguration.getConfiguration(museConnector.selectedMuseModel()) }
            .distinctUntilChanged()
            .subscribe {
                mutableSqcConfiguration.value = it
            }.let { disposableBag.add(it) }
        sqMonitor.startMonitoring()
    }

    override fun onCleared() {
        super.onCleared()
        disposableBag.clear()
    }
}