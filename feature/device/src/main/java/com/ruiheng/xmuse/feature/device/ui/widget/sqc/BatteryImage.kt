package com.ruiheng.xmuse.feature.device.ui.widget.sqc

import androidx.annotation.DrawableRes
import androidx.annotation.IdRes
import com.ruiheng.xmuse.feature.device.R

const val FULL_BATTERY_IMAGE_THRESHOLD = 75

@IdRes
@DrawableRes
fun getBatteryImage(percentage: Int): Int {
    return when {
        percentage <= 25 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_25
        percentage <= 50 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_50
        percentage <= 75 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_75
        else -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_full
    }
}

@IdRes
@DrawableRes
fun getBatteryImageWithBackground(percentage: Int): Int {
    return when {
        percentage <= 25 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_25
        percentage <= 50 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_50
        percentage <= 75 -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_75
        else -> com.ruiheng.xmuse.core.ui.R.drawable.ico_battery_full
    }
}