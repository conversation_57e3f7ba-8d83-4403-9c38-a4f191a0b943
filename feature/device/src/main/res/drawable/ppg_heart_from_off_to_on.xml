<?xml version="1.0" encoding="utf-8"?>
<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:name="vector"
            android:width="880dp"
            android:height="840dp"
            android:viewportWidth="880"
            android:viewportHeight="840">
            <path
                android:name="path"
                android:pathData="M 193 743 C 73 545 0 361 0 254 C 0 108 107 1 254 0 C 359 0 444 61 470 155 L 482 198 L 529 173 C 568 152 585 149 646 152 C 703 155 725 161 758 183 C 814 220 848 274 862 348 C 889 492 828 585 650 673 C 554 720 342 795 273 806 C 235 811 235 811 193 743 Z"/>
        </vector>
    </aapt:attr>
    <target android:name="path">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="fillColor"
                android:duration="1000"
                android:valueFrom="@color/white"
                android:valueTo="@color/red500"
                android:valueType="colorType"
                android:interpolator="@android:interpolator/accelerate_decelerate"/>
        </aapt:attr>
    </target>
</animated-vector>