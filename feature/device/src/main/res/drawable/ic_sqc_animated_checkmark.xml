<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:name="vector"
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <path
                android:name="path_1"
                android:pathData="M 6 12 L 10 16 L 18 8"
                android:trimPathEnd="0"
                android:strokeColor="#000000"
                android:strokeWidth="2"
                android:strokeLineCap="round"/>
        </vector>
    </aapt:attr>
    <target android:name="path_1">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="trimPathEnd"
                android:duration="1500"
                android:valueFrom="0"
                android:valueTo="1"
                android:valueType="floatType"
                android:interpolator="@android:interpolator/fast_out_slow_in"/>
        </aapt:attr>
    </target>
</animated-vector>