<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/space24">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.bottomsheet.BottomSheetDragHandleView
            android:id="@+id/drag_handle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/drag_handle"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="?colorOnSurface"
            app:tabTextColor="?colorOnSurfaceVariant"
            app:tabSelectedTextAppearance="@style/TextAppearance.AppCompat.Headline"
            app:tabTextAppearance="@style/TextAppearance.AppCompat.Body1">

        </com.google.android.material.tabs.TabLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="400dp"
        app:layout_constraintTop_toBottomOf="@id/layout_top_bar">


        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            app:layout_constraintTop_toTopOf="parent">

        </androidx.viewpager2.widget.ViewPager2>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>