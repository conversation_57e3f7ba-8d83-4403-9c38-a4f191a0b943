<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/half_normal_margin"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            android:text="@string/current_status"
            android:textColor="?colorOnPrimaryContainer"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_score"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:paddingStart="24dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <HorizontalScrollView
            android:id="@+id/scroll_voew_chip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            app:layout_constraintTop_toBottomOf="@id/tv_score">


            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingTop="@dimen/half_normal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin"
                android:paddingBottom="@dimen/half_normal_margin"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                app:singleLine="true"
                app:singleSelection="true">


                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_focus"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="46dp"
                    android:checked="true"
                    android:text="专注力"
                    android:textSize="@dimen/font_small"
                    app:checkedIconEnabled="false"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipCornerRadius="16dp"
                    app:chipIcon="@drawable/ic_dot"
                    app:chipIconEnabled="true"
                    app:chipIconSize="8dp"
                    app:chipIconTint="@color/color1"
                    app:chipStrokeColor="@color/grey400"
                    app:chipStrokeWidth="1px" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_relax"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="46dp"
                    android:layout_marginStart="@dimen/half_normal_margin"
                    android:checked="false"
                    android:text="放松度"
                    android:textSize="@dimen/font_small"
                    app:checkedIconEnabled="false"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipCornerRadius="16dp"
                    app:chipIcon="@drawable/ic_dot"
                    app:chipIconEnabled="true"
                    app:chipIconSize="8dp"
                    app:chipIconTint="@color/color2"
                    app:chipStrokeColor="@color/grey400"
                    app:chipStrokeWidth="1px" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_stress"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="46dp"
                    android:layout_marginStart="@dimen/half_normal_margin"
                    android:checked="false"
                    android:text="压力值"
                    android:textSize="@dimen/font_small"
                    app:checkedIconEnabled="false"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipCornerRadius="16dp"
                    app:chipIcon="@drawable/ic_dot"
                    app:chipIconEnabled="true"
                    app:chipIconSize="8dp"
                    app:chipIconTint="@color/color3"
                    app:chipStrokeColor="@color/grey400"
                    app:chipStrokeWidth="1px" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_anxiety"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="46dp"
                    android:layout_marginStart="@dimen/half_normal_margin"
                    android:checked="false"
                    android:text="焦虑值"
                    android:textSize="@dimen/font_small"
                    app:checkedIconEnabled="false"
                    app:chipBackgroundColor="@color/chip_background_color"
                    app:chipCornerRadius="16dp"
                    app:chipIcon="@drawable/ic_dot"
                    app:chipIconEnabled="true"
                    app:chipIconSize="8dp"
                    app:chipIconTint="@color/color4"
                    app:chipStrokeColor="@color/grey400"
                    app:chipStrokeWidth="1px" />
            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/activity_horizontal_margin"
            app:layout_constraintTop_toBottomOf="@id/scroll_voew_chip">

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart"
                android:layout_width="match_parent"
                android:layout_height="240dp"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>