<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            android:text="@string/connect_the_device"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_scanning"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn_scan"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.35"
                tools:src="@drawable/ico_bt_scan_02" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="@id/img_btn_scan"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toTopOf="@id/img_btn_scan"
                tools:src="@drawable/ico_bind_loading01" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:text="@string/connecting"
                android:textColor="?colorOnSurfaceVariant"
                android:textSize="@dimen/font_normal"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toBottomOf="@id/img_btn_scan" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_error_container"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_error_container"
                android:layout_width="160dp"
                android:layout_height="160dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.1"
                tools:src="@drawable/img_connection_fail" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:minWidth="124dp"
                app:cornerRadius="@dimen/half_normal_margin"
                android:text="@string/Reconnect"
                android:textSize="@dimen/font_normal"
                app:backgroundTint="?colorPrimary"
                android:textColor="?colorOnPrimary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/img_error_container" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>