<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        tools:src="@drawable/pic_sleep_bg" />


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_title_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/activity_vertical_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/space48"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SignalQualityLayout
            android:id="@+id/sqc_layout_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            android:layout_marginEnd="@dimen/space24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ruiheng.xmuse.feature.device.ui.widget.sqc.MuseStatusIndicatorView
        android:id="@+id/sqc_layout"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        app:layout_constraintBottom_toBottomOf="@id/app_bar"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:id="@+id/view_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>