<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_bluetooth_connection"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1:1"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toStartOf="@id/vertical_guideline_left_connect"
            app:layout_constraintStart_toEndOf="@id/vertical_guideline_left_connect"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_left_connect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.55" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_right_connect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="1" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SignalQualityLayout
            android:id="@+id/signalQuality"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="invisible"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.35" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.65" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/battery"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/signalQuality"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@id/vertical_guideline_right"
            app:layout_constraintStart_toEndOf="@id/vertical_guideline_left"
            tools:src="@drawable/ico_battery_full" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
