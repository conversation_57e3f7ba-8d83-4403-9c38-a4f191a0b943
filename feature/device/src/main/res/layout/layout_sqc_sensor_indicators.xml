<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSqcSensorIndicatorsParent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcSensorIndicator
            android:id="@+id/vSensorDrlRef"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_circle_surface_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.503"
            tools:layout_constraintVertical_bias="0.34" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.PpgSensorIndicator
            android:id="@+id/vSensorPpg"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_circle_surface_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.605"
            tools:layout_constraintVertical_bias="0.375"
            tools:visibility="gone" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcSensorIndicator
            android:id="@+id/vSensorLeftFront"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.335"
            tools:layout_constraintVertical_bias="0.42" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcSensorIndicator
            android:id="@+id/vSensorRightFront"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.67"
            tools:layout_constraintVertical_bias="0.42" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcSensorIndicator
            android:id="@+id/vSensorLeftBack"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.246"
            tools:layout_constraintVertical_bias="0.76" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SqcSensorIndicator
            android:id="@+id/vSensorRightBack"
            android:layout_width="19.5dp"
            android:layout_height="19.5dp"
            android:background="@drawable/shape_indicator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_constraintHorizontal_bias="0.754"
            tools:layout_constraintVertical_bias="0.76" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
