<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSqcGraphParent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SignalQualityConveyorView
            android:id="@+id/layoutConveyorBelt"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@id/ivConveyorBeltFrame" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivConveyorBeltFrame"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@null"
            android:scaleType="fitStart"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/sqc_outline01"
            tools:tint="@color/black87"
            tools:visibility="visible" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.PpgHeartIndicator
            android:id="@+id/ppgHeartIndicator"
            android:layout_width="23.40dp"
            android:layout_height="23.40dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintVertical_bias="0.09"
            tools:visibility="invisible" />

        <com.ruiheng.xmuse.feature.device.ui.widget.sqc.SensorSignalQualityIndicatorsLayout
            android:id="@+id/layoutSensorIndicators"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:visibility="invisible" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsLeftImageBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_left" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsLeftImage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_left" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsMiddleImageBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_middle" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsMiddleImage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_middle" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsRightImageBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_right" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/fnirsRightImage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintEnd_toEndOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintStart_toStartOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            tools:src="@drawable/img_ppg_fnirs_right" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAnimatedCheckmark"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription=""
            android:scaleType="fitCenter"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toStartOf="@id/vertical_guideline_right"
            app:layout_constraintStart_toEndOf="@id/vertical_guideline_left"
            app:layout_constraintTop_toTopOf="@+id/ivConveyorBeltFrame"
            app:layout_constraintVertical_bias="0.85"

            app:srcCompat="@drawable/ic_sqc_animated_checkmark"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.35" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/vertical_guideline_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.65" />

        <!--        <androidx.appcompat.widget.AppCompatImageView-->
        <!--            android:id="@+id/battery"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="0dp"-->
        <!--            android:src="@drawable/battery_75_white_bg"-->
        <!--            app:layout_constraintBottom_toBottomOf="@id/layoutConveyorBelt"-->
        <!--            app:layout_constraintDimensionRatio="1:1"-->
        <!--            app:layout_constraintEnd_toStartOf="@id/vertical_guideline_right"-->
        <!--            app:layout_constraintStart_toEndOf="@id/vertical_guideline_left" />-->
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
