<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/space24">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:text="当前脑电状态"
        android:textColor="?colorOnPrimaryContainer"
        android:textSize="@dimen/font_title"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:textColor="?colorOnPrimaryContainer"
        android:textSize="@dimen/font_title"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="当前状态" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title_blood_oxygen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:textColor="?colorOnPrimaryContainer"
        android:textSize="@dimen/font_title"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="当前状态" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btn_record"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:srcCompat="@drawable/ps_ic_audio_play" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btn_record_stop"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/btn_record"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:srcCompat="@drawable/ps_ic_audio_stop" />

    <!--        <HorizontalScrollView-->
    <!--            android:id="@+id/scroll_voew_chip"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:paddingStart="@dimen/activity_horizontal_margin"-->
    <!--            android:paddingTop="@dimen/half_normal_margin"-->
    <!--            android:paddingEnd="@dimen/activity_horizontal_margin"-->
    <!--            android:paddingBottom="@dimen/half_normal_margin"-->
    <!--            android:scrollbars="none"-->
    <!--            app:layout_constraintTop_toBottomOf="@id/tv_title">-->


    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/half_normal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/half_normal_margin"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:singleLine="true">


        <com.google.android.material.chip.Chip
            android:id="@+id/chip_alpha"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="α"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipBackgroundColor="@color/chip_background_color"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color2"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_beta"
            style="@style/Widget.Material3.Chip.Suggestion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="β"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipBackgroundColor="@color/chip_background_color"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color3"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_gamma"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="γ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipBackgroundColor="@color/chip_background_color"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color4"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_delta"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="δ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipBackgroundColor="@color/chip_background_color"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color5"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_theta"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="θ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipBackgroundColor="@color/chip_background_color"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color1"
            app:chipStrokeWidth="0dp" />
    </com.google.android.material.chip.ChipGroup>
    <!--        </HorizontalScrollView>-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_chart_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/activity_horizontal_margin"
        app:layout_constraintTop_toBottomOf="@id/chip_group">

        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/chart"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/chart_heart_rate"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            app:layout_constraintTop_toTopOf="parent" />

        <com.github.mikephil.charting.charts.LineChart
            android:id="@+id/chart_blood_oxygen"
            android:layout_width="match_parent"
            android:layout_height="240dp"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_current_state_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/activity_horizontal_margin"
        app:layout_constraintDimensionRatio="97:102"
        app:layout_constraintEnd_toStartOf="@id/layout_heart_rate_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_chart_container"
        app:strokeColor="@color/colorPrimary"
        app:strokeWidth="2dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_current_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            tools:src="@drawable/ico_tab_brain" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_current_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/current_status"
                android:textColor="@color/grey900"
                android:textSize="15dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_current_state_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/eeg_trend"
                android:textColor="@color/grey600"
                android:textSize="11dp"
                app:layout_constraintStart_toStartOf="@id/tv_current_state"
                app:layout_constraintTop_toBottomOf="@id/tv_current_state" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </com.google.android.material.card.MaterialCardView>


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_heart_rate_container"
        style="?attr/materialCardViewFilledStyle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/activity_horizontal_margin"
        app:layout_constraintDimensionRatio="97:102"
        app:layout_constraintEnd_toStartOf="@id/layout_blood_oxygen_container"
        app:layout_constraintStart_toEndOf="@id/layout_current_state_container"
        app:layout_constraintTop_toTopOf="@id/layout_current_state_container"
        app:strokeColor="@color/red300"
        app:strokeWidth="2dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_heart_rate"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            tools:src="@drawable/ico_tab_heart_beat" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_heart_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/grey900"
                android:textSize="15dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2"
                tools:text="75次/分钟" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_heart_rate_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/heart_rate"
                android:textColor="@color/grey600"
                android:textSize="11dp"
                app:layout_constraintStart_toStartOf="@id/tv_heart_rate"
                app:layout_constraintTop_toBottomOf="@id/tv_heart_rate" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/layout_blood_oxygen_container"
        style="?attr/materialCardViewFilledStyle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        android:layout_marginStart="@dimen/activity_horizontal_margin"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        app:layout_constraintDimensionRatio="97:102"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/layout_heart_rate_container"
        app:layout_constraintTop_toTopOf="@id/layout_current_state_container"
        app:strokeColor="@color/red300"
        app:strokeWidth="2dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_blood_oxygen"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            tools:src="@drawable/ico_tab_blood_oxygen" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_blood_oxygen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/grey900"
                android:textSize="15dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.3"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2"
                tools:text="75次/分钟" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_blood_oxygen_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="血氧指标"
                android:textColor="@color/grey600"
                android:textSize="11dp"
                app:layout_constraintStart_toStartOf="@id/tv_blood_oxygen"
                app:layout_constraintTop_toBottomOf="@id/tv_blood_oxygen" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>