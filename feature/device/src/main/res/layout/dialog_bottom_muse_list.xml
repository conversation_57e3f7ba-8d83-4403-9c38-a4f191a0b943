<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            android:text="@string/search_device"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/half_normal_margin"
            android:indeterminate="true"
            app:indicatorColor="?colorOnSurfaceVariant"
            app:indicatorInset="2dp"
            app:indicatorSize="16dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toEndOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            app:trackThickness="2dp" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="260dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_menu"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="2"
                tools:listitem="@layout/item_public_menu_row" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_scanning"
            android:layout_width="match_parent"
            android:layout_height="260dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn_scan"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.35"
                tools:src="@drawable/ico_bt_scan_02" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_btn"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="@id/img_btn_scan"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toTopOf="@id/img_btn_scan"
                tools:src="@drawable/ico_bt_scan_01" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/half_normal_margin"
                android:text="@string/searching"
                android:textColor="?colorOnSurfaceVariant"
                android:textSize="@dimen/font_normal"
                app:layout_constraintEnd_toEndOf="@id/img_btn_scan"
                app:layout_constraintStart_toStartOf="@id/img_btn_scan"
                app:layout_constraintTop_toBottomOf="@id/img_btn_scan" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>