package com.ruiheng.xmuse.feature.setting.ui

import android.os.Bundle
import android.view.View
import androidx.viewbinding.ViewBinding
import com.ruiheng.xmuse.core.ui.ui.BaseNavFragment
import com.ruiheng.xmuse.feature.setting.SettingActivity
import com.ruiheng.xmuse.feature.setting.SettingViewModel

abstract class BaseSettingFragment<T : ViewBinding> : BaseNavFragment<T>() {

    protected val userViewModel: SettingViewModel by lazy {
        (requireActivity() as SettingActivity).settingViewModel
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
    }

}