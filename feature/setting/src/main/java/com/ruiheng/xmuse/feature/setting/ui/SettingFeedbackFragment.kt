package com.ruiheng.xmuse.feature.setting.ui

import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.feature.setting.SettingActivity
import com.ruiheng.xmuse.feature.setting.SettingInfoMenuItem
import com.ruiheng.xmuse.feature.setting.SettingViewModel
import com.ruiheng.xmuse.feature.setting.SettingViewModel.Companion.RUIHENG_OFFICIAL_WEBSITE
import com.ruiheng.xmuse.feature.setting.SettingViewModel.Companion.RUIHENG_WECHAT_SERVICE
import com.ruiheng.xmuse.feature.setting.SettingViewModel.Companion.RUIHGENG_PHONE
import com.ruiheng.xmuse.feature.setting.databinding.FragmentFeedbackBinding

class SettingFeedbackFragment : BaseSettingFragment<FragmentFeedbackBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentFeedbackBinding.inflate(inflater, container, false)

    private val settingViewModel: SettingViewModel by lazy {
        (requireActivity() as SettingActivity).settingViewModel
    }

    override fun bindTitleRes() = com.ruiheng.xmuse.core.ui.R.string.feedback

    private var infoAdapter: PublicNormalMenuRVAdapter<SettingInfoMenuItem> by autoCleared()

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

        infoAdapter = PublicNormalMenuRVAdapter { menu ->
            when (menu.title) {

                com.ruiheng.xmuse.core.ui.R.string.email -> {
                    val email = "<EMAIL>"
                    val emailIntent = Intent(Intent.ACTION_SEND).apply {
                        type = "message/rfc822"
                        putExtra(Intent.EXTRA_EMAIL, arrayOf(email)) // 收件人
                    }
                    try {
                        startActivity(Intent.createChooser(emailIntent, "选择邮件客户端"))
                    } catch (e: ActivityNotFoundException) {
                        e.printStackTrace()
                    }
//                    fun startByType() {
//                        val emailIntent = Intent(Intent.ACTION_SEND).apply {
//                            type = "message/rfc822"
//                            putExtra(Intent.EXTRA_EMAIL, arrayOf(email)) // 收件人
//                        }
//                        try {
//                            startActivity(Intent.createChooser(emailIntent, "选择邮件客户端"))
//                        } catch (e: ActivityNotFoundException) {
//                            e.printStackTrace()
////                            startByType()
//                        }
//                    }
//
//                    fun startByCategory() {
//                        val emailIntent = Intent(Intent.ACTION_SEND).apply {
////                        type = "message/rfc822"
//                            addCategory(Intent.CATEGORY_APP_EMAIL)
//                            putExtra(Intent.EXTRA_EMAIL, arrayOf(email)) // 收件人
//                        }
//                        try {
//                            startActivity(Intent.createChooser(emailIntent, "选择邮件客户端"))
//                        } catch (e: ActivityNotFoundException) {
//                            e.printStackTrace()
//                            startByType()
//                        }
//                    }
//                    val emailIntent = Intent(Intent.ACTION_SEND).apply {
//                        setData(Uri.parse("mailto:"))
////                        type = "message/rfc822"
////                            addCategory(Intent.CATEGORY_APP_EMAIL)
//                        putExtra(Intent.EXTRA_EMAIL, arrayOf(email)) // 收件人
//                    }
//                    try {
//                        startActivity(Intent.createChooser(emailIntent, "选择邮件客户端"))
//                    } catch (e: ActivityNotFoundException) {
//                        e.printStackTrace()
//                        startByCategory()
//                    }
                }

                com.ruiheng.xmuse.core.ui.R.string.phone_call_service -> {
                    try {
                        val dialIntent =
                            Intent(Intent.ACTION_DIAL, Uri.parse("tel:$RUIHGENG_PHONE"))
                        startActivity(dialIntent)
                    } catch (e: Exception) {
                        ClipboardUtils.copyText(RUIHGENG_PHONE)
                        ToastUtils.showShort("客服电话已复制")
                        e.printStackTrace()
                    }
                }

                com.ruiheng.xmuse.core.ui.R.string.official_website -> {
                    try {
                        val officialWebsite = RUIHENG_OFFICIAL_WEBSITE
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(officialWebsite))
                        startActivity(intent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                com.ruiheng.xmuse.core.ui.R.string.online_service -> {
                    try {
                        val wechatService = RUIHENG_WECHAT_SERVICE
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(wechatService))
                        startActivity(intent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
        binding.rvSettingInfo.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvSettingInfo.adapter = infoAdapter
        autoStoppedFlow(settingViewModel.feedbackWayListStateFlow) {
            infoAdapter.submitList(it)
        }
    }


    override fun onWidgetClick(view: View) {
        when (view) {

            else -> {
                super.onWidgetClick(view)
            }
        }

    }
}