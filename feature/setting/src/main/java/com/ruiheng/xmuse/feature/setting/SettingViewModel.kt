package com.ruiheng.xmuse.feature.setting

import android.app.DownloadManager
import android.view.View
import androidx.annotation.StringRes
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.AppUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.FileDownloadRepository
import com.ruiheng.xmuse.core.data.repository.SettingRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesSettingRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.network.model.other.AppVersion
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import com.ruiheng.xmuse.core.ui.custom.PublicMenuImp
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingViewModel @Inject constructor(
    private val netResourcesUserRepository: NetResourcesUserRepository,
    private val netResourcesSettingRepository: NetResourcesSettingRepository,
    private val fileDownloadRepository: FileDownloadRepository,
    private val settingRepository: SettingRepository
) : ViewModel() {

    companion object{
        const val RUIHENG_EMAIL = "<EMAIL>"
        const val RUIHGENG_PHONE = "0592-5797666"
        const val RUIHENG_WECHAT_SERVICE = "https://work.weixin.qq.com/kfid/kfc82fafb7b2c02d848"
        const val RUIHENG_OFFICIAL_WEBSITE = "https://www.xmuse.cn"
    }

    private val _fetchLatestVersionStateFlow =
        MutableStateFlow<Result<AppVersion>>(Result.Loading())
    val fetchLatestVersionStateFlow = _fetchLatestVersionStateFlow as StateFlow<Result<AppVersion>>

    private val _xmuseDomainConfigStateFlow =
        MutableStateFlow<Result<KvConfigItem>>(Result.Loading())
    val xmuseDomainConfigStateFlow = _xmuseDomainConfigStateFlow as StateFlow<Result<KvConfigItem>>

    init {
        viewModelScope.launch {
            netResourcesSettingRepository.fetchLatestVersion().collect {
                _fetchLatestVersionStateFlow.value = it
            }
        }
    }

    fun startWxAuthor() = netResourcesUserRepository.startWxAuthor()

    val feedbackWayListStateFlow: StateFlow<List<SettingInfoMenuItem>> = MutableStateFlow(
        listOf(
            SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.official_website, RUIHENG_OFFICIAL_WEBSITE),
            SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.email, RUIHENG_EMAIL),
            SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.phone_call_service, RUIHGENG_PHONE),
            SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.online_service, null),
        )
    )

    val settingListStateFlow: StateFlow<List<SettingInfoMenuItem>> =
        _fetchLatestVersionStateFlow.map { newVersionResult ->
            val newVersion = newVersionResult.data
//            val versionContent =
//                if (newVersion != null) "有新版本:${newVersion.name}" else AppUtils.getAppVersionName()
            val userInfoList = listOf(
                SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.app_version_check, null),
                SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.user_agreement, null),
                SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.privacy_policy, null),
                SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.contact, null),
//                SettingInfoMenuItem(com.ruiheng.xmuse.core.ui.R.string.feedback, null),
            )
            userInfoList
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = listOf()
        )

    fun startFileDownload(url: String, name: String) =
        fileDownloadRepository.startDownload(url, name, DownloadManager.Request.VISIBILITY_VISIBLE)

    fun startApp(isLocalTest: Boolean, isTest: Boolean) =
        settingRepository.startApp(isLocalTest, isTest).stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading()
        )

    fun loadPlatformAppInfo() =
        netResourcesSettingRepository.fetchPlatformVersionInfo().stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading()
        )

    /**
     * 获取Xmuse域名配置
     */
    fun getXmuseDomainConfig() {
        viewModelScope.launch {
            settingRepository.getXmuseDomainConfig().collect {
                _xmuseDomainConfigStateFlow.value = it
            }
        }
    }
}

data class SettingInfoMenuItem(
    @StringRes val title: Int,
    val content: String?,
    val showArrow: Int = View.VISIBLE
) : PublicMenuImp {
    override fun bindTitle() = title
    override fun bindContent() = content
    override fun bindArrowShow(): Int = showArrow
    override fun bindBgResources() = android.R.color.transparent
}

