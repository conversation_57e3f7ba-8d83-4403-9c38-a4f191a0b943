/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.feature.setting.ui

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.addCallback
import androidx.activity.viewModels
import androidx.core.content.FileProvider
import com.blankj.utilcode.util.AppUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.network.model.other.AppVersion
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.setting.R
import com.ruiheng.xmuse.feature.setting.SettingViewModel
import com.ruiheng.xmuse.feature.setting.databinding.ActivityAppUpgradeBinding
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.io.File
import java.util.Locale


@AndroidEntryPoint
class AppUpgradeActivity : BaseBindingActivity<ActivityAppUpgradeBinding>() {
    override fun bindDataBindingView() = ActivityAppUpgradeBinding.inflate(layoutInflater)
    val settingViewModel: SettingViewModel by viewModels()


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        val callback = onBackPressedDispatcher.addCallback(this) {
            checkFinish()
        }
        callback.isEnabled = true
        binding.btnInstall.visibility = View.GONE
        binding.btnMarket.visibility = View.GONE
        binding.btnSure.visibility = View.VISIBLE
        binding.btnLoading.visibility = View.GONE
        binding.btnInstall.setOnClickListener(this)
        binding.btnCancel.setOnClickListener(this)
        binding.btnMarket.setOnClickListener(this)
        binding.imgUpgradeBg.showImage(R.drawable.img_upgrade)
        binding.btnSure.setOnClickListener(this)
        autoRemoveFlow(settingViewModel.fetchLatestVersionStateFlow) { result ->
            if (!result.isLoading()) {
                if (result.isSuccessWithData()) {
                    val version = result.data!!
                    binding.tvNewVersion.text = version.name
                    binding.tvAppInfo.text = version.remark
                    startCheckPlatform(version)
                } else {
                    Toast.makeText(this, getString(R.string.upgrade_check_error), Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }
    }

    private fun startCheckPlatform(version: AppVersion) {
        autoRemoveFlow(settingViewModel.loadPlatformAppInfo()) { result ->
            result.showResult()
            if (!result.isLoading()) {
                val platformAppInfo = result.data
                binding.btnMarket.visibility =
                    if (platformAppInfo?.checkAppStoreApp(version) == true) View.VISIBLE else View.GONE
            }
        }
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btnMarket -> {
                openAppMarket(this)
            }

            binding.btnSure -> {
                val appInfo = settingViewModel.fetchLatestVersionStateFlow.value.data ?: return
                startAppUpdate(appInfo)
            }

            binding.btnInstall -> {
                if (installUri == null) return
                installApk(installUri!!)
            }

            binding.btnCancel -> {
                checkFinish()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }

    private fun checkFinish() {
        val fetchLatestVersionStateFlow =
            settingViewModel.fetchLatestVersionStateFlow.value.data
        if (fetchLatestVersionStateFlow?.requiredVersionFlag == true) {
            Toast.makeText(this, getString(R.string.mandatory_upgrade_version), Toast.LENGTH_SHORT).show()
//            ToastUtils.showShort("该版本为强制升级版本~")
        } else {
            finish()
        }
    }

    private var installUri: Uri? = null
    private fun startAppUpdate(version: AppVersion) {
        val fullDownloadPath = version.fullDownloadPath ?: return
        autoRemoveFlow(
            settingViewModel.startFileDownload(
                fullDownloadPath,
                "${version.name}.apk"
            )
        ) { result ->
            if (result.isLoading()) {
                binding.btnInstall.visibility = View.GONE
                binding.btnSure.visibility = View.GONE
                binding.btnMarket.visibility = View.GONE
                binding.btnLoading.visibility = View.VISIBLE
                val loadingResult = result as Result.Loading
                binding.btnLoading.text = getString(R.string.loading_progress,loadingResult.progress)
            } else {
                if (result.isSuccessWithData()) {
                    installUri = result.data
                    binding.btnInstall.visibility = View.VISIBLE
                    binding.btnSure.visibility = View.GONE
                    binding.btnMarket.visibility = View.GONE
                    binding.btnLoading.visibility = View.GONE
                } else {
                    binding.btnInstall.visibility = View.GONE
                    binding.btnSure.visibility = View.VISIBLE
                    binding.btnMarket.visibility = View.VISIBLE
                    binding.btnLoading.visibility = View.GONE
                }
            }
        }
//        startCheckPlatform { platformAppInfo ->
//            if (platformAppInfo?.checkAppStoreApp(version) == true) {
//                val intent = Intent(Intent.ACTION_VIEW)
//                intent.setData(Uri.parse(platformAppInfo.downloadLink))
//                // 检查是否有可以处理该Intent的应用程序
//                if (intent.resolveActivity(packageManager) != null) {
//                    startActivity(intent)
//                } else {
//                    startAppLoad()
//                }
//            } else {
//                startAppLoad()
//            }
//        }
    }


    private fun installApk(uri: Uri) {
        val contentUri = FileProvider.getUriForFile(
            this,
            "${packageName}.fileprovider",
            File(uri.path!!)
        )
        AppUtils.installApp(contentUri)
    }

    private fun openAppMarket(context: Context, packageName: String = "com.ruiheng.xmuse") {
        try {
            val marketPackageName = getMarketPackageName()
            val uri = Uri.parse("market://details?id=$packageName")
            val intent = Intent(Intent.ACTION_VIEW, uri)

            marketPackageName?.let {
                intent.setPackage(it)
            }

            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }catch (e:Exception){
            Toast.makeText(this, getString(R.string.jump_to_app_store), Toast.LENGTH_SHORT).show()
            e.printStackTrace()
        }

    }

    private fun getMarketPackageName(): String? {
        val brand: String = Build.BRAND.lowercase(Locale.getDefault())
        return when (brand) {
            "xiaomi" -> "com.xiaomi.market"
            "vivo" -> "com.bbk.appstore"
            "oppo" -> "com.oppo.market"
            "honor", "huawei" -> "com.huawei.appmarket"
            else -> null
        }
    }
//    override fun bindNavigationSpaceView() = binding.viewNavigationSpace
}
