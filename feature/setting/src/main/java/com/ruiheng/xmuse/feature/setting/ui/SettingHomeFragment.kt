package com.ruiheng.xmuse.feature.setting.ui

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.sdk.android.feedback.impl.FeedbackAPI
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.aliyunAppKey
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.aliyunAppScret
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.privacyPolicy
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.userProtocol
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.helper.IBasePageErrorView
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.feature.setting.R
import com.ruiheng.xmuse.feature.setting.SettingActivity
import com.ruiheng.xmuse.feature.setting.SettingInfoMenuItem
import com.ruiheng.xmuse.feature.setting.SettingViewModel
import com.ruiheng.xmuse.feature.setting.databinding.FragmentSettingBinding
import java.util.Locale

class SettingHomeFragment : BaseSettingFragment<FragmentSettingBinding>(),
    IBasePageErrorView {
    override fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ) = FragmentSettingBinding.inflate(inflater, container, false)

    private val settingViewModel: SettingViewModel by lazy {
        (requireActivity() as SettingActivity).settingViewModel
    }

    override fun bindTitleRes() = com.ruiheng.xmuse.core.ui.R.string.setting

    private var infoAdapter: PublicNormalMenuRVAdapter<SettingInfoMenuItem> by autoCleared()

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        val appName =
            "${AppUtils.getAppName()}(${AppUtils.getAppVersionName()})"
        val appText =
            if (RetrofitNetworkModule.IS_LOCAL_TEST) "${appName}(本地)" else if (RetrofitNetworkModule.IS_TEST) "${appName}(测试)" else appName
        binding.tvApp.text = appText
        binding.btnPublishVersion.visibility =
            if (RetrofitNetworkModule.IS_LOCAL_TEST || RetrofitNetworkModule.IS_TEST) View.VISIBLE else View.GONE
        binding.btnPublishVersion.setOnClickListener(this)
        binding.imgLogo.setOnLongClickListener {
            startApp(isLocalTest = false, isTest = true)
            true
        }
        binding.tvApp.setOnLongClickListener {
            startApp(isLocalTest = true, isTest = true)
            true
        }

        autoRemoveFlow(settingViewModel.fetchLatestVersionStateFlow) { result ->
            result.controlLoading(this)
        }

        infoAdapter = PublicNormalMenuRVAdapter { menu ->
            when (menu.title) {
                com.ruiheng.xmuse.core.ui.R.string.app_version_check -> {
//                    openAppMarket(requireContext())
                    val fetchVersion = settingViewModel.fetchLatestVersionStateFlow.value.data
                    if (fetchVersion == null) {
                        ToastUtils.showShort("当前为最新版本")
                    } else {
                        val intent = Intent(requireContext(), AppUpgradeActivity::class.java)
                        startActivity(intent)
                    }
                }

                com.ruiheng.xmuse.core.ui.R.string.user_agreement -> {
                    AgentWebActivity.start(requireContext(), userProtocol, "")
                }

                com.ruiheng.xmuse.core.ui.R.string.privacy_policy -> {
                    AgentWebActivity.start(requireContext(), privacyPolicy, "")
                }

                com.ruiheng.xmuse.core.ui.R.string.contact -> {
                    startNavPage(R.id.actionToFeedback)
                }

                com.ruiheng.xmuse.core.ui.R.string.feedback -> {
//                    startFeedback()
                    startNavPage(R.id.actionToFeedback)
                }
            }
        }
        binding.rvSettingInfo.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        binding.rvSettingInfo.adapter = infoAdapter
        autoStoppedFlow(settingViewModel.settingListStateFlow) {
            infoAdapter.submitList(it)
        }
    }


    private fun startApp(isLocalTest: Boolean, isTest: Boolean) {
        autoRemoveFlow(settingViewModel.startApp(isLocalTest, isTest)) { result ->
            if (result.isLoading()) {
                val text =
                    if (isLocalTest) "切换本地环境" else if (isTest) "切换测试环境" else "切换正式服"
                ToastUtils.showLong("${text},需要重启App")
            }
            if (result.isSuccessWithData()) {
                AppUtils.exitApp()
//                ActivityUtils.finishAllActivities()
            }
        }
    }
    //    private fun startDownloadUrl() {
//        val version = settingViewModel.fetchLatestVersionStateFlow.value.data ?: return
//        val fullDownloadPath = version.fullDownloadPath ?: return
//        autoRemoveFlow(settingViewModel.startFileDownload(fullDownloadPath, "name.cpp")) { result ->
//            result.controlLoading(this)
//            Timber.d("!!!!!Progress:${result}")
//        }
//    }

    private fun startFeedback() {
        FeedbackAPI.init(Utils.getApp(), aliyunAppKey, aliyunAppScret)
        FeedbackAPI.openFeedbackActivity()
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btnPublishVersion -> {
                startApp(false, isTest = false)
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }


}