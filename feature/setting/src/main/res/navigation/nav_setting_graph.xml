<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_cat_add_graph"
    app:startDestination="@id/settingHome">

    <fragment
        android:id="@+id/settingHome"
        android:name="com.ruiheng.xmuse.feature.setting.ui.SettingHomeFragment"
        tools:layout="@layout/fragment_setting">
        <action
            android:id="@+id/actionToFeedback"
            app:destination="@id/feedbackPage" />
    </fragment>


    <fragment
        android:id="@+id/feedbackPage"
        android:name="com.ruiheng.xmuse.feature.setting.ui.SettingFeedbackFragment"
        tools:layout="@layout/fragment_feedback">

    </fragment>
</navigation>