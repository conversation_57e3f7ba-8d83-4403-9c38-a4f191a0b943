<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black64">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/space48"
        android:layout_marginEnd="@dimen/space48"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="268:368"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            app:shapeAppearance="?attr/shapeAppearanceCornerMedium"
            app:srcCompat="?colorSurface" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_upgrade_bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="268:129"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="?attr/shapeAppearanceCornerMedium"
            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Top"
            tools:src="@drawable/img_upgrade" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_new_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_corner_chip_bg"
            android:paddingStart="@dimen/half_normal_margin"
            android:paddingTop="2dp"
            android:paddingEnd="@dimen/half_normal_margin"
            android:paddingBottom="2dp"
            android:textColor="?colorOnPrimary"
            android:textSize="@dimen/font_small"
            app:layout_constraintBottom_toBottomOf="@id/img_upgrade_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.13"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="V1.2.0" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/space12"
            android:layout_marginEnd="@dimen/space24"
            android:layout_marginBottom="@dimen/space12"
            app:layout_constraintBottom_toTopOf="@id/layout_action_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_new_version"
            app:layout_constraintTop_toBottomOf="@id/img_upgrade_bg">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_app_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4dp"
                    android:textColor="?colorOnSurface"
                    android:textSize="@dimen/font_normal"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="更新内容：\n1.优化产品体验，修复若干问题；\n2.新增白噪音功能；\n3.课程功能上线。" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_action_container"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/space24"
            android:layout_marginEnd="@dimen/space24"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/btn_cancel">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/upgrade_now"
                android:textColor="?colorOnPrimary"
                android:textSize="@dimen/font_normal"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_market"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/app_store"
                android:textColor="?colorOnSecondary"
                android:textSize="@dimen/font_small"
                app:backgroundTint="@color/colorSecondary"
                app:layout_constraintStart_toEndOf="@id/btn_sure"
                app:layout_constraintTop_toBottomOf="@id/btn_sure" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_loading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/upgrade_now"
                android:textColor="?colorOnPrimary"
                android:textSize="@dimen/font_normal"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_install"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/install_now"
                android:textColor="?colorOnPrimary"
                android:textSize="@dimen/font_normal"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/space12"
            android:text="@string/upgrade_later"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>