<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurfaceDim">


    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/activity_vertical_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_logo"
            android:layout_width="72dp"
            android:layout_height="72dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="?attr/shapeAppearanceCornerMedium"
            app:srcCompat="@mipmap/ic_launcher" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_app"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/half_normal_margin"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_normal"
            app:layout_constraintEnd_toEndOf="@id/img_logo"
            app:layout_constraintStart_toStartOf="@id/img_logo"
            app:layout_constraintTop_toBottomOf="@id/img_logo"
            tools:text="妙诗App" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_account_manager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:text="@string/data_setting"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_normal"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_app" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_account_manager"
            style="?attr/materialCardViewFilledStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_account_manager">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_account_relate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:text="@string/about"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_normal"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_account_manager" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_account_relate"
            style="?attr/materialCardViewFilledStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            app:layout_constraintTop_toBottomOf="@id/tv_account_relate">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_setting_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_public_menu_row" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/half_normal_margin"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_publish_version"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            android:layout_marginEnd="@dimen/activity_horizontal_margin"
            android:text="进入正式服"
            android:textSize="@dimen/font_normal"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_beian"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/half_normal_margin"
            android:text="App备案号：闽ICP备2024061439号-2A"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_publish_version" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>