package com.ruiheng.xmuse.core.common.result.permission

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.fragment.app.Fragment
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions

fun Activity.requestDeviceBlePermission(granted: () -> Unit) {
    XXPermissions.with(this).apply {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permission(Permission.Group.BLUETOOTH)
        } else {
            permission(Permission.ACCESS_FINE_LOCATION, Permission.ACCESS_COARSE_LOCATION)
        }.request(object : OnPermissionCallback {
            override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                if (!allGranted) {
                    return
                }
                granted.invoke()
            }

            override fun onDenied(permissions: MutableList<String>, doNotAskAgain: <PERSON>olean) {
                if (doNotAskAgain) {
                    XXPermissions.startPermissionActivity(
                        this@requestDeviceBlePermission,
                        permissions
                    )
                }
            }
        })
    }
}

fun Context.requestDeviceBlePermission(granted: () -> Unit) {
    XXPermissions.with(this).apply {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permission(Permission.Group.BLUETOOTH)
        } else {
            permission(Permission.ACCESS_FINE_LOCATION, Permission.ACCESS_COARSE_LOCATION)
        }.request(object : OnPermissionCallback {
            override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                if (!allGranted) {
                    return
                }
                granted.invoke()
            }

            override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                if (doNotAskAgain) {
                    XXPermissions.startPermissionActivity(
                        this@requestDeviceBlePermission,
                        permissions
                    )
                }
            }
        })
    }
}

fun Fragment.requestStoragePermission(granted: () -> Unit) {
    XXPermissions.with(this).apply {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permission(Permission.Group.BLUETOOTH)
        } else {
            permission(Permission.ACCESS_FINE_LOCATION, Permission.ACCESS_COARSE_LOCATION)
        }.request(object : OnPermissionCallback {
            override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                if (!allGranted) {
                    return
                }
                granted.invoke()
            }

            override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                if (doNotAskAgain) {
                    XXPermissions.startPermissionActivity(
                        this@requestStoragePermission,
                        permissions
                    )
                }
            }
        })
    }
}