package com.ruiheng.xmuse.core.common.result.time

import org.w3c.dom.Text
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.Locale

const val utcStringPattern = "yyyy-MM-dd HH:mm:ss"

fun getUTCNowLocalDateTime(): LocalDateTime = Instant.now().atZone(ZoneOffset.UTC).toLocalDateTime()

fun getUTCNowTimeString(): String {
    // 获取当前 UTC 时间
    val instant = Instant.now()

    // 自定义格式：yyyy-MM-dd HH:mm:ss
    val formatter = DateTimeFormatter
        .ofPattern(utcStringPattern, Locale.getDefault())
        .withZone(ZoneOffset.UTC)

    // 格式化输出
    return formatter.format(instant)
}

fun String.getLocalDateTimeFromUTCString(): LocalDateTime {
    val formatter = DateTimeFormatter
        .ofPattern(utcStringPattern, Locale.getDefault())
        .withZone(ZoneOffset.UTC)
    val instant = Instant.from(formatter.parse(this))
    return instant.atZone(ZoneId.systemDefault()).toLocalDateTime()
}

fun LocalDateTime.formatMonthByLocale(locale: Locale = Locale.getDefault()): String {
    if (Locale.CHINESE.language.equals(locale.language)) {
        return "${this.monthValue}月"
    } else {
        return this.month.getDisplayName(TextStyle.FULL, locale)
    }
}

fun LocalDateTime.getSystemZoneEpochSecond(): Long {
    return this
        .atZone(ZoneId.systemDefault())  // 获取系统默认时区
        .toInstant()
        .epochSecond
}

fun LocalDateTime.formatterDate(pattern: String = "yyyy.MM.dd"): String =
    this.format(DateTimeFormatter.ofPattern(pattern))

fun LocalDateTime.formatterTime(pattern: String = "HH:mm"): String =
    this.format(DateTimeFormatter.ofPattern(pattern))

fun LocalDateTime.utcLocalDateTimeToString(): String {
    // 转换为 UTC 时间字符串
    val utcFormatter = DateTimeFormatter
        .ofPattern(utcStringPattern)
        .withZone(ZoneOffset.UTC)

    return this.atOffset(ZoneOffset.UTC).format(utcFormatter)
}

fun LocalDateTime.durationString(endTime: LocalDateTime?): String {
    if (endTime == null) return "0分钟"
    var duration = Duration.between(this, endTime)
    if (duration.isNegative) {
        duration = duration.negated()
    }
    val hours = duration.toHours().toInt()
    var minutes = (duration.toMinutes() % 60).toInt()
    if (hours == 0 && minutes == 0) {
        minutes += 1
    }
    if (hours == 0) return "${minutes}分钟"
    if (minutes == 0) return "${hours}小时"
    return "${hours}小时${minutes}分钟"
}

fun formatMinutesToHourMinute(totalMinutes: Float?): Pair<Int, Int> {
    val pairNull = Pair(0, 0)
    if (totalMinutes == null) return pairNull
    val totalSeconds = (totalMinutes * 60).toInt()
    if (totalSeconds < 0) return pairNull // 处理负数情况

    val minutes = totalSeconds / 60 // 总分钟数
    val hours = minutes / 60        // 小时数（取整数部分）
    val remainingMinutes = minutes % 60 // 剩余分钟数（不足1小时的部分）

    return when {
        hours > 0 && remainingMinutes > 0 -> Pair(hours, remainingMinutes)
        hours > 0 -> Pair(hours, 0)
        remainingMinutes > 0 -> Pair(0, remainingMinutes)
        else -> pairNull
    }
}
