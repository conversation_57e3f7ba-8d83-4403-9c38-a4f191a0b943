/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.core.common.result

import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.R
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import java.net.UnknownHostException
import java.net.UnknownServiceException

sealed interface Result<out T> {
    val data: T?

    data class Success<T>(override val data: T) : Result<T>
    data class Error<T>(
        val exception: Throwable,
        override val data: T? = null,
        val resultCode: Int? = null
    ) : Result<T>

    data class Empty<T>(override val data: T?):Result<T>

    data class Loading<T>(override val data: T? = null, val progress: Int = 0) : Result<T>
    data class InitialSuccess<T>(override val data: T? = null) : Result<T>

    fun isSuccessWithData() = this is Success && data != null

    fun isError() = this is Error

    fun hasData(): Boolean {
        if (data == null) return false
        if (data is List<*>) {
            return (data as List<*>).isNotEmpty()
        }
        return true
    }

    fun getErrorResultText(defaultError: String = "unknown error"): String {
        var msg = defaultError
        if (isError()) {
            val exception = (this as Error).exception
            if (exception is PetivityThrowable) {
                if (!exception.message.isNullOrEmpty()) {
                    msg = exception.message!!
                }
            }
            if (isNetError()) {
                msg = Utils.getApp().getString(R.string.Network_unavailable)
            }
        }
        return msg
    }

    fun showResult(
        showSuccess: Boolean = false,
        defaultSuccess: String = "success",
        defaultError: String = "出现了点问题~"
    ) {
        if (isError()) {
            val exception = (this as Error).exception
            var msg = defaultError
            if (exception is PetivityThrowable) {
                if (!exception.message.isNullOrEmpty()) {
                    msg = exception.message!!
                }
            }
            if (isNetError()) {
                msg = Utils.getApp().getString(R.string.Network_unavailable)
            }
            ToastUtils.showShort(msg)
        }
        if (isSuccessWithData() && showSuccess) {

        }
    }

    fun getErrorContent(defaultError: String = "出现了点问题~"): String? {
        if (isError()) {
            val exception = (this as Error).exception
            var msg = defaultError
            if (exception is PetivityThrowable) {
                if (!exception.message.isNullOrEmpty()) {
                    msg = exception.message!!
                }
            }
            if (isNetError()) {
                msg = Utils.getApp().getString(R.string.Network_unavailable)
            }
            return msg
        }
        return null
    }

    fun isNetError(): Boolean {
        fun isExceptionNetError(exception: Throwable?) = (exception is UnknownHostException
                || exception is UnknownServiceException)
        if (this is Error) {
            if (isExceptionNetError(exception)) return true
            val causeException = exception.cause
            if (isExceptionNetError(causeException)) return true

        }
        return false
    }
}

fun Result<Any?>?.isLoading() = this != null && this is Result.Loading

fun <T> Flow<T>.asResult(): Flow<Result<T>> = map<T, Result<T>> { Result.Success(it) }
    .onStart { emit(Result.Loading()) }
    .catch { emit(Result.Error(it)) }

open class PetivityThrowable(
    override val message: String? = null,
    override val cause: Throwable? = null
) : Throwable(message, cause) {}