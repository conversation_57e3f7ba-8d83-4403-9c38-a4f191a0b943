package com.ruiheng.xmuse.core.common.result.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import androidx.annotation.RequiresPermission


fun Context.isBluetoothEnabled(): Boolean {
    val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    val adapter = bluetoothManager.adapter
    return adapter?.isEnabled == true
}

@RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
fun Context.openBluetoothSettings(){
    val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
    startActivity(enableBtIntent)
}