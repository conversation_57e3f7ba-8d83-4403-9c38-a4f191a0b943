buildscript {

}

/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


// Root build.gradle.kts
//buildscript {
//    allprojects {
//        configurations.all {
//            resolutionStrategy.dependencySubstitution {
//                substitute(module("org.jetbrains.compose.compiler:compiler")).apply {
//                    using(module("androidx.compose.compiler:compiler:${VersionsParser.Versions.Android.Compose.compiler}"))
//                }
//            }
//        }
//    }
//
//}

ext {
    set("namespacePrefix", "com.ruiheng.xmuse")
}

tasks.register("signingReport", Exec::class) {
    group = "Android"
    description = "Displays the signing info for each variant"
    commandLine = listOf("gradlew", "signingReport")
}