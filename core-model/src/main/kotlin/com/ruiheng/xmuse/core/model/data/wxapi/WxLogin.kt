package com.ruiheng.xmuse.core.model.data.wxapi

data class WxLogin(
    val access_token: String?,
    val expires_in: Int,
    val refresh_token: String,
    val openid: String,
    val scope: String,
    val unionid: String,
    val errcode: Int,
    val errmsg: String
) {
    companion object {
        fun createEmpty(): WxLogin {
            return WxLogin(
                "",
                0,
                "",
                "",
                "",
                "",
                -1,
                ""
            )
        }
    }
}