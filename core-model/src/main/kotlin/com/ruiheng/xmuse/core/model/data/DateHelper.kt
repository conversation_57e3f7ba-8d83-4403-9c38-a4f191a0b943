package com.ruiheng.xmuse.core.model.data

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object DateHelper {

    val apiRequestDateFormat = "yyyy-MM-dd'T'HH:mm:ss"
    val apiRequestNewDateFormat = "HH:mm"
    val apiRequestScheduleMealTimeFormat = "HH:mm"
    fun getRequestDateFormat(time: String): Date {
        val date: Date = try {
            SimpleDateFormat(apiRequestDateFormat, Locale.getDefault()).parse(time)
        } catch (e: Exception) {
            SimpleDateFormat(apiRequestNewDateFormat, Locale.getDefault()).parse(time)
        }
        return date
    }

}