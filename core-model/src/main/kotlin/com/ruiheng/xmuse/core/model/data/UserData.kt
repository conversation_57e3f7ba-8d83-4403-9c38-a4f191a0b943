package com.ruiheng.xmuse.core.model.data

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

data class UserData(
    val uid: String,
    val nickName: String?,
    val phone: String?,
    val headPortrait: String?,
    val email: String?,
    val judgeInputBind: Boolean?,
    val deviceId: String?,
    val token: String?,
    val judgeInputPassword: Boolean?,
    val judgeInputPhone: Boolean?,
    val selected: Boolean,
    val gender: Int?, //1 male 0 female
    val birthday: String?,
    val bpAioToken: UserAioTokenData?
) {
    fun bindDisplayPhoneNum(): String {
        if (phone.isNullOrEmpty()) return ""
        return if (phone.length == 11) {
            phone.replaceRange(3, 7, "****")
        } else {
            phone // 如果手机号长度不为11，返回原始字符串
        }
    }

    fun parseBirthYear(): Int {
        val formats = listOf(
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd"
        )
        if (birthday.isNullOrEmpty()) return -1
        for (format in formats) {
            try {
                val formatter = DateTimeFormatter.ofPattern(format)
                val localDate = LocalDate.parse(birthday, formatter)
                return localDate.year
            } catch (e: DateTimeParseException) {
                // 尝试下一个格式
            }
        }
        return -1
    }
}

data class UserAioTokenData(
    val code: String?,
    val jwt_token: String?,
    val message: String?,
    val uid: String?
)


data class UserThirdParty(
    val headPortrait: String? = null,
    val nickName: String? = null,
    val thirdType: Int? = null,
    val unionId: String? = null,
)