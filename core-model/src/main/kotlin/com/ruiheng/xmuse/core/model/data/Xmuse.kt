package com.ruiheng.xmuse.core.model.data

data class Xmuse(
    var name: String? = null,
    var mac: String? = null,
    var rssi: Double? = null,
    var battery: Int? = null,
    var firmware: String? = null,
    var sn:String? = null,
) {
    fun getBatteryText() = if (battery != null && battery!! >= 0) "${battery}%" else "--"

    fun getRssiText() = if (rssi != null) {
        when {
            rssi == null -> "--"
            (rssi!! > -60) -> "强"
            (rssi!! > -70) -> "良"
            (rssi!! > -80) -> "差"
            else -> "极差"
        }
    } else "--"
}