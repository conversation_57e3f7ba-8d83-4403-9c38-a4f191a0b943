# 新增配置API接口实现文档

本文档展示了如何在Xmuse-Android项目中新增配置API接口 `https://api-fort.itingluo.com/apiv1/kvconfig/items/xmusedomain` 的完整实现过程。

## 实现概述

按照项目的分层架构，从底层到上层依次实现：
1. **Network层**: 定义数据模型和Service接口
2. **Data层**: 实现Repository进行数据处理
3. **Presentation层**: 在ViewModel中暴露给UI使用

## 新增和修改的文件

### 1. 数据模型 (KvConfigItem)

**文件**: `core-network/src/main/java/com/ruiheng/xmuse/core/network/model/other/AppVersion.kt`

在现有文件末尾添加：

```kotlin
/**
 * KV配置项数据模型
 * 用于接收远程配置信息
 */
data class KvConfigItem(
    val key: String,
    val value: String,
    val description: String? = null,
    val createTime: String? = null,
    val updateTime: String? = null
)
```

### 2. 配置服务接口 (ConfigService)

**新建文件**: `core-network/src/main/java/com/ruiheng/xmuse/core/network/other/ConfigService.kt`

```kotlin
package com.ruiheng.xmuse.core.network.other

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import retrofit2.http.GET

/**
 * 配置服务接口
 * 用于获取远程配置信息
 */
interface ConfigService {

    /**
     * 获取xmuse域名配置
     * [API](https://api-fort.itingluo.com/apiv1/kvconfig/items/xmusedomain)
     */
    @GET("apiv1/kvconfig/items/xmusedomain")
    suspend fun getXmuseDomainConfig(): ApiRequestResult<KvConfigItem>
}
```

### 3. Retrofit网络模块修改

**文件**: `core-network/src/main/java/com/ruiheng/xmuse/core/network/RetrofitNetworkModule.kt`

#### 添加导入
```kotlin
import com.ruiheng.xmuse.core.network.other.ConfigService
```

#### 添加Provider方法
```kotlin
@Provides
@Singleton
fun provideRetrofitConfigService(resultInterceptor: RequestResultInterceptor? = null): ConfigService {
    return provideConfigRetrofitClient(resultInterceptor).create(ConfigService::class.java)
}
```

#### 添加专用Retrofit客户端
```kotlin
/**
 * 提供配置服务的Retrofit客户端
 * 使用不同的baseUrl: https://api-fort.itingluo.com/
 */
private fun provideConfigRetrofitClient(resultInterceptor: RequestResultInterceptor? = null): Retrofit {
    val logging = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }
    val okHttpClient = OkHttpClient.Builder().apply {
        if (resultInterceptor != null) {
            addInterceptor(resultInterceptor)
        }
        // 配置服务可能不需要用户token，根据实际需求决定是否添加
        // addInterceptor { provideUserTokenHeader(it) }
        addInterceptor(logging)
    }.build()

    return Retrofit.Builder()
        .baseUrl("https://api-fort.itingluo.com/")
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
}
```

### 4. 网络资源仓库

**新建文件**: `core-data/src/main/java/com/ruiheng/xmuse/core/data/repository/net/NetResourcesConfigRepository.kt`

```kotlin
package com.ruiheng.xmuse.core.data.repository.net

import androidx.annotation.WorkerThread
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import com.ruiheng.xmuse.core.network.other.ConfigService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 配置相关的网络资源仓库
 * 负责获取远程配置信息
 */
@Singleton
class NetResourcesConfigRepository @Inject constructor(
    private val configService: ConfigService
) {

    /**
     * 获取Xmuse域名配置
     */
    @WorkerThread
    fun getXmuseDomainConfig() = flow<Result<KvConfigItem>> {
        emit(Result.Loading())
        try {
            val response = configService.getXmuseDomainConfig()
            if (response.code == 200 && response.data != null) {
                emit(Result.Success(response.data))
            } else {
                emit(Result.Error(PetivityThrowable(response.msg ?: "获取配置失败")))
            }
        } catch (e: Exception) {
            emit(Result.Error(PetivityThrowable(e.message ?: "网络请求失败")))
        }
    }.flowOn(Dispatchers.IO)
}
```

### 5. SettingRepository修改

**文件**: `core-data/src/main/java/com/ruiheng/xmuse/core/data/repository/SettingRepository.kt`

#### 添加导入
```kotlin
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesConfigRepository
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
```

#### 修改构造函数
```kotlin
@Singleton
class SettingRepository @Inject constructor(
    @ApplicationScope private val appScope: CoroutineScope,
    private val roomHelper: RoomHelper,
    private val configRepository: NetResourcesConfigRepository
) {
```

#### 添加方法
```kotlin
/**
 * 获取Xmuse域名配置
 */
@WorkerThread
fun getXmuseDomainConfig() = configRepository.getXmuseDomainConfig()
```

### 6. ViewModel层修改

**文件**: `feature/setting/src/main/java/com/ruiheng/xmuse/feature/setting/SettingViewModel.kt`

#### 添加导入
```kotlin
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
```

#### 添加StateFlow
```kotlin
private val _xmuseDomainConfigStateFlow =
    MutableStateFlow<Result<KvConfigItem>>(Result.Loading())
val xmuseDomainConfigStateFlow = _xmuseDomainConfigStateFlow as StateFlow<Result<KvConfigItem>>
```

#### 添加方法
```kotlin
/**
 * 获取Xmuse域名配置
 */
fun getXmuseDomainConfig() {
    viewModelScope.launch {
        settingRepository.getXmuseDomainConfig().collect {
            _xmuseDomainConfigStateFlow.value = it
        }
    }
}
```

## 使用示例

### 在Activity或Fragment中使用

```kotlin
class SettingActivity : AppCompatActivity() {
    
    private val settingViewModel: SettingViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取域名配置
        settingViewModel.getXmuseDomainConfig()
        
        // 观察配置结果
        lifecycleScope.launch {
            settingViewModel.xmuseDomainConfigStateFlow.collect { result ->
                when (result) {
                    is Result.Loading -> {
                        // 显示加载状态
                        showLoading()
                    }
                    is Result.Success -> {
                        // 处理成功结果
                        val config = result.data
                        handleDomainConfig(config)
                    }
                    is Result.Error -> {
                        // 处理错误
                        showError(result.exception.message)
                    }
                }
            }
        }
    }
    
    private fun handleDomainConfig(config: KvConfigItem) {
        // 使用配置信息
        val domainValue = config.value
        // 更新UI或保存配置
        Log.d("Config", "Domain: ${config.key} = ${config.value}")
    }
}
```

## 实现特点

1. **独立域名**: 使用独立的域名，创建了专门的Retrofit客户端
2. **遵循架构**: 严格按照项目的Clean Architecture分层实现
3. **依赖注入**: 使用Hilt进行依赖注入管理
4. **响应式编程**: 使用Flow和StateFlow进行数据流处理
5. **错误处理**: 包含完整的错误处理机制
6. **可扩展性**: 可以轻松添加更多配置接口

## 业务集成实现

### 7. HomeViewModel业务集成

**文件**: `brainprint/src/main/java/com/ruiheng/xmuse/ui/home/<USER>

#### 添加导入
```kotlin
import com.ruiheng.xmuse.core.data.repository.SettingRepository
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
```

#### 修改构造函数
```kotlin
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val userDataRepository: UserDataRepository,
    private val museConnector: MuseConnector,
    private val netResourcesUserRepository: NetResourcesUserRepository,
    private val settingRepository: SettingRepository
) : ViewModel() {
```

#### 添加配置状态管理
```kotlin
private val _xmuseDomainConfigStateFlow = MutableStateFlow<Result<KvConfigItem>>(Result.Loading())
val xmuseDomainConfigStateFlow: StateFlow<Result<KvConfigItem>> = _xmuseDomainConfigStateFlow

/**
 * 获取Xmuse域名配置
 */
fun getXmuseDomainConfig() {
    viewModelScope.launch {
        settingRepository.getXmuseDomainConfig().collect {
            _xmuseDomainConfigStateFlow.value = it
        }
    }
}

/**
 * 确保配置已加载的私有方法
 */
private suspend fun ensureConfigLoaded() {
    if (_xmuseDomainConfigStateFlow.value !is Result.Success) {
        Log.d("HomeViewModel", "配置未成功加载，重新获取...")
        try {
            settingRepository.getXmuseDomainConfig().collect { result ->
                Log.d("HomeViewModel", "重新获取配置结果: $result")
                _xmuseDomainConfigStateFlow.value = result
                return@collect
            }
        } catch (e: Exception) {
            Log.e("HomeViewModel", "获取配置失败", e)
        }
    }
}

/**
 * 获取配置URL的通用方法
 */
private suspend fun getConfigUrl(
    urlType: String,
    urlExtractor: (KvConfigItem) -> String,
    defaultUrl: String
): String {
    Log.d("HomeViewModel", "开始获取${urlType}URL...")
    Log.d("HomeViewModel", "当前配置状态: ${_xmuseDomainConfigStateFlow.value}")

    // 确保配置已加载
    ensureConfigLoaded()

    // 返回配置中的指定字段或默认URL
    val finalUrl = when (val configResult = _xmuseDomainConfigStateFlow.value) {
        is Result.Success -> {
            val url = urlExtractor(configResult.data)
            Log.d("HomeViewModel", "使用配置${urlType}URL: $url")
            url
        }
        else -> {
            Log.d("HomeViewModel", "使用默认${urlType}URL: $defaultUrl")
            defaultUrl
        }
    }

    Log.d("HomeViewModel", "最终返回${urlType}URL: $finalUrl")
    return finalUrl
}

/**
 * 获取心理健康评估的URL，优先使用配置接口返回的home字段，失败时使用默认URL
 */
suspend fun getMentalHealthAssessmentUrl(): String {
    return getConfigUrl(
        urlType = "心理健康评估",
        urlExtractor = { it.home },
        defaultUrl = com.ruiheng.xmuse.core.network.RetrofitNetworkModule.USER_PROFILE_URL
    )
}

/**
 * 获取报告页面的URL，优先使用配置接口返回的report字段，失败时使用默认URL
 */
suspend fun getReportUrl(): String {
    return getConfigUrl(
        urlType = "报告",
        urlExtractor = { it.report },
        defaultUrl = ""
    )
}
```

### 8. HomeFragment业务集成

**文件**: `brainprint/src/main/java/com/ruiheng/xmuse/ui/home/<USER>

#### 初始化时获取配置
```kotlin
// 在initView方法中添加
// 初始化时获取域名配置
homeViewModel.getXmuseDomainConfig()
```

#### 修改点击事件处理
```kotlin
binding.layoutMentalHealthAssessment -> {
    val userToken = RetrofitNetworkModule.provideUserToken() ?: ""
    // 使用协程获取配置URL
    lifecycleScope.launch {
        val assessmentUrl = homeViewModel.getMentalHealthAssessmentUrl()
        Log.v("zcy", "Mental Health Assessment URL: $assessmentUrl")
        BPWebActivity.start(requireContext(), assessmentUrl, userToken)
    }
}
```

#### 添加必要导入
```kotlin
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
```

### 9. BPWebActivity URL修复

**文件**: `brainprint/src/main/java/com/ruiheng/xmuse/ui/web/BPWebActivity.kt`

#### 修复硬编码URL问题
```kotlin
// 修改前
.go("file:////android_asset/jsdemo.html")

// 修改后
.go(webUrl ?: "file:////android_asset/jsdemo.html")
```

## 完整业务流程

1. **应用启动**: HomeFragment初始化时调用 `homeViewModel.getXmuseDomainConfig()` 获取配置
2. **配置获取**: 通过配置API获取 `{"home":"https://report-sit.itingluo.com/xmuse/home","report":"https://report-sit.itingluo.com/xmuse/report-detail"}`
3. **URL选择**: `getMentalHealthAssessmentUrl()` 方法优先返回配置中的 `home` 字段，失败时返回默认URL
4. **页面跳转**: 点击心理健康评估时，使用动态获取的URL跳转到BPWebActivity
5. **异常处理**: 配置获取失败时自动降级到默认URL，保证业务连续性

## 数据模型更新

根据实际API返回格式，更新了 `KvConfigItem` 数据模型：

```kotlin
data class KvConfigItem(
    val home: String,
    val report: String
)
```

对应API返回的JSON结构：
```json
{
    "code": 20000,
    "message": "OK",
    "data": {
        "home": "https://report-sit.itingluo.com/xmuse/home",
        "report": "https://report-sit.itingluo.com/xmuse/report-detail"
    }
}
```

### 10. 报告页面URL配置

**文件**: `brainprint/src/main/java/com/ruiheng/xmuse/ui/home/<USER>

#### 添加报告URL获取方法
```kotlin
/**
 * 获取报告页面的URL，优先使用配置接口返回的report字段，失败时使用默认URL
 */
suspend fun getReportUrl(): String {
    Log.d("HomeViewModel", "开始获取报告URL...")
    Log.d("HomeViewModel", "当前配置状态: ${_xmuseDomainConfigStateFlow.value}")

    // 如果配置还没有成功加载，尝试重新获取
    if (_xmuseDomainConfigStateFlow.value !is Result.Success) {
        Log.d("HomeViewModel", "配置未成功加载，重新获取...")
        try {
            // 直接获取一次配置结果
            settingRepository.getXmuseDomainConfig().collect { result ->
                Log.d("HomeViewModel", "重新获取配置结果: $result")
                _xmuseDomainConfigStateFlow.value = result
                // 无论成功失败都只取第一个结果
                return@collect
            }
        } catch (e: Exception) {
            Log.e("HomeViewModel", "获取配置失败", e)
            // 获取配置失败，继续使用默认URL
        }
    }

    // 返回配置中的report字段或默认URL
    val finalUrl = when (val configResult = _xmuseDomainConfigStateFlow.value) {
        is Result.Success -> {
            Log.d("HomeViewModel", "使用配置报告URL: ${configResult.data.report}")
            configResult.data.report
        }
        else -> {
            // 配置获取失败或正在加载时，使用默认的报告URL（可以是空字符串或其他默认值）
            val defaultReportUrl = ""
            Log.d("HomeViewModel", "使用默认报告URL: $defaultReportUrl")
            defaultReportUrl
        }
    }

    Log.d("HomeViewModel", "最终返回报告URL: $finalUrl")
    return finalUrl
}
```

### 11. DetectionActivity报告跳转修改

**文件**: `brainprint/src/main/java/com/ruiheng/xmuse/ui/detection/DetectionActivity.kt`

#### 添加必要导入
```kotlin
import android.util.Log
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
```

#### 修改报告跳转逻辑
```kotlin
private fun onCompleted() {
    fun loadDataAndJump() {
        val resultData = bpDetectionViewModel.cacheBusyMindData
        // 使用协程获取配置的报告URL
        lifecycleScope.launch {
            val reportUrl = homeViewModel.getReportUrl()
            Log.v("DetectionActivity", "Report URL: $reportUrl")
            BPWebActivity.start(this@DetectionActivity, reportUrl, resultData)
        }
    }
    BPCompletedDialog {
        loadDataAndJump()
    }.startShow(supportFragmentManager)
}
```

## 完整业务流程更新

1. **应用启动**: HomeFragment初始化时调用 `homeViewModel.getXmuseDomainConfig()` 获取配置
2. **配置获取**: 通过配置API获取 `{"home":"https://report-sit.itingluo.com/xmuse/home","report":"https://report-sit.itingluo.com/xmuse/report-detail"}`
3. **心理健康评估**: 点击心理健康评估按钮时，使用配置中的 `home` 字段URL
4. **脑电检测完成**: 检测完成后，使用配置中的 `report` 字段URL跳转到报告页面
5. **异常处理**: 配置获取失败时自动降级到默认URL，保证业务连续性

## 两个URL的使用场景

- **`home` 字段**: 用于心理健康评估页面跳转
- **`report` 字段**: 用于脑电检测完成后的报告页面跳转

## 注意事项

1. **异常处理**: 配置获取失败时自动使用默认URL，保证业务不中断
2. **原有业务保留**: 完全保留了原有的业务逻辑和默认URL
3. **动态配置**: 支持通过远程配置动态修改心理健康评估和报告页面URL
4. **日志记录**: 添加了详细的日志记录，便于调试和监控
5. **性能优化**: 配置在应用启动时预加载，避免用户点击时的等待时间
6. **双URL支持**: 同时支持心理健康评估和报告页面的动态配置
