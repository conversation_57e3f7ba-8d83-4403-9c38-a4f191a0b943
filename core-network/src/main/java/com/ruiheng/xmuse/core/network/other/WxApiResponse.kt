package com.ruiheng.xmuse.core.network.other

import android.util.Log
import retrofit2.Response
import timber.log.Timber
import java.lang.NumberFormatException
import java.util.regex.Pattern

/**
 * Common class used by API responses.
 * @param <T> the type of the response object
</T> */
sealed class WxApiResponse<T> {
    companion object {
        fun <T> create(error: Throwable): WxApiErrorResponse<T> {
            return WxApiErrorResponse(error.message ?: "unknown error", error)
        }

        fun <T> create(response: Response<T>): WxApiResponse<T> {
            Log.d("ApiResponse", "response.isSuccessful: " + response.isSuccessful)
            return if (response.isSuccessful) {
                val body = response.body()
                Log.d("ApiResponse", "body == null || response.code() == 204: ${body == null || response.code() == 204}")
                if (body == null || response.code() == 204) {
                    WxApiEmptyResponse()
                } else {
                    WxApiSuccessResponse(
                        body = body,
                        linkHeader = response.headers()?.get("link")
                    )
                }
            } else {
                val msg = response.errorBody()?.string()
                val errorMsg = if (msg.isNullOrEmpty()) {
                    response.message()
                } else {
                    msg
                }
                WxApiErrorResponse(errorMsg ?: "unknown error")
            }
        }
    }
}

class WxApiEmptyResponse<T> : WxApiResponse<T>()

data class WxApiSuccessResponse<T>(
    val body: T,
    val links: Map<String, String>
) : WxApiResponse<T>() {

    constructor(body: T, linkHeader: String?) : this(
        body = body,
        links = linkHeader?.extractLinks() ?: emptyMap()
    )

    val nextPage: Int? by lazy(LazyThreadSafetyMode.NONE) {
        links[NEXT_LINK]?.let { next ->
            val matcher = PAGE_PATTERN.matcher(next)
            if (!matcher.find() || matcher.groupCount() != 1) {
                null
            } else {
                try {
                    Integer.parseInt(matcher.group(1))
                } catch (ex: NumberFormatException) {
                    Timber.w("cannot parse next page from %s", next)
                    null
                }
            }
        }
    }

    companion object {
        private val LINK_PATTERN = Pattern.compile("<([^>]*)>[\\s]*;[\\s]*rel=\"([a-zA-Z0-9]+)\"")
        private val PAGE_PATTERN = Pattern.compile("\\bpage=(\\d+)")
        private const val NEXT_LINK = "next"

        private fun String.extractLinks(): Map<String, String> {
            val links = mutableMapOf<String, String>()
            val matcher = LINK_PATTERN.matcher(this)
            while (matcher.find()) {
                val count = matcher.groupCount()
                if (count == 2) {
                    links[matcher.group(2)] = matcher.group(1)
                }
            }
            return links
        }
    }
}

data class WxApiErrorResponse<T>(val errorMessage: String, var exception: Throwable? = null) : WxApiResponse<T>()