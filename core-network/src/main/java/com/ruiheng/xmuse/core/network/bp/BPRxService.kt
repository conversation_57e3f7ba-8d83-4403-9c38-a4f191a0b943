package com.ruiheng.xmuse.core.network.bp

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepReportInfo
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.network.model.user.RequestUserBondThird
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import io.reactivex.rxjava3.core.Observable
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface BPRxService {

    @Headers("Content-Type: application/json")
    @POST("agg/api/machine/collection/push-collection-data")
    fun pushSleepDataRx(@Body route: RequestBody): Observable<ApiRequestResult<Any>>

    @Headers("Content-Type: application/json")
    @POST("agg/api/machine/collection/end-collection")
    fun endSleepSessionRx(@Body route: RequestBody): Observable<ApiRequestResult<ApiRequestBPSessionInfo>>

}