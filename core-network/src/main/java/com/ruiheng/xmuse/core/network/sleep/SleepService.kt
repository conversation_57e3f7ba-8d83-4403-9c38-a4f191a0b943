package com.ruiheng.xmuse.core.network.sleep

import com.ruiheng.xmuse.core.network.model.ApiRequestPageResult
import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepInfo
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepReportInfo
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepUserTag
import io.reactivex.rxjava3.core.Observable

import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface SleepService {

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep-config/user-tag/list")
    suspend fun loadSleepUserTagList(@Body route: RequestBody): ApiRequestResult<List<ApiRequestSleepUserTag>>

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/start-sleep")
    suspend fun startSleep(@Body route: RequestBody): ApiRequestResult<ApiRequestSleepInfo>

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/sleep-report/list")
    suspend fun loadSleepRecordList(@Body route: RequestBody): ApiRequestResult<ApiRequestPageResult<ApiRequestSleepInfo>>

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/sleep-report/details")
    suspend fun loadSleepRecordDetail(@Body route: RequestBody): ApiRequestResult<ApiRequestSleepReportInfo>


    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/sleep-trend/details")
    suspend fun loadSleepRecordTrend(@Body route: RequestBody): ApiRequestResult<ApiRequestSleepReportInfo>

//    @Headers("Content-Type: application/json")
//    @POST("hc/api/course/detail")
//    suspend fun loadCourseDetail(@Body route: RequestBody): ApiRequestResult<RequestCourse>
//
//    @Headers("Content-Type: application/json")
//    @POST("ds/api/course-data/save")
//    suspend fun saveCourseRecord(@Body route: RequestBody): ApiRequestResult<Any>
//
//    @Headers("Content-Type: application/json")
//    @POST("/us/api/app/login")
//    suspend fun phoneLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

}