package com.ruiheng.xmuse.core.network.user

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.user.RequestUploadFile
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

interface FileService {

    companion object{
        const val OSS_PARENT = "eeg-data"
        const val APP_OSS_PARENT = "xmuse-app"
        const val APP_AVATAR = "avatar"
        const val APP_COURSE_RECORD_PARENT = "course"
        const val APP_COURSE_RECORD_PATH = "record"
    }

    @Multipart
    @POST("fs/api/file/upload-file-oss")
    suspend fun uploadFile(@Part file: MultipartBody.Part, @Part("subFolder") subFolder:RequestBody): ApiRequestResult<RequestUploadFile>
}