package com.ruiheng.xmuse.core.network.model.other

import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

data class AppVersion(

    val clientType: Int,//1：museDirect  2：museLab  3：App
    val createTime: String?, //2025-03-15 09:49:58
    val name: String?,
    val versionNum: Int,
    val requiredVersionFlag: Boolean?, //是否需要强制更新  true：需要  false：不需要
    val packageType: Int?, //更新包类型：0：全量  1：增量
    val downloadPath: String?,
    val fullDownloadPath: String?, //全量下载路径
    val remark: String?, //推送文案
    val remarkEn: String?
) {
    companion object {
        private val clientTypeAndroid = 3
        private val systemTypeAndroid = 3

        fun getLatestVersionParam() = arrayOf(
            Pair("name", AppUtils.getAppVersionName()),
            Pair("clientType", "$clientTypeAndroid"),
            Pair("systemType", "$systemTypeAndroid")
        )
    }
}

data class PlatformAppInfo(
    val id: String,
    val versionName: String,
    val downloadLink: String?,
    val versionNum: Int,
){
    fun checkAppStoreApp(latestVersion:AppVersion):Boolean{
        return !downloadLink.isNullOrEmpty() && versionNum >= latestVersion.versionNum
    }
}

/**
 * KV配置项数据模型
 * 用于接收远程配置信息
 */
data class KvConfigItem(
    val home: String,
    val report: String
)

/**
 * 配置API专用的响应模型
 * 对应API返回格式: {"code":20000,"message":"OK","data":{"home":"...","report":"..."}}
 */
data class ConfigApiResponse<T>(
    val code: Int,
    val message: String,
    val data: T?
)
