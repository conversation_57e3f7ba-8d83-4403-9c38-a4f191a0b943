package com.ruiheng.xmuse.core.network.util.gson

import com.google.gson.*
import com.google.gson.reflect.TypeToken
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.NullPointerException
import java.lang.reflect.Type

class BaseGsonConverterFactory private constructor(private var gson: Gson) : Converter.Factory() {

    companion object {
        fun create(): BaseGsonConverterFactory {
            val gBuilder = GsonBuilder()
            gBuilder.registerTypeAdapter(String::class.java, StringConverter())
            return create(gBuilder.create())
        }

        fun create(gson: Gson?): BaseGsonConverterFactory {
            if (gson == null) {
                throw NullPointerException("gson == null")
            }
            return BaseGsonConverterFactory(gson)
        }
    }

    override fun responseBodyConverter(
        type: Type,
        annotations: Array<Annotation>,
        retrofit: Retrofit
    ): Converter<ResponseBody, *>? {
        val adapter = gson.getAdapter(TypeToken.get(type))
        return BaseGsonResponseBodyConverter(gson, adapter)
    }

    override fun requestBodyConverter(
        type: Type,
        parameterAnnotations: Array<Annotation>,
        methodAnnotations: Array<Annotation>,
        retrofit: Retrofit
    ): Converter<*, RequestBody>? {
        val adapter = gson.getAdapter(TypeToken.get(type))
        return BaseGsonRequestBodyConverter(gson, adapter)
    }

}

class StringConverter : JsonSerializer<String>, JsonDeserializer<String> {
    override fun serialize(src: String?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {
        if (src == null) {
            return JsonPrimitive("")
        }
        return JsonPrimitive(src.toString())
    }

    override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): String {
        return json?.asJsonPrimitive?.asString ?: ""
    }

}