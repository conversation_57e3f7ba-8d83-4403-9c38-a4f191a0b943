package com.ruiheng.xmuse.core.network.course

import android.content.Context
import android.os.Environment
import com.blankj.utilcode.util.Utils
import java.io.File
import java.util.UUID

data class RequestCourseCategory(
    val courseLayout: String?,
    val moduleKey: String,
    val moduleName: String,
    val courseInfo: RequestCourseInfo
)

data class RequestCourseInfo(
    val total: Int?,
    val list: List<RequestCourse>
)

data class RequestCourse(
    val id: String,
    val code: String,
    val courseNumber: Int?,
    val details: String,
    val image1: String?,
    val image2: String?,
    val title: String,
    val moduleTags: List<RequestCourseMeditationTag>?,
    val meditationTags: List<RequestCourseMeditationTag>?,
    val postureTags: List<RequestCoursePostureTag>?,
    val courseNodes: List<RequestCourseNode>?,
    val version: String?
) {
    fun getCourseBankPath() = "guidance/${id}"
    fun getCourseZipFileName() = "guidance/${id}.zip"
    fun getCourseLink() = if (courseNodes.isNullOrEmpty()) null else courseNodes[0].guideLink

    fun getCourseMasterBankFile(context: Context): Pair<String, String>? {
        val appPrivateDownloadDir =
            Utils.getApp().getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
        val file = File(appPrivateDownloadDir, "${getCourseBankPath()}/Master.bank")
        val stringBankFile =
            File(appPrivateDownloadDir, "${getCourseBankPath()}/Master.strings.bank")
        if (file.exists() && stringBankFile.exists()) {
            return Pair(file.absolutePath, stringBankFile.absolutePath)
        }
        return null
    }
}

data class RequestCourseNode(
    val courseId: String,
    val guideLink: String,
)

data class RequestCourseMeditationTag(
    val id: String,
    val name: String
)

data class RequestCoursePostureTag(
    val id: String,
    val name: String
)

//data class RequestNoise(
//    val id: String = UUID.randomUUID().toString(),
//    val fileName: String,
//    val title: String,
//    val image: String
//) {
//
//    fun loadFileBankName() = "file:///android_asset/noise/${fileName}.bank"
//    fun loadFileEventName() = "event:/${fileName}"
//
//    companion object {
//        const val Noise_Master_Bank: String = "file:///android_asset/noise/Master.bank"
//        const val Noise_Master_String_Bank: String =
//            "file:///android_asset/noise/Master.strings.bank"
//
//        val fakeList: List<RequestNoise>
//
//        init {
//            fakeList = createNoiseList()
//        }
//
//        private fun createNoiseList() =
//            arrayListOf(
//                RequestNoise(fileName = "Sea_Noise", title = "大海", image = "img_noise_sea"),
//                RequestNoise(
//                    fileName = "Subtle_Noise",
//                    title = "暗噪音",
//                    image = "img_noise_subtle"
//                ),
//                RequestNoise(fileName = "Forest_Noise", title = "森林", image = "img_noise_forest"),
//                RequestNoise(
//                    fileName = "Rainforest_Noise",
//                    title = "雨林",
//                    image = "img_noise_rainforest"
//                ),
//                RequestNoise(fileName = "Raining_Noise", title = "雨天", image = "img_noise_rain"),
//                RequestNoise(
//                    fileName = "Spring_Raining_Noise",
//                    title = "春雨",
//                    image = "img_noise_spring_rain"
//                ),
//                RequestNoise(
//                    fileName = "Restaurant_Noise",
//                    title = "餐厅",
//                    image = "img_noise_restaurant"
//                )
//            )
//    }
//}