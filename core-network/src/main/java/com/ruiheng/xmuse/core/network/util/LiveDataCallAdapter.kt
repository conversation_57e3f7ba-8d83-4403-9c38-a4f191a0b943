package com.ruiheng.xmuse.core.network.util

import androidx.lifecycle.LiveData
import com.ruiheng.xmuse.core.network.other.WxApiResponse
import retrofit2.Call
import retrofit2.CallAdapter
import retrofit2.Response
import java.lang.reflect.Type
import java.util.concurrent.atomic.AtomicBoolean
import retrofit2.Callback

class LiveDataCallAdapter<R>(private val responseType: Type) :
    CallAdapter<R, LiveData<WxApiResponse<R>>> {

    override fun responseType(): Type = responseType

    override fun adapt(call: Call<R>): LiveData<WxApiResponse<R>> {
        return object : LiveData<WxApiResponse<R>>() {
            private var started = AtomicBoolean(false)
            override fun onActive() {
                super.onActive()
                if (started.compareAndSet(false, true)) {
                    call.enqueue(object : Callback<R> {
                        override fun onFailure(call: Call<R>, t: Throwable) {
                            postValue(WxApiResponse.create(t))
                        }

                        override fun onResponse(call: Call<R>, response: Response<R>) {
                            postValue(WxApiResponse.create(response))
                        }
                    })
                }
            }
        }
    }

}