package com.ruiheng.xmuse.core.network.other

import androidx.lifecycle.LiveData
import retrofit2.http.GET
import retrofit2.http.Query
import com.ruiheng.xmuse.core.model.data.wxapi.*
/**
 * 微信登录接口
 */
interface WxService {
    @GET("sns/oauth2/access_token")
    fun getAccessToken(
        @Query("appid") appId: String,
        @Query("secret") secret: String,
        @Query("code") code: String,
        @Query("grant_type") grant: String = "authorization_code"
    ): LiveData<WxApiResponse<WxLogin>>

    @GET("sns/userinfo")
    fun getWxUserInfo(
        @Query("access_token") accessToken: String,
        @Query("openid") openId: String
    ): LiveData<WxApiResponse<WxUserInfo>>

    @GET("sns/oauth2/refresh_token")
    fun refreshToken(
        @Query("appid") appId: String,
        @Query("refresh_token") refreshToken: String,
        @Query("grant_type") grant: String = "refresh_token"
    ): LiveData<WxApiResponse<WxLogin>>

    @GET("cgi-bin/token")
    fun getCodeAccessToken(
        @Query("grant_type") grant_type: String = "client_credential",
        @Query("appid") appId: String,
        @Query("secret") secret: String
    ): LiveData<WxApiResponse<WxAccessToken>>

    @GET("cgi-bin/ticket/getticket")
    fun getWxTicket(
        @Query("access_token") accessToken: String,
        @Query("type") type: Int = 2
    ): LiveData<WxApiResponse<WxTicket>>
}