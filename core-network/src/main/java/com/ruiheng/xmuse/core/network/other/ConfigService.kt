package com.ruiheng.xmuse.core.network.other

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.other.ConfigApiResponse
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import retrofit2.http.GET

/**
 * 配置服务接口
 * 用于获取远程配置信息
 */
interface ConfigService {

    /**
     * 获取xmuse域名配置
     * [API](https://api-fort.itingluo.com/apiv1/kvconfig/items/xmusedomain)
     */
    @GET("apiv1/kvconfig/items/xmusedomain")
    suspend fun getXmuseDomainConfig(): ConfigApiResponse<KvConfigItem>
}
