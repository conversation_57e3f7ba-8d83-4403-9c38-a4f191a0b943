package com.ruiheng.xmuse.core.network.model

import androidx.core.graphics.toColorInt
import com.ruiheng.xmuse.core.common.result.time.formatMinutesToHourMinute
import com.ruiheng.xmuse.core.common.result.time.getLocalDateTimeFromUTCString
import com.ruiheng.xmuse.core.common.result.time.getSystemZoneEpochSecond
import com.ruiheng.xmuse.core.common.result.time.getUTCNowLocalDateTime
import com.ruiheng.xmuse.core.common.result.time.getUTCNowTimeString
import com.ruiheng.xmuse.core.common.result.time.utcLocalDateTimeToString

data class ApiRequestSleepUserTag(val id: String, val code: String, val name: String)

data class ApiRequestSleepInfo(
    val endTime: String?,
    val startTime: String?,
    val status: Int,
    val updateBy: String,
    val updateTime: String,
    val dynamicModule: List<Float>?,
    val heartRateModule: List<Float>?,
    val hrvModule: List<Float>?,
    val postureModule: List<Float>?,
    val qualityScore: Int,
    val stageModule: List<Float>?,
    val duration: Long?,
    val reportId: String
) {
    var month: Int? = null
    var displayMonth: Boolean = false
}

data class ApiRequestSleepReportInfo(
    val endTime: String?,
    val startTime: String?,
    val postureModule: ApiRequestSleepReportPosture? = null,
    val stageModule: ApiRequestSleepReportStage? = null,
    val dynamicModule: ApiRequestSleepReportStillness? = null,
    val heartRateModule: ApiRequestSleepReportHeartRate? = null,
    val qualityScore: Int? = 0
) {

    companion object {
        fun createFake(): ApiRequestSleepReportInfo {
            val now = getUTCNowLocalDateTime()
            val end = now.plusHours(8)
            return ApiRequestSleepReportInfo(
                end.utcLocalDateTimeToString(),
                now.utcLocalDateTimeToString(),
                stageModule = ApiRequestSleepReportStage.createFake(),
                qualityScore = 60
            )
        }
    }

    fun bindSleepScore() = (qualityScore ?: 0).toInt()
    fun bindSleepScoreGrade() = when (bindSleepScore()) {
        in 91..100 -> "优秀"
        in 71..90 -> "良好"
        in 60..70 -> "一般"
        else -> "较差"
    }

    fun bindSleepScoreGradeColor(): IntArray = when (bindSleepScore()) {
        in 91..100 -> arrayOf("#2CC9B3", "#2CC9B3", "#9BF7B6").map { it.toColorInt() }.toIntArray()
        in 71..90 -> arrayOf("#618DF9", "#618DF8", "#61C6F9").map { it.toColorInt() }.toIntArray()
        in 60..70 -> arrayOf("#FF9326", "#FFB526", "#FFD85B").map { it.toColorInt() }.toIntArray()
        else -> arrayOf("#F5563B", "#FC7660", "#FC7660").map { it.toColorInt() }.toIntArray()
    }
}

data class ApiRequestSleepReportPosture(
    var colDataList: List<Float>,
    val startTime: String? = null,
    val endTime: String? = null,
    val colType: Int? = null,
    val posture1: Float? = null,
    val posture2: Float? = null,
    val posture3: Float? = null,
    val posture4: Float? = null,
    val posture5: Float? = null,
    val postureRate1: Float? = null,
    val postureRate2: Float? = null,
    val postureRate3: Float? = null,
    val postureRate4: Float? = null,
    val postureRate5: Float? = null
) {

    fun bindRateList() =
        arrayOf(postureRate1, postureRate2, postureRate3, postureRate4, postureRate5).map {
            ((it ?: 0f) * 100).toInt()
        }

    fun bindTimeCountList() = arrayOf(posture1, posture2, posture3, posture4, posture5).map {
        formatMinutesToHourMinute(it)
    }

    companion object {
        fun createFake(): ApiRequestSleepReportPosture {
            val utcNow = getUTCNowLocalDateTime()
            val utcEnd = utcNow.plusHours(8)
            return ApiRequestSleepReportPosture(
                colDataList = listOf(
                    -1f,
                    -1f,
                    -1f,
                    -1f,
                    4f,
                    4f,
                    4f,
                    4f,
                    4f,
                    4f,
                    4f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    1f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    3f,
                    3f,
                    3f,
                    3f,
                    3f,
                    3f,
                    3f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    2f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                    4f,
                    4f,
                    4f,
                    4f,
                    4f,
                    4f,

                    ), utcNow.utcLocalDateTimeToString(), utcEnd.utcLocalDateTimeToString()
            )
        }
    }
}

data class ApiRequestSleepReportStage(
    var colDataList: List<Float>?,
    val startTime: String? = null,
    val endTime: String? = null,
    val colType: Int? = null,
    val stage1: Float? = null,
    val stage2: Float? = null,
    val stage3: Float? = null,
    val stage4: Float? = null,
    val stageRate1: Float? = null,
    val stageRate2: Float? = null,
    val stageRate3: Float? = null,
    val stageRate4: Float? = null
) {
    fun bindRateList() = arrayOf(stageRate1, stageRate2, stageRate3, stageRate4).map {
        ((it ?: 0f) * 100).toInt()
    }

    fun bindTimeCountList() = arrayOf(stage1, stage2, stage3, stage4).map {
        formatMinutesToHourMinute(it)
    }

    companion object {
        fun createFake(): ApiRequestSleepReportStage {
            val utcNow = getUTCNowLocalDateTime()
            val utcEnd = utcNow.plusHours(8)
            return ApiRequestSleepReportStage(
                colDataList = listOf(
                    -1f,
                    -1f,
                    3f,
                    3f,
                    3f,
                    3f,
                    1f,
                    1f,
                    1f,
                    1f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                    0f,
                ), utcNow.utcLocalDateTimeToString(), utcEnd.utcLocalDateTimeToString()
            )
        }
    }

}

data class ApiRequestSleepReportStillness(
    var colDataList: List<Float>?,
    val startTime: String? = null,
    val endTime: String? = null,
    val colType: Int? = null,
    val stage1: Float? = null,
    val stage4: Float? = null,
)

data class ApiRequestSleepReportHeartRate(
    var colDataList: List<Float>?,
    val startTime: String? = null,
    val endTime: String? = null,
    val colType: Int? = null,
    val heartRate: Float? = null,
    val stage1: Float? = null,
    val stage4: Float? = null,
)

//data class ApiRequestSleepReportTrend(
//    val qualityScore:Int?,
//    val sumSleepTimeLong:Long
//    val heartRateSection: ApiRequestSleepReportTrendHeartSection?,
//    val sleepDynamicSection
//
//)
//
//data class ApiRequestSleepReportTrendDynamicSection(
//
//)

data class ApiRequestSleepReportTrendHeartSection(
    val aveValue: Float?,
    val evenDayData: List<ApiRequestSleepReportTrendHeartSectionItem>?,
    val lastAveValue: Float?,  //上一个周期的平均值
    val lastAveValueDiff: Float? //与上一个周期差值
)

data class ApiRequestSleepReportTrendHeartSectionItem(
    val curDay: String, // 2025-07-15
    val data: ApiRequestSleepReportTrendValue
)

data class ApiRequestSleepReportTrendValue(
    val heartRate: Float
)


