package com.ruiheng.xmuse.core.network.other

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.other.AppVersion
import com.ruiheng.xmuse.core.network.model.other.PlatformAppInfo
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.network.model.user.RequestUserBondThird
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface OtherService {

    /**
     * [API](http://10.130.79.21:3000/project/169/interface/api/37811)
     */
    @Headers("Content-Type: application/json")
    @POST("ams/api/version-control/get-latest-version-list")
    suspend fun getLatestVersionList(@Body route: RequestBody): ApiRequestResult<List<AppVersion>>

    @Headers("Content-Type: application/json")
    @POST("ams/api/app-version/get-by-type")
    suspend fun getPlatformStoreInfo(@Body route: RequestBody): ApiRequestResult<PlatformAppInfo>

}