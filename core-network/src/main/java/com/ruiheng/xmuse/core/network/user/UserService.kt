package com.ruiheng.xmuse.core.network.user

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.network.model.user.RequestUserBondThird
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface UserService {
    @Headers("Content-Type: application/json")
    @POST("us/api/user/send-sms")
    suspend fun sendSMS(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/send-email")
    suspend fun sendEmail(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/login")
    suspend fun phoneLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/email-login")
    suspend fun emailLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/login")
    suspend fun appLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/login")
    suspend fun oneKeyLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/first-reset-pwd")
    suspend fun configurePassword(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/reset-pwd")
    suspend fun resetPassword(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/email-reset-pwd")
    suspend fun emailResetPassword(@Body route: RequestBody): ApiRequestResult<Any>

    @POST("us/api/app/account/info-update")
    suspend fun updateUserInfo(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @POST("us/api/app/third-login")
    suspend fun thirdLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @POST("us/api/app/logout")
    suspend fun userLogout(@Body route: RequestBody): ApiRequestResult<Any>



    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-third-list")
    suspend fun loadThirdPartyList(@Body route: RequestBody): ApiRequestResult<List<RequestUserBondThird>>

    @POST("us/api/app/account/bind-third")
    suspend fun bindThirdAccount(@Body route: RequestBody): ApiRequestResult<RequestBindThirdPartyResult>

    @POST("us/api/app/account/un-bind-third")
    suspend fun unbindThirdAccount(@Body route: RequestBody): ApiRequestResult<List<RequestUserBondThird>>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-third-confirmation")
    suspend fun forceUpdateThirdParty(@Body route: RequestBody): ApiRequestResult<List<RequestUserBondThird>>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/update-pwd")
    suspend fun updatePassword(@Body route: RequestBody): ApiRequestResult<Any>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-phone")
    suspend fun bindPhone(@Body route: RequestBody): ApiRequestResult<RequestBindPhoneResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-email")
    suspend fun bindEmail(@Body route: RequestBody): ApiRequestResult<RequestBindPhoneResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-phone-confirmation")
    suspend fun forceUpdatePhone(@Body route: RequestBody): ApiRequestResult<RequestBindPhoneResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/bind-email-confirmation")
    suspend fun forceUpdateEmail(@Body route: RequestBody): ApiRequestResult<RequestBindPhoneResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/info")
    suspend fun loadUserInfo(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

    @Headers("Content-Type: application/json")
    @POST("ds/api/device/bind")
    suspend fun bindDevice(@Body route: RequestBody): ApiRequestResult<RequestDeviceBindResult>

    @Headers("Content-Type: application/json")
    @POST("us/api/app/account/write-off-account")
    suspend fun accountTermination(@Body route: RequestBody): ApiRequestResult<Any>
//    @POST("/api/auth/third-party-login")
//    suspend fun thirdPartyLogin(
//        @Field("third_party_provider") provider: Int,
//        @Field("third_party_id") thirdPartyId: String,
//        @Field("email") email: String,
//        @Field("name") name: String,
//        @Field("picture") picture: String,
//        @Field("timezone") timezone: String,
//        @Field("timestamp") timestamp: String,
//    ): GraphQLResponse<ApiUserResult>

//    @POST("/api/auth/thirdpartylogin")
//    suspend fun thirdPartyLogin(@Body apiUserRequest: ApiUserRequest): GraphQLResponse<ApiUserResult>

}