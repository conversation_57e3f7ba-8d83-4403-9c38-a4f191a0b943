package com.ruiheng.xmuse.core.network.sleep

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepInfo
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepUserTag
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Single

import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface SleepRxService {

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/push-sleep-data")
    fun pushSleepData(@Body route: RequestBody): Observable<ApiRequestResult<Any>>

    @Headers("Content-Type: application/json")
    @POST("agg/api/sleep/end-sleep")
    fun endSleepSession(@Body route: RequestBody): Observable<ApiRequestResult<Any>>

//    @Headers("Content-Type: application/json")
//    @POST("hc/api/course/detail")
//    suspend fun loadCourseDetail(@Body route: RequestBody): ApiRequestResult<RequestCourse>
//
//    @Headers("Content-Type: application/json")
//    @POST("ds/api/course-data/save")
//    suspend fun saveCourseRecord(@Body route: RequestBody): ApiRequestResult<Any>
//
//    @Headers("Content-Type: application/json")
//    @POST("/us/api/app/login")
//    suspend fun phoneLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

}