package com.ruiheng.xmuse.core.network.course

import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindThirdPartyResult
import com.ruiheng.xmuse.core.network.model.user.RequestUserBondThird
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface CourseService {

    @Headers("Content-Type: application/json")
    @POST("hc/api/course/home-data")
    suspend fun loadHomeCourseCategory(@Body route: RequestBody): ApiRequestResult<List<RequestCourseCategory>>

    @Headers("Content-Type: application/json")
    @POST("hc/api/course/detail")
    suspend fun loadCourseDetail(@Body route: RequestBody): ApiRequestResult<RequestCourse>

    @Headers("Content-Type: application/json")
    @POST("ds/api/course-data/save")
    suspend fun saveCourseRecord(@Body route: RequestBody): ApiRequestResult<Any>
//
//    @Headers("Content-Type: application/json")
//    @POST("/us/api/app/login")
//    suspend fun phoneLogin(@Body route: RequestBody): ApiRequestResult<RequestUserResult>

}