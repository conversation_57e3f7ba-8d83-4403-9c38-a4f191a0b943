package com.ruiheng.xmuse.core.network.bp

import java.io.Serializable

data class ApiRequestBPSessionInfo(
    val reportId: String?,
    val startTime: String?,
    val endTime: String?,
    val status: Int,
    val updateBy: String,
    val updateTime: String,
    val id: String?,

    val heartRate: String?,
    val hfNorm: String?,
    val lfNorm: String?,
    val lfVsHf: String?,
    val hrvHdnn: String?,
    val hrvPnn50: String?,
    val hrvRmssd: String?,
    val sneScore: String?,
    val vneScore: String?,
    val spo2: String?,
    val totalScore: String?
) : Serializable {
}