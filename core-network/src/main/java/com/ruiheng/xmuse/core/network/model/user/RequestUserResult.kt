package com.ruiheng.xmuse.core.network.model.user

import java.io.Serializable

data class RequestUserResult(
    val id: String,
    val nickName: String?,
    val headPortrait: String?,
    val phone: String?,
//    val thirdType: String?,
//    val unionId: String?,
    val token: String?,
    val judgeInputPassword: Boolean?,
    val judgeInputPhone: Boolean?,
    val lastLoginTime: String?,
    val source: Int?,
    val email: String?,
    val judgeInputBind: Boolean?,
    val deviceId: String?,
    val thirdList: List<RequestThirdPartyAccount>?,
    val gender: Int?,
    val birthday: String?,
    val aioToken: RequestBPAioInfo?
) : Serializable {

    fun bindDisplayPhoneNum(): String {
        if (phone.isNullOrEmpty()) return ""
        return if (phone.length == 11) {
            phone.replaceRange(3, 7, "****")
        } else {
            phone // 如果手机号长度不为11，返回原始字符串
        }
    }
}

data class RequestBPAioInfo(
    val code: String?,
    val jwt_token: String?,
    val message: String?,
    val uid: String?
)

data class RequestBindPhoneResult(
    val code: String,
    val result: RequestUserResult?,
    val curAccount: List<RequestUserResult>?,
    val switchBindCode: String?,
    val targetAccount: List<RequestUserResult>?
) : Serializable {
    companion object {
        private val bindPhoneResultSuccess = "2"
        private val updatePhoneResultSuccess = "0"

    }

    fun isBindPhoneSuccess() = code == bindPhoneResultSuccess && result != null
    fun isUpdatePhoneSuccess() = code == updatePhoneResultSuccess && result != null
}

data class RequestBindThirdPartyResult(
    val code: String,
    val thirdBindCode: String?,
    var thirdPartyType: Int?,
    val bindRelation: List<RequestUserBondThird>?,
    val curAccount: List<RequestUserResult>?,
    val targetAccount: List<RequestUserResult>?
) {
    companion object {
        private val thirdPartyBindSuccess = "0"
    }

    fun isThirdPartyBindSuccess() = code == thirdPartyBindSuccess && bindRelation != null

}

data class RequestUserBondThird(
    val headPortrait: String?,
    val id: String,
    val nickName: String?,
    val thirdType: Int?,
    val unionId: String?,
    val userId: String
)

data class RequestThirdPartyAccount(
    val thirdName: String?,
    val thirdHeadPortrait: String?,
    val thirdType: Int?
)
