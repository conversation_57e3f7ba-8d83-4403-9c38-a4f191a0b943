// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from session.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;

public final class SessionData {


    /*package*/ final int completedSeconds;

    /*package*/ final ArrayList<Integer> recoveryTimes;

    /*package*/ final ArrayList<Integer> birdTimes;

    /*package*/ final int birdCount;

    /*package*/ final int recoveryCount;

    /*package*/ final int totalPoints;

    public SessionData(
            int completedSeconds,
            ArrayList<Integer> recoveryTimes,
            ArrayList<Integer> birdTimes,
            int birdCount,
            int recoveryCount,
            int totalPoints) {
        this.completedSeconds = completedSeconds;
        this.recoveryTimes = recoveryTimes;
        this.birdTimes = birdTimes;
        this.birdCount = birdCount;
        this.recoveryCount = recoveryCount;
        this.totalPoints = totalPoints;
    }

    public int getCompletedSeconds() {
        return completedSeconds;
    }

    public ArrayList<Integer> getRecoveryTimes() {
        return recoveryTimes;
    }

    public ArrayList<Integer> getBirdTimes() {
        return birdTimes;
    }

    public int getBirdCount() {
        return birdCount;
    }

    public int getRecoveryCount() {
        return recoveryCount;
    }

    public int getTotalPoints() {
        return totalPoints;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "SessionData{" +
                "completedSeconds=" + completedSeconds +
                "," + "recoveryTimes=" + recoveryTimes +
                "," + "birdTimes=" + birdTimes +
                "," + "birdCount=" + birdCount +
                "," + "recoveryCount=" + recoveryCount +
                "," + "totalPoints=" + totalPoints +
        "}";
    }

}
