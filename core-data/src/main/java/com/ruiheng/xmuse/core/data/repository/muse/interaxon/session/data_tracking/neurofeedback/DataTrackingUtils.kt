package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

class DataTrackingUtils private constructor() {

    companion object {
        fun createGaplessTimeseries(
            fallbackInitialValue: Double,
            timeseries: java.util.ArrayList<Double>
        ): java.util.ArrayList<Double> {
            val gapless = java.util.ArrayList<Double>(timeseries.size)
            var prevNonNan = fallbackInitialValue
            for (value in timeseries) {
                gapless.add(
                    if (value.isNaN() || value < 0) {
                        prevNonNan
                    } else {
                        prevNonNan = value
                        value
                    }
                )
            }
            return gapless
        }
    }
}