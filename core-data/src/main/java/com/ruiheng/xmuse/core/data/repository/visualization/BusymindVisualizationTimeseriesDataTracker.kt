package com.ruiheng.xmuse.core.data.repository.visualization

import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable

class BusymindVisualizationTimeseriesDataTracker() {

    /**
     * Synchronizes access to [inputBuffer]. Writing to [inputBuffer] and reading from it
     * via [process] can happen on different threads
     */
    private val lock = Object()

    //    private val inputBuffer = ArrayList<Double>()
    private var disposeable: Disposable? = null
    private var lastBuffer: Double = 0.0
    fun startTracking(inputData: Observable<Double>) {
        inputData
            .map {
                if (it.isNaN()) {
                    -1.0
                } else {
                    it
                }
            }
            .subscribe {
                synchronized(lock) {
                    lastBuffer = it
//                    inputBuffer.add(it)
                }
            }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun processValue() = lastBuffer

}