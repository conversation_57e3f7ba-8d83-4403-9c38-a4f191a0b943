// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

public final class GuidanceContent {


    /*package*/ final String courseUniqueId;

    /*package*/ final String collectionUniqueId;

    /*package*/ final String meditationUniqueId;

    /*package*/ final String absoluteDirectoryPath;

    /*package*/ final String audioFilename;

    /*package*/ final String configJson;

    public GuidanceContent(
            String courseUniqueId,
            String collectionUniqueId,
            String meditationUniqueId,
            String absoluteDirectoryPath,
            String audioFilename,
            String configJson) {
        this.courseUniqueId = courseUniqueId;
        this.collectionUniqueId = collectionUniqueId;
        this.meditationUniqueId = meditationUniqueId;
        this.absoluteDirectoryPath = absoluteDirectoryPath;
        this.audioFilename = audioFilename;
        this.configJson = configJson;
    }

    public String getCourseUniqueId() {
        return courseUniqueId;
    }

    public String getCollectionUniqueId() {
        return collectionUniqueId;
    }

    public String getMeditationUniqueId() {
        return meditationUniqueId;
    }

    public String getAbsoluteDirectoryPath() {
        return absoluteDirectoryPath;
    }

    /** if null, then bundled with app */
    public String getAudioFilename() {
        return audioFilename;
    }

    public String getConfigJson() {
        return configJson;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "GuidanceContent{" +
                "courseUniqueId=" + courseUniqueId +
                "," + "collectionUniqueId=" + collectionUniqueId +
                "," + "meditationUniqueId=" + meditationUniqueId +
                "," + "absoluteDirectoryPath=" + absoluteDirectoryPath +
                "," + "audioFilename=" + audioFilename +
                "," + "configJson=" + configJson +
        "}";
    }

}
