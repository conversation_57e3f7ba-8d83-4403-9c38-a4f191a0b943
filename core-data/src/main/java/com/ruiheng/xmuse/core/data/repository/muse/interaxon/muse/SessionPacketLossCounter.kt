package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import javax.inject.Inject

@ActivityRetainedScoped
class SessionPacketLossCounter(
    private val musePackets: Observable<MuseDataPacket>
) {
    @Inject
    constructor(
        museConnector: MuseConnector,
        dataObservableFactory: MuseDataObservableFactory
    ) : this(
        if (museConnector.getCurrentMuse() != null) {
            dataObservableFactory.createDataObservable(
                museConnector.getCurrentMuse()!!,
                listOf(
                    MuseDataPacketType.EEG,
                    MuseDataPacketType.PPG,
                    MuseDataPacketType.ACCELEROMETER,
                    MuseDataPacketType.BATTERY,
                    MuseDataPacketType.DRL_REF,
                    MuseDataPacketType.GYRO,
                    MuseDataPacketType.THERMISTOR,
                ))
        } else {
            Observable.empty<MuseDataPacket>()
        }
    )

    companion object {
        private const val EEG_CH_COUNT = 4
        private const val PPG_INDEX = 1
        private const val PPG_CH_COUNT = 1
    }

    private var lostPacketCount = 0L
    private var packetCount = 0L
    val packetLossPercent: Double
        get() = if (packetCount > 0) {
            lostPacketCount / packetCount.toDouble() * 100.0
        } else {
            0.0
        }

    private var disposable: Disposable? = null

    fun start() {
        end()
        musePackets.subscribe { packet ->
            when (packet.packetType()) {
                MuseDataPacketType.EEG -> {
                    packetCount += EEG_CH_COUNT
                    for (index in 0 until EEG_CH_COUNT) {
                        if (packet.values()[index].isNaN()) {
                            lostPacketCount += 1
                        }
                    }
                }
                MuseDataPacketType.PPG -> {
                    packetCount += PPG_CH_COUNT
                    if (packet.values()[PPG_INDEX].isNaN()) {
                        lostPacketCount += 1
                    }
                }
                else -> {
                    packetCount += packet.values().size
                    lostPacketCount += packet.values().count { it.isNaN() }
                }
            }
        }.let { disposable = it }
    }

    fun end() {
        disposable?.dispose()
        disposable = null
    }
}