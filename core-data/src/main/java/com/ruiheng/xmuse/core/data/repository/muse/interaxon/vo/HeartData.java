// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from session.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;

public final class HeartData {


    /*package*/ final ArrayList<Double> timeseries;

    /*package*/ final int lowHrSeconds;

    /*package*/ final int highHrSeconds;

    /*package*/ final int lowHrPercentage;

    public HeartData(
            ArrayList<Double> timeseries,
            int lowHrSeconds,
            int highHrSeconds,
            int lowHrPercentage) {
        this.timeseries = timeseries;
        this.lowHrSeconds = lowHrSeconds;
        this.highHrSeconds = highHrSeconds;
        this.lowHrPercentage = lowHrPercentage;
    }

    public ArrayList<Double> getTimeseries() {
        return timeseries;
    }

    public int getLowHrSeconds() {
        return lowHrSeconds;
    }

    public int getHighHrSeconds() {
        return highHrSeconds;
    }

    public int getLowHrPercentage() {
        return lowHrPercentage;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "HeartData{" +
                "timeseries=" + timeseries +
                "," + "lowHrSeconds=" + lowHrSeconds +
                "," + "highHrSeconds=" + highHrSeconds +
                "," + "lowHrPercentage=" + lowHrPercentage +
        "}";
    }

}
