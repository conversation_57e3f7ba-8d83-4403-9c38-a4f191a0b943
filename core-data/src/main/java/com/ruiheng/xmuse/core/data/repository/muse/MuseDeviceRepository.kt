package com.ruiheng.xmuse.core.data.repository.muse

import android.content.res.AssetManager
import android.health.connect.datatypes.SleepSessionRecord
import android.os.Environment
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.Accelerometer
import com.choosemuse.libmuse.Battery
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.DrlRef
import com.choosemuse.libmuse.Eeg
import com.choosemuse.libmuse.Gyro
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseArtifactPacket
import com.choosemuse.libmuse.MuseConnectionListener
import com.choosemuse.libmuse.MuseConnectionPacket
import com.choosemuse.libmuse.MuseDataListener
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.MuseListener
import com.choosemuse.libmuse.MuseManager
import com.choosemuse.libmuse.MuseManagerAndroid
import com.choosemuse.libmuse.MuseModel
import com.choosemuse.libmuse.MusePreset
import com.choosemuse.libmuse.Optics
import com.choosemuse.libmuse.Ppg
import com.choosemuse.libmuse.internal.Busymind
import com.choosemuse.libmuse.internal.BusymindEventListener
import com.choosemuse.libmuse.internal.BusymindListener
import com.choosemuse.libmuse.internal.BusymindMode
import com.choosemuse.libmuse.internal.BusymindPacket
import com.choosemuse.libmuse.internal.BusymindVersion
import com.choosemuse.libmuse.internal.Classifier
import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.MuseSessionType
import com.choosemuse.libmuse.internal.RealtimeModel
import com.choosemuse.libmuse.internal.SessionArgs
import com.choosemuse.libmuse.internal.TfliteModelConsts
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.AppExecutors
import com.ruiheng.xmuse.core.data.postLiveDataChange
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.FmodMaps
import com.ruiheng.xmuse.core.model.data.Xmuse
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber
import java.io.File
import java.nio.file.Files
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MuseDeviceRepository @Inject constructor(
    private val appExecutors: AppExecutors,
    private val museDataObservableFactory: MuseDataObservableFactory,
    private val museDataCacheRepository: MuseDataCacheRepository
) : MuseDeviceRepositoryImp {

    companion object {
        val sleepStageUpdateSeconds = 5L
        val RAW_INPUT_TYPES = listOf(
            MuseDataPacketType.EEG,
            MuseDataPacketType.ACCELEROMETER,
            MuseDataPacketType.DRL_REF,
            MuseDataPacketType.QUANTIZATION,
            MuseDataPacketType.PPG,
            MuseDataPacketType.OPTICS
        )
    }

    fun initMuseRealtimeModel() = flow<Result<Boolean>> {

        emit(Result.Loading())
        val folder = Utils.getApp().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)
        if (folder == null) {
            emit(Result.Error(PetivityThrowable()))
            return@flow
        }
        val assets: AssetManager = Utils.getApp().assets
        val files = assets.list("")
        checkNotNull(files)
        for (file in files) {
            val outFile: File = File(folder.absoluteFile, file)
            if (!outFile.exists() && file.endsWith(".tflite")) {
                val inputStream = assets.open(file)
                val outputStream = Files.newOutputStream(outFile.toPath())
                val buf = ByteArray(256)
                var len: Int
                while ((inputStream.read(buf).also { len = it }) > 0) {
                    outputStream.write(buf, 0, len)
                }
                inputStream.close()
                outputStream.close()
            }
        }
        emit(Result.Success(true))
    }.flowOn(Dispatchers.IO)

    fun isMuseWithPpg() = _connectMuseLiveData.value?.data?.model == MuseModel.MU_05

    fun selectedMuseModel() = _connectMuseLiveData.value?.data?.model

    override val _scanXmuseResources = MutableLiveData<Result<MutableList<Muse>>>(Result.Loading())
    override val _connectMuseLiveData = MutableLiveData<Result<Muse?>>(Result.Loading(null))

    fun obseverXmuseInfoLiveData() = _connectXmuseInfoLiveData as LiveData<Result<Xmuse?>>

    private val _connectXmuseInfoLiveData =
        MutableLiveData<Result<Xmuse?>>(Result.Loading(null))

    private val museAndroidManager by lazy {
        MuseManagerAndroid.getInstance()
    }

    private val museDeviceListener = object : MuseListener() {
        override fun museListChanged() {
            val museList = museAndroidManager.muses
            postLiveDataChange(appExecutors, _scanXmuseResources, Result.Loading(museList))
        }
    }

    init {
        museAndroidManager.setContext(Utils.getApp())
        museAndroidManager.setMuseListener(museDeviceListener)
    }

    fun getCurrentMuse() = _connectMuseLiveData.value?.data

    fun startMuseDeviceScan() {
        museAndroidManager.stopListening()
        museAndroidManager.startListening()
    }

    fun isBluetoothEnable() = museAndroidManager.defaultAdapter?.isEnabled == true

    fun stopMuseDeviceScan() {
        museAndroidManager.stopListening()
    }

    fun startMuseFileCache() =
        museDataCacheRepository.startMuseFileCache("other", UUID.randomUUID().toString())

    fun saveMuseFileCache() = museDataCacheRepository.saveFileCache()

    fun disconnectMuseDevice() {
        _connectMuseLiveData.value?.data?.disconnect()
    }

    fun startConnectDevice(xmuse: Muse): LiveData<Result<Muse?>> {
        val currentConnectedMuse = _connectMuseLiveData.value?.data
        currentConnectedMuse?.unregisterAllListeners()
        currentConnectedMuse?.unregisterConnectionListener(xmuseConnectListener)
        currentConnectedMuse?.disconnect()
        _connectMuseLiveData.value = Result.Loading(xmuse)
        xmuse.unregisterAllListeners()
        xmuse.registerConnectionListener(xmuseConnectListener)


        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.EEG)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.ACCELEROMETER)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.DRL_REF)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.QUANTIZATION)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.PPG)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.OPTICS)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.GYRO)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.BATTERY)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.HSI_PRECISION)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.IS_PPG_GOOD)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.THERMISTOR)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.IS_GOOD)
//
//        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.AVG_BODY_TEMPERATURE)
//
////
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.ALPHA_ABSOLUTE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.BETA_ABSOLUTE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.DELTA_ABSOLUTE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.GAMMA_ABSOLUTE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.THETA_ABSOLUTE)
//
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.BETA_RELATIVE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.THETA_RELATIVE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.ALPHA_RELATIVE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.GAMMA_RELATIVE)
        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.DELTA_RELATIVE)

        //TODO
        if (xmuse.model == MuseModel.MS_03) {
            xmuse.setPreset(MusePreset.PRESET_1034)
        } else {
            xmuse.setPreset(MusePreset.PRESET_51)
        }
        xmuse.runAsynchronously()

        return _connectMuseLiveData
    }

    private val xmuseConnectListener = object : MuseConnectionListener() {
        override fun receiveMuseConnectionPacket(packet: MuseConnectionPacket?, muse: Muse?) {
            museConnectionState.onNext(packet?.currentConnectionState)
            val xmuseDeviceList = museAndroidManager.muses ?: mutableListOf<Muse>()
            _scanXmuseResources.postValue(Result.Loading(xmuseDeviceList))
            museDeviceListener.museListChanged()
            if (packet?.isOffline() == true) {
                _connectMuseLiveData.postValue(Result.Error(Exception(), muse))
                _connectXmuseInfoLiveData.postValue(Result.Error(Exception(), null))
                stopBusymindSession()
            }

            if (packet?.isLoading() == true) {
                _connectMuseLiveData.postValue(Result.Loading(muse))
                _connectXmuseInfoLiveData.postValue(Result.Loading(Xmuse()))
            }

            if (packet?.isOnline() == true) {
                startBusymindSession(muse)
                _connectMuseLiveData.postValue(Result.Success(muse))
                val xmuse = Xmuse(
                    muse?.name,
                    muse?.macAddress,
                    muse?.rssi,
                    null,
                    muse?.museVersion?.firmwareVersion,
                    muse?.museConfiguration?.serialNumber
                )
                _connectXmuseInfoLiveData.postValue(Result.Success(xmuse))
            }
        }
    }

    private val busymindModeListener = object : BusymindEventListener(){
        override fun busymindStateDidChange(mode: BusymindMode?, muse: Muse?) {
        }

    }

    private val busymindListener = object : BusymindListener() {
        override fun receiveBusymindPacket(packet: BusymindPacket?, muse: Muse?) {
            packet ?: return
            if (packet.isElapsedTimeUpdated) {

            } else if (!packet.isFmodUpdated) {
                mutableNfbPackets.onNext(packet)
            }
            if (packet.isFmodUpdated) {
            }

            if (packet.isProcOptics) {
            }

            if (packet.isHeartUpdated) {
            }
            if (packet.isBreathScoreUpdated) {
            }
            if (packet.isBodyScoreUpdated) {
            }
            if (packet.isBodyUpdated) {
            }
        }
    }

    private var busymind: Busymind? = null
    private fun startBusymindSession(muse: Muse?) {
        if (busymind != null) {
            stopBusymindSession()
        }
        if (muse == null) return
        val fmodMap = ArrayList(mutableListOf(1000, 1001, 1002, 1003, 1004))
        val fmodParams = ArrayList(mutableListOf(1000, 1001, 1002, 1003, 1004))
        val folder = Utils.getApp().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS) ?: return
        val model = RealtimeModel(
            "tf_sleep_stage_classifier",
            (TfliteModelConsts.EEG_SAMPLING_RATE).toInt() * sleepStageUpdateSeconds.toInt(),  // update every 30 seconds
            TfliteModelConsts.EEG_LABEL,
            fmodParams
        )

        val models = ArrayList<RealtimeModel>()
        models.add(model)
        val queue = MainOperationQueue()
//
        busymind = Busymind.createBusymindWithTflite(
            muse,
            fmodMap,
            models,
            folder.absolutePath,
            queue,
            MuseSessionType.GUIDED,
            BusymindVersion.V8_1
        )

//        busymind = Busymind.createBusymind(BusymindVersion.V8_1)
//        busymind?.replaceHeadband(muse)
//        val sessionArgs = SessionArgs(
//            MuseSessionType.MIND, "", "",
//            300L, 0.0, "", false,
//            FmodMaps.getFmodParameterMap(""), hashMapOf()
//        )
//        busymind?.setSessionArgs(sessionArgs)

        busymind?.registerListener(busymindListener)
        busymind?.setBusymindMode(BusymindMode.FEEDBACK)
        busymind?.registerEventListener(busymindModeListener)
    }

    private fun stopBusymindSession() {
        busymind?.unregisterTfliteProcessors()
        busymind = null
    }

    private val xmuseDataListener = object : MuseDataListener() {
        override fun receiveMuseArtifactPacket(packet: MuseArtifactPacket?, muse: Muse?) {
            val jawClench = packet?.jawClench ?: false
            val blink = packet?.blink ?: false
            postLiveDataChange(appExecutors, _xmuseBlinkLiveData, blink)
            postLiveDataChange(appExecutors, _xmuseJawClenchLiveData, jawClench)
//            if (showRawLog) {
//                Timber.d("receiveMuseArtifactPacket-JawClench::${packet?.jawClench}---Blink:${packet?.blink}")
//            }
        }

        override fun receiveMuseDataPacket(packet: MuseDataPacket?, muse: Muse?) {
            museDataCacheRepository.onMuseDataPacketReceive(packet)
            appExecutors.diskIO().execute {
                when (packet?.packetType()) {

                    MuseDataPacketType.OPTICS -> {
                    }

                    MuseDataPacketType.EEG -> {
                        mutableEegPackets.onNext(packet)
//                        val eeg = arrayOf(
//                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
//                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
//                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
//                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
//                        )
//                        postLiveDataChange(appExecutors, _xmuseEEGLiveData, eeg)
                    }

                    MuseDataPacketType.ALPHA_ABSOLUTE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
                        )
                        postLiveDataChange(appExecutors, _xmuseAlphaAbsoluteLiveData, eeg)
                    }

                    MuseDataPacketType.THETA_ABSOLUTE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
                        )
                        postLiveDataChange(appExecutors, _xmuseThetaAbsoluteLiveData, eeg)
                    }

                    MuseDataPacketType.GAMMA_ABSOLUTE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
                        )
                        postLiveDataChange(appExecutors, _xmuseGammaAbsoluteLiveData, eeg)
                    }

                    MuseDataPacketType.DELTA_ABSOLUTE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
                        )
                        postLiveDataChange(appExecutors, _xmuseDeltaAbsoluteLiveData, eeg)
                    }

                    MuseDataPacketType.BETA_ABSOLUTE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG2).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG3).toFloat(),
                            packet.getEegChannelValue(Eeg.EEG4).toFloat()
                        )
                        postLiveDataChange(appExecutors, _xmuseBetaAbsoluteLiveData, eeg)
                    }

                    MuseDataPacketType.ALPHA_RELATIVE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1),
                            packet.getEegChannelValue(Eeg.EEG2),
                            packet.getEegChannelValue(Eeg.EEG3),
                            packet.getEegChannelValue(Eeg.EEG4)
                        )
                        postLiveDataChange(appExecutors, _xmuseAlphaRelativeLiveData, eeg)
                    }

                    MuseDataPacketType.BETA_RELATIVE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1),
                            packet.getEegChannelValue(Eeg.EEG2),
                            packet.getEegChannelValue(Eeg.EEG3),
                            packet.getEegChannelValue(Eeg.EEG4)
                        )
                        postLiveDataChange(appExecutors, _xmuseBetaRelativeLiveData, eeg)
                    }

                    MuseDataPacketType.THETA_RELATIVE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1),
                            packet.getEegChannelValue(Eeg.EEG2),
                            packet.getEegChannelValue(Eeg.EEG3),
                            packet.getEegChannelValue(Eeg.EEG4)
                        )
                        postLiveDataChange(appExecutors, _xmuseThetaRelativeLiveData, eeg)
                    }

                    MuseDataPacketType.GAMMA_RELATIVE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1),
                            packet.getEegChannelValue(Eeg.EEG2),
                            packet.getEegChannelValue(Eeg.EEG3),
                            packet.getEegChannelValue(Eeg.EEG4)
                        )
                        postLiveDataChange(appExecutors, _xmuseGammaRelativeLiveData, eeg)
                    }

                    MuseDataPacketType.DELTA_RELATIVE -> {
                        val eeg = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1),
                            packet.getEegChannelValue(Eeg.EEG2),
                            packet.getEegChannelValue(Eeg.EEG3),
                            packet.getEegChannelValue(Eeg.EEG4)
                        )
                        postLiveDataChange(appExecutors, _xmuseDeltaRelativeLiveData, eeg)
                    }

                    MuseDataPacketType.IS_PPG_GOOD -> {
                        val isPPGGood = packet.values()[0].toInt()
                        _xmusePPGIsGood.postValue(isPPGGood == 1)
                    }

                    MuseDataPacketType.PPG -> {
                        val irValue = packet.getPpgChannelValue(Ppg.IR).toFloat()
                        val redValue = packet.getPpgChannelValue(Ppg.RED)

                        _xmuseIRLiveData.postValue(irValue)
                    }

                    MuseDataPacketType.DRL_REF -> {
                        val drl = packet.getDrlRefValue(DrlRef.DRL).toFloat()
                        val ref = packet.getDrlRefValue(DrlRef.REF).toFloat()
                        _xmuseDrlRefLiveData.postValue(arrayOf(drl, ref))

                    }

                    MuseDataPacketType.BATTERY -> {
                        val battery =
                            packet.getBatteryValue(Battery.CHARGE_PERCENTAGE_REMAINING).toInt()
                        val xmuse = Xmuse(
                            muse?.name,
                            muse?.macAddress,
                            muse?.rssi,
                            battery,
                            muse?.museVersion?.firmwareVersion,
                            muse?.museConfiguration?.serialNumber
                        )
                        postLiveDataChange(
                            appExecutors,
                            _connectXmuseInfoLiveData,
                            Result.Success(xmuse)
                        )
                    }

                    MuseDataPacketType.HSI_PRECISION -> {
                        val eegHisBuffer = arrayOf(
                            packet.getEegChannelValue(Eeg.EEG1).toInt(),
                            packet.getEegChannelValue(Eeg.EEG2).toInt(),
                            packet.getEegChannelValue(Eeg.EEG3).toInt(),
                            packet.getEegChannelValue(Eeg.EEG4).toInt()
                        )
                        _xmuseHsiPLiveData.postValue(eegHisBuffer)
                    }

                    MuseDataPacketType.QUANTIZATION -> {

//                        val eegHisBuffer = arrayOf(
//                            packet.getEegChannelValue(Eeg.EEG1),
//                            packet.getEegChannelValue(Eeg.EEG2),
//                            packet.getEegChannelValue(Eeg.EEG3),
//                            packet.getEegChannelValue(Eeg.EEG4)
//                        )
//                        Timber.d("QUANTIZATION:${eegHisBuffer[0]}:${eegHisBuffer[1]}:${eegHisBuffer[2]}:${eegHisBuffer[3]}")
                    }

//                    MuseDataPacketType.ALPHA_RELATIVE -> {
//                        val eegHisBuffer = arrayOf(
//                            packet.getEegChannelValue(Eeg.EEG1),
//                            packet.getEegChannelValue(Eeg.EEG2),
//                            packet.getEegChannelValue(Eeg.EEG3),
//                            packet.getEegChannelValue(Eeg.EEG4)
//                        )
//                        Timber.d("ALPHA_RELATIVE:${eegHisBuffer[0]}:${eegHisBuffer[1]}:${eegHisBuffer[2]}:${eegHisBuffer[3]}")
//                    }

                    MuseDataPacketType.ALPHA_SCORE -> {
//                        val average = calculateScorleData(packet) ?: return@execute
//                        _xmuseAlphaScoreLiveData.postValue(average)
                    }

                    MuseDataPacketType.BETA_SCORE -> {
//                        val average = calculateScorleData(packet) ?: return@execute
//                        _xmuseBetaScoreLiveData.postValue(average)
                    }

                    MuseDataPacketType.DELTA_SCORE -> {
//                        val average = calculateScorleData(packet) ?: return@execute
//                        _xmuseDeltaScoreLiveData.postValue(average)
                    }

                    MuseDataPacketType.THETA_SCORE -> {
//                        val average = calculateScorleData(packet) ?: return@execute
//                        _xmuseThetaScoreLiveData.postValue(average)
                    }

                    MuseDataPacketType.GAMMA_SCORE -> {
//                        val average = calculateScorleData(packet) ?: return@execute
//                        _xmuseGammaScoreLiveData.postValue(average)
                    }

                    MuseDataPacketType.GYRO -> {
                        val x = packet.getGyroValue(Gyro.X)
                        val y = packet.getGyroValue(Gyro.Y)
                        val z = packet.getGyroValue(Gyro.Z)
                        val gyroValue = arrayOf(x, y, z)
                        postLiveDataChange(appExecutors, _xmuseGyroLiveData, gyroValue)
                    }

                    MuseDataPacketType.ACCELEROMETER -> {
                        mutableAccPackets.onNext(packet)
//                        val x = packet.getAccelerometerValue(Accelerometer.X)
//                        val y = packet.getAccelerometerValue(Accelerometer.Y)
//                        val z = packet.getAccelerometerValue(Accelerometer.Z)
//                        val accValue = arrayOf(x, y, z)
//                        postLiveDataChange(appExecutors, _xmuseAccLiveData, accValue)
                    }

                    else -> {}
                }
            }
        }

    }

    override val _xmuseEEGLiveData = MutableLiveData<Array<Float>?>()
    override val _xmuseHsiPLiveData = MutableLiveData(arrayOf(-1, -1, -1, -1))

    override val _xmuseAlphaAbsoluteLiveData = MutableLiveData<Array<Float>?>(null)
    override val _xmuseBetaAbsoluteLiveData = MutableLiveData<Array<Float>?>(null)
    override val _xmuseDeltaAbsoluteLiveData = MutableLiveData<Array<Float>?>(null)
    override val _xmuseThetaAbsoluteLiveData = MutableLiveData<Array<Float>?>(null)
    override val _xmuseGammaAbsoluteLiveData = MutableLiveData<Array<Float>?>(null)

    override val _xmuseAlphaScoreLiveData = MutableLiveData(0f)
    override val _xmuseBetaScoreLiveData = MutableLiveData(0f)
    override val _xmuseDeltaScoreLiveData = MutableLiveData(0f)
    override val _xmuseThetaScoreLiveData = MutableLiveData(0f)
    override val _xmuseGammaScoreLiveData = MutableLiveData(0f)

    override val _xmuseAlphaRelativeLiveData = MutableLiveData<Array<Double>?>(null)
    override val _xmuseBetaRelativeLiveData = MutableLiveData<Array<Double>?>(null)
    override val _xmuseDeltaRelativeLiveData = MutableLiveData<Array<Double>?>(null)
    override val _xmuseThetaRelativeLiveData = MutableLiveData<Array<Double>?>(null)
    override val _xmuseGammaRelativeLiveData = MutableLiveData<Array<Double>?>(null)

    override val _xmuseDrlRefLiveData = MutableLiveData<Array<Float>?>(null)
    override val _xmuseIRLiveData = MutableLiveData(0f)
    override val _xmuseJawClenchLiveData = MutableLiveData(false)
    override val _xmuseBlinkLiveData = MutableLiveData<Boolean>(false)
    override val _xmusePPGIsGood = MutableLiveData(false)

    override val _xmuseGyroLiveData = MutableLiveData(arrayOf(0.0, 0.0, 0.0))
    override val _xmuseAccLiveData = MutableLiveData(arrayOf(0.0, 0.0, 0.0))


    private val mutableNfbPackets = BehaviorSubject.create<BusymindPacket?>()

    override val nfbPackets: Observable<BusymindPacket>
        get() = mutableNfbPackets.observeOn(AndroidSchedulers.mainThread())

    override val eegPackets: Observable<MuseDataPacket>
        get() = mutableEegPackets.observeOn(AndroidSchedulers.mainThread())

    override val accPackets: Observable<MuseDataPacket>
        get() = mutableAccPackets.observeOn(AndroidSchedulers.mainThread())

    private val mutableEegPackets = BehaviorSubject.create<MuseDataPacket?>()
    private val mutableAccPackets = BehaviorSubject.create<MuseDataPacket?>()


    override val sleepStageClassifier = BehaviorSubject.createDefault(listOf(0f, 0f, 0f, 0f, 0f))
    override val museConnectionState =
        BehaviorSubject.createDefault<ConnectionState>(ConnectionState.UNKNOWN)
}