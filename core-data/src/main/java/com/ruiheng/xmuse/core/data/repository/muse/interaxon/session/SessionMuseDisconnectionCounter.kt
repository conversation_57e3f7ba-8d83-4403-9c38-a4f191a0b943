package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import com.choosemuse.libmuse.ConnectionState
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.disposables.CompositeDisposable
import javax.inject.Inject

@ActivityRetainedScoped
class SessionMuseDisconnectionCounter
@Inject constructor(
    private val museConnector: MuseConnector
) {

    private var disconnectCount = 0

    private val disposables = CompositeDisposable()

    fun startCounting() {
        museConnector.museConnectionState
            .buffer(2, 1)
            .subscribe { states ->
                if (states.size >= 2 && states[0] == ConnectionState.CONNECTED &&
                    states[1] == ConnectionState.DISCONNECTED
                ) {
                    disconnectCount++
                }
            }.let { disposables.add(it) }
    }

    fun stopCounting(): Int {
        disposables.clear()
        return disconnectCount
    }
}