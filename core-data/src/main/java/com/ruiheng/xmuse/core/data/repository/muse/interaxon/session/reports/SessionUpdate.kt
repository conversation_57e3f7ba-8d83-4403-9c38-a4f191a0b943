package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepPositionsUserSession
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepStagesUserSession
import java.util.UUID

data class SessionUpdate(
    val id: String = UUID.randomUUID().toString(),
    var timeSeries: UserSessionTimeSeries? = null,
    var sleepPositions: SleepPositionsUserSession? = null,
    var sleepStages: SleepStagesUserSession? = null,
    var presleep: PresleepUserSession? = null
) {
}

data class PresleepUserSession(var sleepScore: Int? = null)

data class UserSessionTimeSeries(
    var sleepPositions: Array<Float>? = null,
    var sleepStages: Array<Float>? = null,
    var mindCalmPerSecond: Array<Float>? = null,
    var bodyMovementPerSecond: Array<Float>? = null,
    var bodyMovementDownSampled:Array<Float>? = null,
    var heartRatePerSecond: Array<Float>? = null,
    var heartRateDownSampled:Array<Float>? = null,
    var breathSyncPerSecond: Array<Float>? = null,
    var deepSleepIntensity: Array<Float>? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as UserSessionTimeSeries

        if (sleepPositions != null) {
            if (other.sleepPositions == null) return false
            if (!sleepPositions.contentEquals(other.sleepPositions)) return false
        } else if (other.sleepPositions != null) return false
        if (sleepStages != null) {
            if (other.sleepStages == null) return false
            if (!sleepStages.contentEquals(other.sleepStages)) return false
        } else if (other.sleepStages != null) return false
        if (mindCalmPerSecond != null) {
            if (other.mindCalmPerSecond == null) return false
            if (!mindCalmPerSecond.contentEquals(other.mindCalmPerSecond)) return false
        } else if (other.mindCalmPerSecond != null) return false
        if (bodyMovementPerSecond != null) {
            if (other.bodyMovementPerSecond == null) return false
            if (!bodyMovementPerSecond.contentEquals(other.bodyMovementPerSecond)) return false
        } else if (other.bodyMovementPerSecond != null) return false
        if (heartRatePerSecond != null) {

            if (other.heartRatePerSecond == null) return false
            if (!heartRatePerSecond.contentEquals(other.heartRatePerSecond)) return false
        } else if (other.heartRatePerSecond != null) return false
        if (breathSyncPerSecond != null) {
            if (other.breathSyncPerSecond == null) return false
            if (!breathSyncPerSecond.contentEquals(other.breathSyncPerSecond)) return false
        } else if (other.breathSyncPerSecond != null) return false
        if (deepSleepIntensity != null) {
            if (other.deepSleepIntensity == null) return false
            if (!deepSleepIntensity.contentEquals(other.deepSleepIntensity)) return false
        } else if (other.deepSleepIntensity != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = sleepPositions?.contentHashCode() ?: 0
        result = 31 * result + (sleepStages?.contentHashCode() ?: 0)
        result = 31 * result + (mindCalmPerSecond?.contentHashCode() ?: 0)
        result = 31 * result + (bodyMovementPerSecond?.contentHashCode() ?: 0)
        result = 31 * result + (heartRatePerSecond?.contentHashCode() ?: 0)
        result = 31 * result + (breathSyncPerSecond?.contentHashCode() ?: 0)
        result = 31 * result + (deepSleepIntensity?.contentHashCode() ?: 0)
        return result
    }

}