package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.blankj.utilcode.util.BusUtils.Bus
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseConnectionListener
import com.choosemuse.libmuse.MuseConnectionPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.internal.Busymind
import com.choosemuse.libmuse.internal.BusymindEventListener
import com.choosemuse.libmuse.internal.BusymindListener
import com.choosemuse.libmuse.internal.BusymindMode
import com.choosemuse.libmuse.internal.BusymindPacket
import com.choosemuse.libmuse.internal.BusymindParameters
import com.choosemuse.libmuse.internal.BusymindVersion
import com.choosemuse.libmuse.internal.MuseSessionType
import com.choosemuse.libmuse.internal.SessionArgs
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.reactivex.rxjava3.subjects.BehaviorSubject
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BusymindMonitor(
    private val museDataObservableFactory: MuseDataObservableFactory,
    private val scheduler: Scheduler
) {
    private var busymind: Busymind? = null

    @Inject
    constructor(museDataObservableFactory: MuseDataObservableFactory) : this(
        museDataObservableFactory, Schedulers.computation()
    )

    companion object {
        val RAW_INPUT_TYPES = listOf(
            MuseDataPacketType.EEG,
            MuseDataPacketType.ACCELEROMETER,
            MuseDataPacketType.DRL_REF,
            MuseDataPacketType.QUANTIZATION,
            MuseDataPacketType.PPG,
            MuseDataPacketType.OPTICS,
            MuseDataPacketType.ALPHA_SCORE,
            MuseDataPacketType.BETA_SCORE,
            MuseDataPacketType.THETA_SCORE,
            MuseDataPacketType.DELTA_SCORE,
            MuseDataPacketType.GAMMA_SCORE,
            MuseDataPacketType.ALPHA_RELATIVE,
            MuseDataPacketType.BETA_RELATIVE,
            MuseDataPacketType.THETA_RELATIVE,
            MuseDataPacketType.DELTA_RELATIVE,
            MuseDataPacketType.GAMMA_RELATIVE,
            MuseDataPacketType.ALPHA_ABSOLUTE,
            MuseDataPacketType.BETA_ABSOLUTE,
            MuseDataPacketType.THETA_ABSOLUTE,
            MuseDataPacketType.DELTA_ABSOLUTE,
            MuseDataPacketType.GAMMA_ABSOLUTE,
        )
    }

    var busymindVersion: BusymindVersion?
        set(value) {
            disposable.clear()
            busymind?.replaceAlgorithm(value)
            muse?.let { startMonitoring(it) }
        }
        get() = busymind?.version

    val nfbPackets: Observable<BusymindPacket>
        get() = mutableNfbPackets.observeOn(AndroidSchedulers.mainThread())
    private val mutableNfbPackets = BehaviorSubject.create<BusymindPacket>()

    val elapsedTimePackets: Observable<BusymindPacket>
        get() = mutableElapsedTimePackets.observeOn(AndroidSchedulers.mainThread())
    private val mutableElapsedTimePackets = BehaviorSubject.create<BusymindPacket>()

    val busymindModeObservable: Observable<BusymindMode>
        get() = mutableBusymindMode.observeOn(AndroidSchedulers.mainThread())
    private val mutableBusymindMode = BehaviorSubject.createDefault(BusymindMode.IDLE)


    var busymindMode: BusymindMode?
        get() = mutableBusymindMode.value
        set(value) = busymind?.setBusymindMode(value) ?: Unit

    val parameters: BusymindParameters?
        get() = busymind?.parameters

    private val packetListener = object : BusymindListener() {
        override fun receiveBusymindPacket(packet: BusymindPacket?, muse: Muse?) {
            packet ?: return
            if (packet.isElapsedTimeUpdated) {
                mutableElapsedTimePackets.onNext(packet)
            } else if (!packet.isFmodUpdated) {
                if (packet.isProcEeg) {
                    if (packet.busymind != -1.0 && mutableBusymindMode.value != BusymindMode.FEEDBACK) {
                        mutableBusymindMode.onNext(BusymindMode.FEEDBACK)
                    }
                }
                mutableNfbPackets.onNext(packet)
            }
        }
    }
    private val eventListener = object : BusymindEventListener() {
        override fun busymindStateDidChange(mode: BusymindMode?, muse: Muse?) {
            mode ?: return
            mutableBusymindMode.onNext(mode)
        }
    }

    private var disposable = CompositeDisposable()
    var muse: Muse? = null
//
//    init {
//        busymind.registerListener(packetListener)
//        busymind.registerEventListener(eventListener)
//        busymind.setSessionArgs(
//            SessionArgs(
//                MuseSessionType.GUIDED, "", "",
//                500L, 500.0,
//                "", true,
//                arrayListOf(), hashMapOf()
//            )
//        )
//        busymindMode = BusymindMode.FEEDBACK
//    }

    fun setSessionArgs(sessionArgs: SessionArgs) {
//        busymind.setSessionArgs(sessionArgs)
//        busymind.volume = VolumeArgs(
//            /* guidanceVolume = */ 1.0,
//            /* feedbackVolume = */ 1.0,
//            /* birdsVolume = */ 1.0,
//            /* backgroundVolume = */ 1.0,
//            /* alertsVolume = */ 1.0,
//            /* volumeControlOn = */ false,
//            /* guidanceSoundscapeCrossfade = */ 0.5
//        )
    }

    private val connectionListener = object : MuseConnectionListener() {
        override fun receiveMuseConnectionPacket(packet: MuseConnectionPacket?, muse: Muse?) {
            if (packet?.currentConnectionState == ConnectionState.CONNECTED && muse != null) {
                updatePresetFromMuse(muse)
                startMonitoring(muse)
            }
        }
    }

    private fun updatePresetFromMuse(muse: Muse) {
        val connectionState = muse.connectionState
        if (connectionState == ConnectionState.CONNECTED) {
            val preset = muse.museConfiguration?.preset
            if (preset != null) {
                busymind?.setMusePreset(preset)
            }
        }
    }

    fun startMonitoring(muse: Muse) {
        if (muse == this.muse) {
            return
        }

        this.muse?.unregisterConnectionListener(connectionListener)

        busymind = Busymind.createBusymind(BusymindVersion.V8_2)
        busymind?.registerEventListener(eventListener)
        val connectionState = muse.connectionState
        if (connectionState == ConnectionState.CONNECTED) {
            busymind?.replaceHeadband(muse)
//            val serialNumber = muse.museConfiguration.serialNumber ?: return
//            val museModel = muse.model ?: return
//            val museModel = MuseModel.fromMuseSerialNumber(serialNumber) ?: return
//            busymind.setMuseModelInternal(museModel.museModelInternal)
            busymind?.setBusymindMode(BusymindMode.FEEDBACK)
            busymind?.registerListener(packetListener)
            setupObservablesForConnectedMuse(muse)
            this.muse = muse
        }
        muse.registerConnectionListener(connectionListener)
    }

    private fun setupObservablesForConnectedMuse(muse: Muse) {
        disposable.clear()
        disposable = CompositeDisposable()

        val museDataObservables = RAW_INPUT_TYPES.associateWith { packetType ->
            museDataObservableFactory.createDataObservable(muse, packetType).map { it.values() }
        }
//        museDataObservables.forEach { (packetType, observable) ->
//            observable
//                .observeOn(scheduler)
//                .subscribe { data ->
////                    busymind.processData(data, packetType)
//                }.let { disposable.add(it) }
//        }
    }

    fun stopMonitoring() {
        disposable.clear()
        muse?.unregisterConnectionListener(connectionListener)
        muse = null
        busymind?.unregisterTfliteProcessors()
        busymind?.unregisterListener(packetListener)
        busymind?.unregisterEventListener(eventListener)
        busymind = null
    }

    fun resetCalibration() {
        if (this.muse == null) return
        busymind?.unregisterTfliteProcessors()
        busymind?.unregisterListener(packetListener)
        busymind?.unregisterEventListener(eventListener)
        busymind = null

        busymind = Busymind.createBusymind(BusymindVersion.V8_2)
        busymind?.registerEventListener(eventListener)
        val connectionState = muse!!.connectionState
        if (connectionState == ConnectionState.CONNECTED) {
            busymind?.replaceHeadband(muse)
            busymind?.setBusymindMode(BusymindMode.FEEDBACK)
            busymind?.registerListener(packetListener)
        }
        muse?.registerConnectionListener(connectionListener)
        mutableBusymindMode.onNext(BusymindMode.CALIBRATION)
    }
}