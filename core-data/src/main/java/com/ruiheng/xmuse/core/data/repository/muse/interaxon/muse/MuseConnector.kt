package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseArtifactPacket
import com.choosemuse.libmuse.MuseDataListener
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.MuseListener
import com.choosemuse.libmuse.MuseManagerAndroid
import com.choosemuse.libmuse.MuseModel
import com.choosemuse.libmuse.MusePreset
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BluetoothState
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * CQQ
 * modify by CQQ, need to confirm
 * TODO 增加自动连接功能
 */
@Singleton
class MuseConnector
@Inject constructor(
    private val museDataObservableFactory: MuseDataObservableFactory
) {

    companion object {
        private const val CONNECTION_ATTEMPT_SECS = 15L
    }

    private var savedMuseMacAddress: String? = null

    private val museAndroidManager by lazy {
        MuseManagerAndroid.getInstance()
    }
    val muses: Observable<Result<List<Muse>>>
        get() = mutableMuses
    private val mutableMuses = BehaviorSubject.createDefault<Result<List<Muse>>>(Result.Loading())

    private val museDiscoverListener = object : MuseListener() {
        override fun museListChanged() {
            updateMuses()
        }
    }

    val muse: Observable<Muse>
        get() = mutableMuse
    private val mutableMuse = BehaviorSubject.create<Muse>()

    private val selectedMuseListener = object : MuseSelectedListener() {
        override fun onMuseSelected(muse: Muse?) {
//            muse ?: return
            mutableMuse.onNext(muse)
            savedMuseMacAddress = muse?.macAddress
            if (muse != null) {
                museDataObservableFactory.createConnectionStateObservable(muse)
                    .subscribe {
                        mutableMuseConnectionState.onNext(it)
                        museDiscoverListener.museListChanged()
                    }.let { selectedMuseBluetooth = it }
            }
        }
    }

    init {
        museAndroidManager.setContext(Utils.getApp())
        museAndroidManager.setMuseListener(museDiscoverListener)
    }


    val bluetoothState: Observable<BluetoothState>
        get() = mutableBluetoothState
    private val mutableBluetoothState = BehaviorSubject.create<BluetoothState>()

    val museConnectionState: Observable<ConnectionState>
        get() = mutableMuseConnectionState
    private val mutableMuseConnectionState = BehaviorSubject.createDefault(ConnectionState.UNKNOWN)


    private var discoveryDisposable: Disposable? = null
    private var selectedMuseBluetooth: Disposable? = null

//    fun setMuseSelector(museSelector: MuseDeviceSelector) {
//        this.museSelector = museSelector
//        museSelector.registerMuseSelectedListener(selectedMuseListener)
//        museSelector.registerDiscovererListener(museDiscovererListener)
//        updateMuses()
//    }

    fun selectedMuseModel() = mutableMuse.value?.model

    fun isStreamingData() = mutableMuse.value?.connectionState == ConnectionState.CONNECTED

    fun isNeedingUpdate() = mutableMuse.value?.connectionState == ConnectionState.NEEDS_UPDATE

    private fun updateMuses() {
        val museList = museAndroidManager.muses
        mutableMuses.onNext(Result.Loading(museList))
        mutableBluetoothState.onNext(
            if (isStreamingData() || isNeedingUpdate()) {
                BluetoothState.CONNECTED
            } else if (savedMuseMacAddress != null) {
                BluetoothState.SEARCHING
            } else {
                BluetoothState.SELECT
            }
        )
    }

    fun createConnectionAttempt(muse: Muse): Observable<Result<Muse?>> {
        connectMuse(muse)
        return museDataObservableFactory.createConnectionStateObservable(muse)
            .filter { it == ConnectionState.CONNECTED }
            .timeout(CONNECTION_ATTEMPT_SECS, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
            .flatMap<Result<Muse?>> { Observable.just(Result.Success(muse)) }
            .onErrorReturnItem(Result.Error(PetivityThrowable(), muse))
            .take(1)
    }

    private val xmuseDataListener = object : MuseDataListener() {
        override fun receiveMuseArtifactPacket(packet: MuseArtifactPacket?, muse: Muse?) {

        }

        override fun receiveMuseDataPacket(packet: MuseDataPacket?, muse: Muse?) {
        }

    }
    private fun connectMuse(muse: Muse) {
        val macAddress = muse.macAddress ?: return
        turnOnDiscovery()  // Setting discovery on some times helps a finicky connection
        savedMuseMacAddress = macAddress
//        if (getCurrentMuse()?.macAddress != muse.macAddress) {
        disconnectSelectedMuse()
        selectedMuseListener.onMuseSelected(muse)
//
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.EEG)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.ACCELEROMETER)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.DRL_REF)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.QUANTIZATION)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.PPG)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.OPTICS)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.GYRO)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.BATTERY)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.HSI_PRECISION)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.IS_PPG_GOOD)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.THERMISTOR)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.IS_GOOD)
////
////        xmuse.registerDataListener(xmuseDataListener, MuseDataPacketType.AVG_BODY_TEMPERATURE)
////
//////
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.ALPHA_ABSOLUTE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.BETA_ABSOLUTE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.DELTA_ABSOLUTE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.GAMMA_ABSOLUTE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.THETA_ABSOLUTE)
////
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.BETA_RELATIVE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.THETA_RELATIVE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.ALPHA_RELATIVE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.GAMMA_RELATIVE)
//        muse.registerDataListener(xmuseDataListener, MuseDataPacketType.DELTA_RELATIVE)
//
//        if (muse.model == MuseModel.MS_03) {
//            muse.setPreset(MusePreset.PRESET_1034)
//        } else {
//            muse.setPreset(MusePreset.PRESET_51)
//        }

        muse.runAsynchronously()
//        }
    }

    fun turnOnDiscovery() {
        museAndroidManager.stopListening()
        museAndroidManager.startListening()
//        museSelector.setAutomaticallyConnectToSavedMuse(true)

        // Sometimes the platform doesn't send discovery updates. Turning it off and on
        // periodically appears to be a sufficient workaround
        Observable.interval(3, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
            .subscribe {
                if (mutableMuses.value?.data?.find { savedMuseMacAddress == it.macAddress } == null) {
                    museAndroidManager.stopListening()
                    museAndroidManager.startListening()
                }
            }.let { discoveryDisposable = it }
    }

    fun turnOffDiscovery() {
        discoveryDisposable?.dispose()
        discoveryDisposable = null
//        museSelector.turnOffMuseScan()
    }

    fun disconnectMuse() {
//        museSelector.setAutomaticallyConnectToSavedMuse(false)
        disconnectSelectedMuse()
    }

    private fun disconnectSelectedMuse() {
        val selectedMuse = mutableMuse.value
        val selectedConnectState = selectedMuse?.connectionState
        when (selectedConnectState) {
            ConnectionState.CONNECTED, ConnectionState.CONNECTING, ConnectionState.NEEDS_UPDATE -> {
                selectedMuse.unregisterAllListeners()
                selectedMuse.disconnect()
            }

            else -> {

            }
        }
    }

    fun provideEegObservable(): Observable<MuseDataPacket> {
        if (getCurrentMuse() == null) {
            return Observable.empty()
        }
        return museDataObservableFactory.createDataObservable(
            getCurrentMuse()!!,
            MuseDataPacketType.EEG
        )
    }

    fun provideAccObservable(): Observable<MuseDataPacket> {
        if (getCurrentMuse() == null) {
            return Observable.empty()
        }
        return museDataObservableFactory.createDataObservable(
            getCurrentMuse()!!,
            MuseDataPacketType.ACCELEROMETER
        )
    }

//    fun setDisconnectMuseOnAppBackground(disconnect: Boolean) =
//        museSelector.setDisconnectMuseOnAppBackground(disconnect)

    fun getCurrentMuse(): Muse? {
        return mutableMuse.value
    }

}