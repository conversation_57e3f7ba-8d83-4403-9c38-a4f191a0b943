package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.choosemuse.libmuse.internal.AlphaPower
import com.choosemuse.libmuse.internal.SessionAlphaDetector
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable

class AlphaPeakDataTracker(
    private val alphaDetector: SessionAlphaDetector
) {

    val alphaPeakHz: Double?
        get() = alphaDetector.alphaPeak

    private var disposables: CompositeDisposable? = null

    fun startTracking(eegPackets: Observable<ArrayList<Double>>) {
        disposables = CompositeDisposable()
        eegPackets
            .map { ArrayList(it) }
            .subscribe { alphaDetector.processEeg(it) }
            .let { disposables?.add(it) }
    }

    fun stopTracking() {
        disposables?.clear()
        disposables = null
    }

    fun getAlphaPower(historicalAlphaPowerPwTimeDescending: List<Double>): AlphaPower {
        return alphaDetector.getAlphaPower(ArrayList(historicalAlphaPowerPwTimeDescending))
    }
}