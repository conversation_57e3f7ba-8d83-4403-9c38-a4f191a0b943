package com.ruiheng.xmuse.core.data.util

import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.data.AppExecutors
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.network.other.WxApiEmptyResponse
import com.ruiheng.xmuse.core.network.other.WxApiErrorResponse
import com.ruiheng.xmuse.core.network.other.WxApiResponse
import com.ruiheng.xmuse.core.network.other.WxApiSuccessResponse

/**
 * A generic class that can provide a resource backed by both the sqlite database and the network.
 *
 *
 * You can read more about it in the [Architecture
 * Guide](https://developer.android.com/arch).
 * @param <ResultType>
 * @param <RequestType>
</RequestType></ResultType> */
abstract class NetworkBoundResource<ResultType, RequestType>
@MainThread constructor(
    private val appExecutors: AppExecutors,
    private var loadDb: Boolean = true
) {

    private val result = MediatorLiveData<Result<ResultType>>()

    init {
        if (loadDb) {
            result.value = Result.Loading()
            @Suppress("LeakingThis")
            val dbSource = loadFromDb()
            result.addSource(dbSource!!) { data ->
                result.removeSource(dbSource)
                if (shouldFetch(data)) {
                    fetchFromNetwork(dbSource)
                } else {
                    result.addSource(dbSource) { newData ->
                        setValue(Result.Success(newData))
                    }
                }
            }
        } else {
            setValue(Result.Loading(null))
            fetchFromNetwork(null)
        }
    }

    @MainThread
    private fun setValue(newValue: Result<ResultType>) {
        if (result.value != newValue) {
            result.value = newValue
        }
    }

    private fun fetchFromNetwork(dbSource: LiveData<ResultType>?) {
        val apiResponse = createCall()
        if (loadDb && dbSource != null) {
            // we re-attach dbSource as a new source, it will dispatch its latest value quickly
            result.addSource(dbSource) { newData ->
                setValue(Result.Loading(newData))
            }
        }
        result.addSource(apiResponse!!) { response ->
            result.removeSource(apiResponse)
            if (dbSource != null) {
                result.removeSource(dbSource)
            }

            when (response) {
                is WxApiSuccessResponse -> {
                    appExecutors.diskIO().execute {
                        try {
                            val requestResult = processResponse(response)
                            saveCallResult(requestResult)
                            val requestResultPerson = processResult(requestResult)
                            appExecutors.mainThread().execute {

                                if (loadDb) {
                                    // we specially request a new live data,
                                    // otherwise we will get immediately last cached value,
                                    // which may not be updated with latest results received from network.
                                    result.addSource(loadFromDb()!!) { newData ->
                                        setValue(Result.Success(newData))
                                    }
                                } else {
                                    setValue(requestResultPerson)
                                }
                            }
                        } catch (e: Exception) {
                            onFetchFailed(response)
                            appExecutors.mainThread().execute {
                                if (loadDb && dbSource != null) {
                                    result.addSource(dbSource) { newData ->
                                        setValue(Result.Error(e, newData))
                                    }
                                } else {
                                    setValue(Result.Error(e, null))
                                }
                            }
                        }

                    }
                }

                is WxApiEmptyResponse -> {
                    appExecutors.diskIO().execute {
                        emptyResult()
                    }
                    appExecutors.mainThread().execute {
                        if (loadDb) {
                            // reload from disk whatever we had
                            result.addSource(loadFromDb()!!) { newData ->
                                setValue(Result.Success(newData))
                            }
                        } else {
                            setValue(Result.Empty(null))
                        }
                    }
                }

                is WxApiErrorResponse -> {
                    appExecutors.diskIO().execute {
                        onFetchFailed(response)
                    }
                    if (loadDb && dbSource != null) {
                        result.addSource(dbSource) { newData ->
                            setValue(
                                Result.Error(
                                    response.exception!!,
                                    newData

                                )
                            )
                        }
                    } else {
                        val exception = response.exception
                        var data: ResultType? = null
//                        if (exception is PetivityThrowable && exception.data != null) {
//                            try {
//                                data = processErrorResult(exception.data as RequestType)
//                            } catch (e: Exception) {
//
//                            }
//                        }
                        setValue(Result.Error(response.exception!!, data))
                    }
                }
            }
        }
    }

    @WorkerThread
    protected open fun onFetchFailed(result: WxApiResponse<RequestType>) {
    }

    fun asLiveData() = result as LiveData<Result<ResultType>>

    fun asMutableLiveData() = result as MutableLiveData<Result<ResultType>>

    @WorkerThread
    protected open fun processResponse(response: WxApiSuccessResponse<RequestType>) = response.body

    @WorkerThread
    protected open fun saveCallResult(item: RequestType) {

    }

    @WorkerThread
    protected open fun emptyResult() {

    }

    @WorkerThread
    protected open fun processResult(result: RequestType): Result<ResultType> {
        var realResult: Result<ResultType>
        try {
            realResult = Result.Success(result as ResultType)
        } catch (e: Exception) {
//            throw e
            realResult = Result.Empty(null)
        }
        return realResult
    }

    @WorkerThread
    protected open fun processErrorResult(result: RequestType): ResultType {
        return result as ResultType
    }

    @MainThread
    protected open fun shouldFetch(data: ResultType?) = true

    @MainThread
    protected open fun loadFromDb(): LiveData<ResultType>? {
        return null
    }

    @MainThread
    protected open fun createCall(): LiveData<WxApiResponse<RequestType>>? {
        return null
    }
}