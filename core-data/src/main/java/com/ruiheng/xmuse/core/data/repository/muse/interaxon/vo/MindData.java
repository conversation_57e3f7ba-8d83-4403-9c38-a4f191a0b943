// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from session.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;

public final class MindData {


    /*package*/ final ArrayList<Double> timeseries;

    /*package*/ final int calmPercentage;

    /*package*/ final int neutralPercentage;

    /*package*/ final int activePercentage;

    /*package*/ final int calmPoints;

    /*package*/ final int neutralPoints;

    /*package*/ final int calmSeconds;

    /*package*/ final int neutralSeconds;

    /*package*/ final int activeSeconds;

    public MindData(
            ArrayList<Double> timeseries,
            int calmPercentage,
            int neutralPercentage,
            int activePercentage,
            int calmPoints,
            int neutralPoints,
            int calmSeconds,
            int neutralSeconds,
            int activeSeconds) {
        this.timeseries = timeseries;
        this.calmPercentage = calmPercentage;
        this.neutralPercentage = neutralPercentage;
        this.activePercentage = activePercentage;
        this.calmPoints = calmPoints;
        this.neutralPoints = neutralPoints;
        this.calmSeconds = calmSeconds;
        this.neutralSeconds = neutralSeconds;
        this.activeSeconds = activeSeconds;
    }

    public ArrayList<Double> getTimeseries() {
        return timeseries;
    }

    public int getCalmPercentage() {
        return calmPercentage;
    }

    public int getNeutralPercentage() {
        return neutralPercentage;
    }

    public int getActivePercentage() {
        return activePercentage;
    }

    public int getCalmPoints() {
        return calmPoints;
    }

    public int getNeutralPoints() {
        return neutralPoints;
    }

    public int getCalmSeconds() {
        return calmSeconds;
    }

    public int getNeutralSeconds() {
        return neutralSeconds;
    }

    public int getActiveSeconds() {
        return activeSeconds;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "MindData{" +
                "timeseries=" + timeseries +
                "," + "calmPercentage=" + calmPercentage +
                "," + "neutralPercentage=" + neutralPercentage +
                "," + "activePercentage=" + activePercentage +
                "," + "calmPoints=" + calmPoints +
                "," + "neutralPoints=" + neutralPoints +
                "," + "calmSeconds=" + calmSeconds +
                "," + "neutralSeconds=" + neutralSeconds +
                "," + "activeSeconds=" + activeSeconds +
        "}";
    }

}
