package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindStatsDataTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.NeurofeedbackDataTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.ResultsMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionDurationTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlatformFeatureFlags
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlaybackCommand
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.core.Observable

@ActivityRetainedScoped
class SessionDataTracker(
    private val sessionProgressSaver: SessionProgressSaver,
    private val busymindStatsDataTracker: BusymindStatsDataTracker,
    private val neurofeedbackDataTracker: NeurofeedbackDataTracker,
    private val durationTracker: SessionDurationTracker,
    private val maxDataTrackingDurationMonitor: MaxDataTrackingDurationMonitor,
    private val enforceMaxSessionLength: Boolean,
    private val resultsMode: ResultsMode?,
    private val audioDurationSecs: Int,
//    private val nfbProcessor: NfbProcessor
) {

    @Inject
    constructor(
        sessionProgressSaver: SessionProgressSaver,
        busymindStatsDataTracker: BusymindStatsDataTracker,
        neurofeedbackDataTracker: NeurofeedbackDataTracker,
        maxDataTrackingDurationMonitor: MaxDataTrackingDurationMonitor,
//        featureFlags: PlatformFeatureFlags,
        durationTracker: SessionDurationTracker,
//        config: DataTrackingConfig,
//        audioDurationSecs: Int,
//        nfbProcessor: NfbProcessor
    ) : this(
        sessionProgressSaver,
        busymindStatsDataTracker,
        neurofeedbackDataTracker,
        durationTracker,
        maxDataTrackingDurationMonitor,
        true,
//        featureFlags.isFeatureFlagEnabled(
//            PlatformFeatureFlags.FEATURE_FLAG_ENFORCE_MAX_SESSION_LENGTH,
//            true
//        ),
        SessionManager.dataTrackingConfig?.resultsMode,
        2000
//        audioDurationSecs,
//        nfbProcessor
    )

    enum class TrackingState {
        INIT,
        RUNNING,
        PAUSED,
        ENDED,
    }

    private val disposableBag = CompositeDisposable()

    val dataTrackingState = BehaviorSubject.createDefault(TrackingState.INIT)

    val finishedSavingProgress: Observable<Boolean>
        get() = sessionProgressSaver.finishedSavingProgress
    val maxDataTrackingTimeReached: Observable<Boolean>
        get() = maxDataTrackingDurationMonitor.maxDataTrackingTimeReached
    val secondsElapsed
        get() = durationTracker.completedSeconds
    val durationTrackingState
        get() = durationTracker.durationTrackingState

    fun startTracking() {
        durationTracker.startTracking()
        busymindStatsDataTracker.setInitialBusymindMode()
        busymindStatsDataTracker.startTracking()
        neurofeedbackDataTracker.setupInitialConditions()
        neurofeedbackDataTracker.startTracking()

        sessionProgressSaver.startAutomaticProgressSaving(
            jobObservable = when (resultsMode) {
                ResultsMode.SLEEP ->
                    SessionProgressSaver.createSleepSessionUpdateObservable()

                else ->
                    SessionProgressSaver.createMeditationSessionUpdateObservable(
                        audioDurationSecs.toLong(),
                        durationTracker.completedSeconds
                    )
            }
        )

        if (resultsMode == ResultsMode.MEDITATE) {
//            nfbProcessor.command
//                .subscribe {
//                    @Suppress("WHEN_ENUM_CAN_BE_NULL_IN_JAVA")
//                    when (it) {
//                        PlaybackCommand.PAUSE -> pause(pauseDurationTracking = true)
//                        PlaybackCommand.RESUME -> resume()
//                    }
//                }.let { disposableBag.add(it) }
        }

        if (enforceMaxSessionLength) {
            maxDataTrackingDurationMonitor.startMonitoring()
            maxDataTrackingDurationMonitor.maxDataTrackingTimeReached
                .subscribe {
                    stopTracking(addExtraTime = true)
                }.let { disposableBag.add(it) }
        }

        dataTrackingState.onNext(TrackingState.RUNNING)
    }

    fun pause(pauseDurationTracking: Boolean) {
        if (pauseDurationTracking) {
            durationTracker.pauseTracking()
            neurofeedbackDataTracker.stopTracking()
        } else {
            neurofeedbackDataTracker.startNewDataGap()
        }
        busymindStatsDataTracker.pauseTracking()
        dataTrackingState.onNext(TrackingState.PAUSED)
    }

    fun resume() {
        if (dataTrackingState.value != TrackingState.PAUSED) return
        neurofeedbackDataTracker.endDataGap()
        neurofeedbackDataTracker.startTracking()
        durationTracker.resumeTracking()
        busymindStatsDataTracker.resumeTracking()
        dataTrackingState.onNext(TrackingState.RUNNING)
    }

    fun stopTracking(addExtraTime: Boolean) {
        if (dataTrackingState.value == TrackingState.INIT ||
            dataTrackingState.value == TrackingState.ENDED
        ) return
        busymindStatsDataTracker.stopTracking()
        neurofeedbackDataTracker.stopTracking()
        durationTracker.stopTracking()
        maxDataTrackingDurationMonitor.stopMonitoring()
        disposableBag.clear()
        sessionProgressSaver.stopAutomaticProgressSaving(addExtraTime)
        dataTrackingState.onNext(TrackingState.ENDED)
    }
}