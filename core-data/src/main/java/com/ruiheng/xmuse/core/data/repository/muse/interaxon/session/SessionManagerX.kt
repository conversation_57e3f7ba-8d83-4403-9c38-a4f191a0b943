package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseManager
import com.choosemuse.libmuse.internal.MuseSessionType
import com.choosemuse.libmuse.internal.SessionArgs
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDeadBatteryMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.SessionPacketLossCounter
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.DataTrackingConfig
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.SessionDataTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindStatsDataTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.FmodMaps
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.CompositeDisposable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@ActivityRetainedScoped
class SessionManager
@Inject constructor(
//    private val sessionComponentFactory: SessionComponentFactory,
//    private val userManager: UserManager,
    private val durationTracker: SessionDurationTracker,
    private val dataTracker: SessionDataTracker,
    private val deadBatteryMonitor: MuseDeadBatteryMonitor,
//    private val sqcDisconnectionMonitor: MuseDisconnectionMonitor,
//    private val badSignalMonitor: MuseBadSignalMonitor,
    private val busymindDataTracker: BusymindStatsDataTracker,
    private val packetLossCounter: SessionPacketLossCounter,
    private val museDisconnectionCounter: SessionMuseDisconnectionCounter,
    private val museManager: MuseManager,
    private val museConnector: MuseConnector
) {

    companion object {
        const val SESSION_ID_CRASHLYTICS_KEY = "sessionID"

        var dataTrackingConfig: DataTrackingConfig? = null

    }

//    private var sessionComponent: SessionComponent? = null

    val sessionId: String?
        get() = dataTrackingConfig?.sessionId

//    val dataTracker: SessionDataTracker
//        get() = sessionComponent!!.dataTracker
//
//    val durationTracker: SessionDurationTracker
//        get() = sessionComponent!!.durationTracker

//    val nfbAudioPlayer: NfbAudioPlayer
//        get() = sessionComponent!!.nfbAudioPlayer
//
//    val nfbMessageMonitor: NfbMessageMonitor
//        get() = sessionComponent!!.nfbMessageMonitor

//    val deadBatteryMonitor: MuseDeadBatteryMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.deadBatteryMonitor
//        } else {
//            null
//        }

//    val challengeCompletionMonitor: ChallengeCompletionMonitor?
//        get() = sessionComponent?.challengeCompletionMonitor
//
//    val sqcDisconnectionMonitor: MuseDisconnectionMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.sqcDisconnectionMonitor()
//        } else {
//            null
//        }
//
//    val meditationPlayerDisconnectionMonitor: MuseDisconnectionMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.meditationPlayerDisconnectionMonitor()
//        } else {
//            null
//        }

//    val badSignalMonitor: MuseBadSignalMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.badSignalMonitor()
//        } else {
//            null
//        }

//    val busymindDataTracker: BusymindStatsDataTracker
//        get() = sessionComponent!!.busymindDataTracker


//    val storageSynchronizer: SessionJourneyUserStorageSynchronizer
//        get() = sessionComponent!!.storageSynchronizer
//
//    val alertRecoveryAnalytics: AlertRecoveryAnalytics
//        get() = sessionComponent!!.alertRecoveryAnalytics

//    val museDeviceSelector: MuseDeviceSelector
//        get() = museConnector.museSelector
//
//    val nfbProcessor: NfbProcessor?
//        get() = sessionComponent?.nfbProcessor

    private var disposables: CompositeDisposable? = null
    private val mutableDataTrackingState =
        MutableLiveData(
            dataTracker.dataTrackingState?.value
                ?: SessionDataTracker.TrackingState.INIT
        )
//    val sessionOngoing: Boolean
//        get() = sessionComponent != null

    //    val webviewSessionAdapter: WebviewSessionAdapter?
//        get() = if (dataTrackingConfig?.isWebSession == true) {
//            sessionComponent?.webviewSessionAdapter
//        } else {
//            null
//        }
    private val disposableBag = CompositeDisposable()
    val elapsedTime: LiveData<Long>
        get() = mutableElapsedTime
    private val mutableElapsedTime = MutableLiveData<Long>(0)
    fun startSession() {
//        if (sessionOngoing) {
//            Log.w("SessionManager", "Ignore startSession. Session is already running")
//            return
//        }
//
        disposables = CompositeDisposable()
//        SessionManager.dataTrackingConfig = dataTrackingConfig
//        val legacySession = MuseApplication.getInstance().vmFactory.createSession(cppConfig)
//        legacySession.startSession()
//        MuseApplication.getInstance().vmFactory.setCurrentSession(legacySession)
        val muse = if (dataTrackingConfig?.muse == null) {
            null
        } else {
            museConnector.getCurrentMuse()
        }
//        sessionComponent = sessionComponentFactory.create(muse, dataTrackingConfig).apply {
//            setupMuseForSession(muse, this, dataTrackingConfig)
//
////            sessionFileWriter.startNewRecording()
////            oscMessageSender.startMonitoring()
////            signalAlertNotification.startMonitoringAlerts()
////            storageSynchronizer.startObservingStorage()
////
////            if (dataTrackingConfig.resultsMode == ResultsMode.MEDITATE) {
////                challengeCompletionMonitor.startMonitoring()
////            }
//        }

        deadBatteryMonitor?.startMonitoring()
//        context.startService(Intent(context, SessionService::class.java))
//
//        with(userManager.remoteSessionSynchronizer) {
//            forestallUpload(dataTrackingConfig.utcTimestamp)
//            createSession(dataTrackingConfig.toUserSession())
//        }
//        logStartSession(dataTrackingConfig)
//        FirebaseCrashlytics.getInstance()
//            .setCustomKey(SESSION_ID_CRASHLYTICS_KEY, dataTrackingConfig.sessionId)
//        updateRecentsPlaylist(dataTrackingConfig)
        dataTracker.secondsElapsed.observeOn(AndroidSchedulers.mainThread())
            .subscribe(mutableElapsedTime::postValue).let { disposableBag.add(it) }

        dataTracker.dataTrackingState.observeOn(AndroidSchedulers.mainThread()).subscribe {
            Timber.d("dataTrackingState:${it}")
            when (it) {
                SessionDataTracker.TrackingState.INIT -> {
                    dataTracker.startTracking()
//                        startAlertMonitoring()
                }

                SessionDataTracker.TrackingState.ENDED -> {
//                        logEndMeditation()
                }

                else -> {
                }
            }
            mutableDataTrackingState.postValue(it)
        }.let { disposableBag.add(it) }
    }

    private fun setupMuseForSession(
        muse: Muse?,
        dataTrackingConfig: DataTrackingConfig
    ) {
        if (muse != null) {
            museManager.setBusymindSessionArgs(createBusymindSessionArgs(dataTrackingConfig))
            packetLossCounter.start()
//            museManager.busymindMonitor.nfbPackets
//                .subscribe {
//                    session.dataStatusMonitor.receiveBusymindPacket(
//                        it,
//                        museConnector.museSelector.selectedMuse
//                    )
//                }.let { disposables?.add(it) }

            museDisconnectionCounter.startCounting()
        } else {
            museManager.disableMuseUsage()
        }
    }

    private fun createBusymindSessionArgs(dataTrackingConfig: DataTrackingConfig): SessionArgs {
//        val museSessionType = when (dataTrackingConfig.sessionType) {
//            SessionType.FRONTAL_OXYGENATION -> MuseSessionType.FRONTAL_OXYGENATION
//            SessionType.MIND -> MuseSessionType.MIND
//            SessionType.TIMED -> MuseSessionType.TIMED
//            SessionType.BODY -> MuseSessionType.BODY
//            SessionType.HEART -> MuseSessionType.HEART
//            SessionType.BREATH -> MuseSessionType.BREATH
//            SessionType.GUIDED -> MuseSessionType.GUIDED
//            SessionType.SLEEP -> MuseSessionType.SLEEP
//        }

        return SessionArgs(
            /* sessionType = */
            dataTrackingConfig.sessionType,
            /* soundscapeContentId = */
            dataTrackingConfig.sessionInfo?.fmodContent?.config?.soundscapeContentId ?: "",
            /* guidanceContentId = */
            "",
            /* sessionLength = */
            dataTrackingConfig.sessionInfo?.sessionLengthSeconds?.toLong() ?: 0L,
            /* guidanceLengthSecs = */
            0.0,
            /* fmodEvent = */
            dataTrackingConfig.sessionInfo?.fmodContent?.config?.fmodEvent ?: "",
            /* nanChannelMirroringEnabled = */
            true,
//            museManager.museCharacteristicsManager.isCharacteristicsExperimentEnabled,
            /* fmodMap = */
            dataTrackingConfig.sessionInfo?.fmodContent?.config?.fmodMap
                ?: FmodMaps.getFmodParameterMap(
                    dataTrackingConfig.sessionInfo?.fmodContent?.config?.soundscapeContentId ?: ""
                ),
            /* tfFilePaths = */
            HashMap()
        )
    }

    fun stopDataTracking(addExtraTime: Boolean) {
        dataTracker.stopTracking(addExtraTime)
//        meditationPlayerDisconnectionMonitor?.stopMonitoring()
//        badSignalMonitor?.stopMonitoring()
    }

    fun endSession() {
//        if (!sessionOngoing) {
//            Log.w("SessionManager", "No session to end")
//            return
//        }

//        sessionComponent?.legacySession?.endSession()
//        MuseApplication.getInstance().vmFactory.setCurrentSession(null)

        val disconnectionCount = museDisconnectionCounter.stopCounting() ?: 0
        val packetLossPercent = packetLossCounter.packetLossPercent ?: 0.0
        dataTrackingConfig?.let {
//            val realmSession = userManager.session.getSession(it.utcTimestamp)
//            logEndSession(it, realmSession, disconnectionCount, packetLossPercent)
//            with(userManager.remoteSessionSynchronizer) {
//                permitUpload(it.utcTimestamp)
//                refresh()
//            }
        }
        deadBatteryMonitor.stopMonitoring()

        if (dataTrackingConfig?.muse == null) {
            museManager.enableMuseUsage()
        }


//            signalAlertNotification.stopMonitoringAlerts()
//            sessionFileWriter.closeRecording()
//            oscMessageSender.shutdown()
//            storageSynchronizer.stopObservingStorage()
//            challengeCompletionMonitor.stopMonitoring()
        packetLossCounter.end()

//        sessionComponent = null
        dataTrackingConfig = null
        disposables?.clear()
        disposables = null
//        context.stopService(Intent(context, SessionService::class.java))
    }

}