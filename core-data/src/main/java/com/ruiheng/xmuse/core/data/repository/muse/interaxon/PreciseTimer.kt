package com.ruiheng.xmuse.core.data.repository.muse.interaxon

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import org.threeten.bp.Instant
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * 高精度计时器 - 用于精确跟踪和发布毫秒级时间流逝
 * 支持暂停、恢复、重置等操作，基于系统时间戳实现稳定计时
 * 主要用于音频播放同步、冥想训练计时等需要精确时间控制的场景
 */
class PreciseTimer
constructor(
    private val epochMillisSecondIntervals: Observable<Long>,
    private val epochMillis: () -> Long
) {

    @Inject
    constructor() : this(
        // 默认使用10Hz频率（每100ms）更新一次，适用于音频播放时间同步
        Observable.interval(100, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
            .map { Instant.now().toEpochMilli() },
        { Instant.now().toEpochMilli() }
    )

    /** 计时器订阅，控制计时周期 */
    private var intervalDisposable: Disposable? = null
    private var epochMillisOnLastCountdown: Long? = null //上次计时的系统时间戳（毫秒）
    private var elapsedMillisSinceLastCount: Long? = null //上次计时的系统时间戳（毫秒）

    val millisElapsed = BehaviorSubject.createDefault<Long>(0) //总毫秒数
    val state = BehaviorSubject.createDefault(PlaybackState.INIT) //计时器当前状态

    /**
     * 重置并启动计时器
     * 会将已流逝时间归零，并切换到PLAYING状态
     */
    fun restart() {
        state.onNext(PlaybackState.RESTARTING)
        this.millisElapsed.onNext(0)
        start()
    }

    /**
     * 重置并启动计时器
     * 会将已流逝时间归零，并切换到PLAYING状态
     */
    fun start() {
        intervalDisposable?.dispose()
        intervalDisposable = null

        state.onNext(PlaybackState.PLAYING)
        epochMillisOnLastCountdown = epochMillis.invoke() // 记录启动时间
        intervalDisposable = epochMillisSecondIntervals
            .subscribe {
                if (intervalDisposable != null) {
                    countSecondsElapsed(it)
                }
            }
    }

    /**
     * 计算并更新已流逝的时间
     * @param currentEpochMillis 当前系统时间戳（毫秒）
     */
    private fun countSecondsElapsed(currentEpochMillis: Long) {
        val then = epochMillisOnLastCountdown ?: return
        val now = currentEpochMillis + (elapsedMillisSinceLastCount ?: 0)
        elapsedMillisSinceLastCount = null
        val elapsedMillis = now - then
        epochMillisOnLastCountdown = currentEpochMillis // 更新上次计时时间
        if (elapsedMillis <= 0) {
            return
        }
        millisElapsed.onNext(millisElapsed.value!! + elapsedMillis)
    }

    /**
     * 恢复暂停的计时器
     * 仅在已暂停状态下有效，会继续累加之前的时间
     */
    fun resume() {
        // 若没有暂停时间差，无需恢复
        elapsedMillisSinceLastCount ?: return
        start()
    }

    // 若没有暂停时间差，无需恢复
    fun pause() {
        if (state.value!! != PlaybackState.PLAYING) {
            return
        }
        state.onNext(PlaybackState.PAUSED)
        val then = epochMillisOnLastCountdown ?: return
        elapsedMillisSinceLastCount = epochMillis.invoke() - then
        intervalDisposable?.dispose()
        intervalDisposable = null
    }

    // 若没有暂停时间差，无需恢复
    fun end() {
        if (state.value!! == PlaybackState.ENDED) {
            return
        }
        state.onNext(PlaybackState.ENDED)
        intervalDisposable?.dispose()
        intervalDisposable = null
    }
}