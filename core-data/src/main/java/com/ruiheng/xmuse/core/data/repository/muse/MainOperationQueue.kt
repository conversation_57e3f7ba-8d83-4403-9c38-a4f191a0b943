package com.ruiheng.xmuse.core.data.repository.muse

import com.choosemuse.libmuse.internal.Operation
import com.choosemuse.libmuse.internal.PlatformMainOperationQueue
import com.choosemuse.libmuse.internal.PlatformScheduledFuture
import com.choosemuse.libmuse.internal.TimeUnit
import java.util.Timer
import java.util.TimerTask

class MainOperationQueue: PlatformMainOperationQueue() {
    private var timer: Timer? = null

    init {
        timer = Timer()
    }

    private fun milliseconds(unit: TimeUnit, value: Long): Long {
        return when (unit) {
            TimeUnit.MICROSECONDS -> value / 1000
            TimeUnit.SECONDS -> value * 1000
            else -> value
        }
    }

    override fun executeAsync(operation: Operation) {
        schedule(operation, 0, TimeUnit.MILLISECONDS)
    }

    override fun executeSync(operation: Operation) {
        operation.onExecute()
    }

    override fun schedule(
        operation: Operation,
        delay: Long,
        unit: TimeUnit
    ): PlatformScheduledFuture {
        val task = getTask(operation)
        timer!!.schedule(task, milliseconds(unit, delay))
        return ScheduledFuture(task)
    }

    override fun scheduleAtFixedRate(
        operation: Operation,
        initDelay: Long,
        period: Long,
        unit: TimeUnit
    ): PlatformScheduledFuture {
        val task = getTask(operation)
        timer!!.schedule(task, milliseconds(unit, initDelay), milliseconds(unit, period))
        return ScheduledFuture(task)
    }

    private fun getTask(operation: Operation): TimerTask {
        return object : TimerTask() {
            override fun run() {
                operation.onExecute()
            }
        }
    }
}

 class ScheduledFuture(var task: TimerTask?) : PlatformScheduledFuture() {
    override fun cancel() {
        if (task != null) {
            task!!.cancel()
        }
        task = null
    }
}