// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

public final class SleepSessionInfo {


    /*package*/ final String teacherPhotoUrl;

    /*package*/ final String teacherName;

    /*package*/ final int sessionLengthSeconds;

    /*package*/ final GuidanceContent guidanceContent;

    /*package*/ final FmodContent fmodContent;

    public SleepSessionInfo(
            String teacherPhotoUrl,
            String teacherName,
            int sessionLengthSeconds,
            GuidanceContent guidanceContent,
            FmodContent fmodContent) {
        this.teacherPhotoUrl = teacherPhotoUrl;
        this.teacherName = teacherName;
        this.sessionLengthSeconds = sessionLengthSeconds;
        this.guidanceContent = guidanceContent;
        this.fmodContent = fmodContent;
    }

    public String getTeacherPhotoUrl() {
        return teacherPhotoUrl;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public int getSessionLengthSeconds() {
        return sessionLengthSeconds;
    }

    public GuidanceContent getGuidanceContent() {
        return guidanceContent;
    }

    public FmodContent getFmodContent() {
        return fmodContent;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "SleepSessionInfo{" +
                "teacherPhotoUrl=" + teacherPhotoUrl +
                "," + "teacherName=" + teacherName +
                "," + "sessionLengthSeconds=" + sessionLengthSeconds +
                "," + "guidanceContent=" + guidanceContent +
                "," + "fmodContent=" + fmodContent +
        "}";
    }

}
