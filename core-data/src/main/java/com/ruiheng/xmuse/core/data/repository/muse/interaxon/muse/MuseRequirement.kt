package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.MuseModel
import com.choosemuse.libmuse.internal.MuseSessionType

enum class MuseRequirement {
    NO_MUSE {
        override val museModels = emptyList<MuseModel>()
    },
    ANY_MUSE {
        override val museModels = MuseModel.entries
    },
    PPG_MUSE {
        override val museModels = MuseModel.entries.filter { model ->
            when (model) {
                MuseModel.MU_01,
//                MuseModel.GL_01,
                MuseModel.MU_02 -> false

                MuseModel.MU_03,
                MuseModel.MU_04,
                MuseModel.MU_05,
                MuseModel.MU_06,
                MuseModel.MS_03-> true
//                MuseModel.AMHL_01
            }
        }
    },
    SLEEP_MUSE {
        override val museModels = MuseModel.entries.filter { model ->
            when (model) {
                MuseModel.MU_01,
//                MuseModel.GL_01,
//                MuseModel.AMHL_01,
                MuseModel.MU_03,
                MuseModel.MU_02,
                MuseModel.MU_06 -> false

                MuseModel.MU_04,
                MuseModel.MU_05,
                MuseModel.MS_03 -> true
            }
        }
    },
    OPTIONAL_ANY_MUSE {
        override val museModels = MuseModel.entries
    },
    OPTICS_MUSE {
        override val museModels = MuseModel.entries.filter { model ->
            when (model) {
                MuseModel.MU_01,
//                MuseModel.GL_01,
//                MuseModel.AMHL_01,
                MuseModel.MU_03,
                MuseModel.MU_02,
                MuseModel.MU_06,
                MuseModel.MU_04,
                MuseModel.MU_05 -> false

                MuseModel.MS_03 -> true
            }
        }

    };

    abstract val museModels: List<MuseModel>

    companion object {
        fun toMuseRequirement(
            sessionType: MuseSessionType?,
            requiresMuse: Boolean
        ): MuseRequirement {
            return when (sessionType) {
                MuseSessionType.TIMED -> NO_MUSE
                MuseSessionType.BODY,
                MuseSessionType.HEART,
                MuseSessionType.BREATH -> PPG_MUSE

                MuseSessionType.SLEEP -> SLEEP_MUSE
                MuseSessionType.MIND -> ANY_MUSE

                null,
                MuseSessionType.GUIDED -> if (requiresMuse) {
                    ANY_MUSE
                } else {
                    OPTIONAL_ANY_MUSE
                }

                MuseSessionType.FRONTAL_OXYGENATION -> OPTICS_MUSE
            }
        }
    }
}