//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session
//
//import com.choosemuse.libmuse.Muse
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.ApplicationComponent
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.DataTrackingConfig
//import javax.inject.Inject
//import javax.inject.Singleton
//
//@Singleton
//class SessionComponentFactory
//@Inject constructor() {
//
//    fun create(
//        muse: Muse?,
////        legacySession: LegacySession,
////        userManager: UserManager,
//        config: DataTrackingConfig
//    ): SessionComponent {
//        return DaggerSessionComponent.builder()
//            .sessionModule(SessionModule.WithConfig(config))
////            .sessionUserModule(SessionUserModule(userManager))
//            .sessionMuseModule(
//                if (muse == null) {
//                    SessionMuseModule.WithoutMuse()
//                } else {
//                    SessionMuseModule.WithMuse(muse)
//                }
//            )
//            .build()
//    }
//}