package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.realtime_neurofeedback

import android.util.Log
import com.choosemuse.libmuse.internal.Classifier
import com.choosemuse.libmuse.internal.DataWindow
import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.DataWindowBufferDelegate
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.core.Observable
import com.choosemuse.libmuse.internal.RealtimeModel

class TfliteProcessor(
    private val classifier: Classifier,
    private val buffer: DataWindowBuffer,
    private val modelName: String,
    private val scheduler: Scheduler
) : DataWindowBufferDelegate() {

    companion object {
        fun createFromConfig(
            tfliteFactory: TfliteFactory,
            model: RealtimeModel,
            scheduler: Scheduler
        ): TfliteProcessor? {
            return model.let {
                if (it.name != null &&
                    it.hopSize != null &&
                    it.hopInputType != null &&
                    it.fmodParameters != null
                ) {
                    val classifier = tfliteFactory.createClassifier(it.name)
                    val buffer = tfliteFactory.createBuffer(
                        it.name,
                        it.hopSize.toLong(),
                        it.hopInputType
                    )
                    if (classifier != null && buffer != null) {
                        TfliteProcessor(classifier, buffer, it.name, scheduler)
                    } else {
                        Log.w("NfbAudioPlayer", "Skipping model ${it.name}")
                        null
                    }
                } else {
                    null
                }
            }
        }
    }

    private val dataWindowCreatedHandler = BehaviorSubject.create<Any>()
    private var disposable: Disposable? = null

    val output = BehaviorSubject.create<ArrayList<Float>>()

    fun addData(timestamp: Float, data: ArrayList<Float>) = buffer.updateBuffer(timestamp, data)

    fun activate() {
        if (disposable != null) {
            return
        }

        disposable = dataWindowCreatedHandler.observeOn(scheduler)
            .map { buffer.flushWindows() }
            .flatMap { Observable.fromArray(*it.toTypedArray()) }  // process each window individually
            .map { ArrayList<DataWindow>(1).apply { add(it) } }
            .map {
                classifier.processData(it).data
            }
            .subscribe { output.onNext(it) }
        buffer.setDataWindowDelegate(this)
    }

    fun deactivate() {
        buffer.setDataWindowDelegate(null)
        disposable?.dispose()
        disposable = null
    }

    override fun onDataWindowCreated() {
        dataWindowCreatedHandler.onNext(Any())
    }
}