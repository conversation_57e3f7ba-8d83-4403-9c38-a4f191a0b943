package com.ruiheng.xmuse.core.data.repository.net

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.time.getLocalDateTimeFromUTCString
import com.ruiheng.xmuse.core.common.result.time.getUTCNowTimeString
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.SessionUpdate
import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepInfo
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepReportInfo
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepUserTag
import com.ruiheng.xmuse.core.network.sleep.SleepRxService
import com.ruiheng.xmuse.core.network.sleep.SleepService
import io.reactivex.rxjava3.core.Observable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesSleepRepository @Inject constructor(
    private val sleepService: SleepService,
    private val sleepRxService: SleepRxService
) {

    /**
     * 获取睡眠模块用户画像分类
     */
    fun fetchSleepUserTagList() =
        flow<Result<List<ApiRequestSleepUserTag>>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson()
            val response = sleepService.loadSleepUserTagList(requestBody)
            requestCatch(
                response,
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 获取睡眠模块用户画像分类
     */
    fun startSleepSession() =
        flow<Result<ApiRequestSleepInfo>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson()
            val response = sleepService.startSleep(requestBody)
            requestCatch(
                response,
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun loadSleepSessionRecordList() = flow<Result<List<ApiRequestSleepInfo>>> {
        emit(Result.Loading())

        val pageInfoObject = JsonObject()
        pageInfoObject.addProperty("pageNo", 1)
        pageInfoObject.addProperty("pageSize", 50)
        val pageObject = JsonObject()
        pageObject.add("page",pageInfoObject)
        val data = JsonObject()
        data.add("data", pageObject)

        val requestBody = data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
        val response = sleepService.loadSleepRecordList(requestBody)
        requestCatch(
            response,
            dataResponse = {
                var lastMonth:Int? = null
                it?.list?.forEachIndexed { index, apiRequestSleepInfo ->
                    val itemMonth =  apiRequestSleepInfo.startTime?.getLocalDateTimeFromUTCString()?.monthValue
                    if (lastMonth == null && !apiRequestSleepInfo.startTime.isNullOrEmpty() ){
                        apiRequestSleepInfo.displayMonth = true
                    }
                    if (itemMonth != lastMonth && !apiRequestSleepInfo.startTime.isNullOrEmpty() ){
                        apiRequestSleepInfo.displayMonth = true
                    }
                    lastMonth = itemMonth
                }
                it?.list
            },
            successCallback = { result ->
                emit(Result.Success(result))
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun loadSessionRecordDetail(recordId: String) =
        flow<Result<ApiRequestSleepReportInfo>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson(Pair("reportId", recordId))
            val response = sleepService.loadSleepRecordDetail(requestBody)
            requestCatch(
                response,
                dataResponse = {result->
                    result?.dynamicModule?.colDataList = averageEveryFourElements(result?.dynamicModule?.colDataList)?.map(Double::toFloat)
                    result?.heartRateModule?.colDataList = averageEveryFourElements(result?.heartRateModule?.colDataList)?.map(Double::toFloat)
                    result
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun startRxPushSessionProgress(
        sessionId: String,
        sessionUpdate: SessionUpdate,
        startTime: String,
    ): Observable<ApiRequestResult<Any>> {
        val stageList = sessionUpdate.timeSeries?.sleepStages
        val postureList = sessionUpdate.timeSeries?.sleepPositions
        val heartBeatList = sessionUpdate.timeSeries?.heartRateDownSampled
        val bodyMovementList = sessionUpdate.timeSeries?.bodyMovementDownSampled
        Timber.d("startRxPushSessionProgress:${heartBeatList.contentToString()}")
        val contentJson = JsonObject()
        contentJson.addProperty("reportId", sessionId)
        contentJson.addProperty("startTime", startTime)
        contentJson.addProperty("endTime", getUTCNowTimeString())
        val stageArray = JsonArray()
        stageList?.forEach {
            stageArray.add(it)
        }
        val postureArray = JsonArray()
        postureList?.forEach {
            postureArray.add(it)
        }
        val heartArray = JsonArray()
        heartBeatList?.forEach {
            heartArray.add(it)
        }
        val dynamicArray = JsonArray()
        bodyMovementList?.forEach {
            dynamicArray.add(it)
        }
        contentJson.add("stageList",stageArray)
        contentJson.add("postureList",postureArray)
        contentJson.add("heartRateList",heartArray)
        contentJson.add("dynamicList",dynamicArray)
        val data = JsonObject()
        data.add("data", contentJson)
        val requestBody = data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
        return sleepRxService.pushSleepData(requestBody)
    }

    fun rxEndSession(sessionId: String): Observable<Result<Any?>> {
        val requestBody = generateRequestDataJson(
            Pair("reportId", sessionId),
        )
        return sleepRxService.endSleepSession(requestBody).map { it.toResult() }
    }
}

fun <T : Number> averageEveryFourElements(list: List<T>?): List<Double>? {
    val result = mutableListOf<Double>()
    if (list.isNullOrEmpty())return result
    var i = 0
    while (i < list.size) {
        // 计算当前批次的结束索引（剩余元素数量可能不足4个）
        val endIndex = (i + 4).coerceAtMost(list.size)
        // 截取当前批次的元素
        val batch = list.subList(i, endIndex)
        // 计算平均值（无论元素数量多少都计算）
        val average = batch.sumOf { it.toDouble() } / batch.size
        result.add(average)
        // 移动到下一批次
        i += 4
    }
    return result
}


