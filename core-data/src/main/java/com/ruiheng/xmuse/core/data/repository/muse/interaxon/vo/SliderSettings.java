// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

public final class SliderSettings {


    /*package*/ final String lowLabel;

    /*package*/ final String highLabel;

    /*package*/ final Float minValue;

    /*package*/ final Float maxValue;

    /*package*/ final Float defaultValue;

    /*package*/ final Boolean currentValueVisible;

    public SliderSettings(
            String lowLabel,
            String highLabel,
            Float minValue,
            Float maxValue,
            Float defaultValue,
            Boolean currentValueVisible) {
        this.lowLabel = lowLabel;
        this.highLabel = highLabel;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.defaultValue = defaultValue;
        this.currentValueVisible = currentValueVisible;
    }

    public String getLowLabel() {
        return lowLabel;
    }

    public String getHighLabel() {
        return highLabel;
    }

    public Float getMinValue() {
        return minValue;
    }

    public Float getMaxValue() {
        return maxValue;
    }

    public Float getDefaultValue() {
        return defaultValue;
    }

    public Boolean getCurrentValueVisible() {
        return currentValueVisible;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "SliderSettings{" +
                "lowLabel=" + lowLabel +
                "," + "highLabel=" + highLabel +
                "," + "minValue=" + minValue +
                "," + "maxValue=" + maxValue +
                "," + "defaultValue=" + defaultValue +
                "," + "currentValueVisible=" + currentValueVisible +
        "}";
    }

}
