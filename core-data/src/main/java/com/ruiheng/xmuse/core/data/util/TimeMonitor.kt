package com.ruiheng.xmuse.core.data.util

import com.ruiheng.xmuse.core.common.result.network.Dispatcher
import com.ruiheng.xmuse.core.common.result.network.MuseDispatchers
import com.ruiheng.xmuse.core.common.result.network.di.ApplicationScope
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Observer
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.launch
import java.time.LocalTime
import java.util.Calendar
import javax.inject.Inject
import javax.inject.Singleton

interface TimeMonitor {
    //    val timeHourUpdate: Flow<Long>
    val greetingUpdateFlow: SharedFlow<GreetingTimeOfDay>
}

@Singleton
internal class TimeBroadcastMonitor @Inject constructor(
    @ApplicationScope appScope: CoroutineScope,
    @Dispatcher(MuseDispatchers.IO) private val ioDispatcher: CoroutineDispatcher
) : TimeMonitor {
    //    override val timeHourUpdate: SharedFlow<Long> = flow<Long> {
//        trySend(System.currentTimeMillis())
//    }.distinctUntilChanged()
//        .conflate()
//        .flowOn(ioDispatcher)
//        .shareIn(appScope, SharingStarted.WhileSubscribed(5_000), 1)
    override val greetingUpdateFlow: SharedFlow<GreetingTimeOfDay> = flow {
        while (true) {
            val calendar = Calendar.getInstance()
            val hour = calendar.get(Calendar.HOUR_OF_DAY)
            val timePeriod = when {
                hour in GreetingTimeOfDay.Morning.startTimeHour until GreetingTimeOfDay.Morning.endTimeHour -> GreetingTimeOfDay.Morning
                hour in GreetingTimeOfDay.Noon.startTimeHour until GreetingTimeOfDay.Noon.endTimeHour -> GreetingTimeOfDay.Noon
                hour in GreetingTimeOfDay.Afternoon.startTimeHour until GreetingTimeOfDay.Afternoon.endTimeHour -> GreetingTimeOfDay.Afternoon
                else -> GreetingTimeOfDay.Evening
            }
            emit(timePeriod)
            delay(1 * 60 * 1000L)
        }
    }.flowOn(ioDispatcher)
        .shareIn(appScope, SharingStarted.WhileSubscribed(5_000), 1)

}

enum class GreetingTimeOfDay(val startTimeHour: Int, val endTimeHour: Int) {
    Morning(6, 12),
    Noon(12, 14),
    Afternoon(14, 18),
    Evening(18, 6)
}