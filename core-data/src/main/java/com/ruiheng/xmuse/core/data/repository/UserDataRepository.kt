package com.ruiheng.xmuse.core.data.repository

import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.model.data.UserThirdParty
import kotlinx.coroutines.flow.Flow

interface UserDataRepository {

    enum class ThirdPartyLoginType(val requestType: Int, val thirdPartyAccountTitle: String) {
        WeChat(1, "微信号"),
        QQ(2, "QQ号"),
        Sina(6, "新浪")
    }

    val userData: Flow<UserData?>
    val userListData: Flow<List<UserData>?>
    val thirdPartyBondList: Flow<List<UserThirdParty>>
    fun insertUser(userData: UserData): Flow<Result<UserData>>
}