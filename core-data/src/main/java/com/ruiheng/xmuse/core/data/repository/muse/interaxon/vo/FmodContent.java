// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

public final class FmodContent {


    /*package*/ final String soundscapeUniqueId;

    /*package*/ final String journeyUniqueId;

    /*package*/ final String absoluteDirectoryPath;

    /*package*/ final String bankFilename;

    /*package*/ final String stringsBankFilename;

    /*package*/ final String pureDataFilename;

    /*package*/ final FmodConfig config;

    public FmodContent(
            String soundscapeUniqueId,
            String journeyUniqueId,
            String absoluteDirectoryPath,
            String bankFilename,
            String stringsBankFilename,
            String pureDataFilename,
            FmodConfig config) {
        this.soundscapeUniqueId = soundscapeUniqueId;
        this.journeyUniqueId = journeyUniqueId;
        this.absoluteDirectoryPath = absoluteDirectoryPath;
        this.bankFilename = bankFilename;
        this.stringsBankFilename = stringsBankFilename;
        this.pureDataFilename = pureDataFilename;
        this.config = config;
    }

    public String getSoundscapeUniqueId() {
        return soundscapeUniqueId;
    }

    public String getJourneyUniqueId() {
        return journeyUniqueId;
    }

    public String getAbsoluteDirectoryPath() {
        return absoluteDirectoryPath;
    }

    /** if null, then bundled with app */
    public String getBankFilename() {
        return bankFilename;
    }

    public String getStringsBankFilename() {
        return stringsBankFilename;
    }

    public String getPureDataFilename() {
        return pureDataFilename;
    }

    public FmodConfig getConfig() {
        return config;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "FmodContent{" +
                "soundscapeUniqueId=" + soundscapeUniqueId +
                "," + "journeyUniqueId=" + journeyUniqueId +
                "," + "absoluteDirectoryPath=" + absoluteDirectoryPath +
                "," + "bankFilename=" + bankFilename +
                "," + "stringsBankFilename=" + stringsBankFilename +
                "," + "pureDataFilename=" + pureDataFilename +
                "," + "config=" + config +
        "}";
    }

}
