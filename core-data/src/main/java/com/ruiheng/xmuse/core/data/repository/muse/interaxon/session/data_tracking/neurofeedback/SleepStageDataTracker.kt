package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.choosemuse.libmuse.internal.Classifier
import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.TfliteModelConsts
import com.choosemuse.libmuse.internal.TimestampedData
import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteSleepStageClassifierModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepStage
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionConsts
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepStagesUserSession
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SleepStageDataTracker @Inject constructor(
    private val tfliteFactory: TfliteFactory
//    val sleepStageClassifier: TfliteSleepStageClassifierModel
) {
    val sleepStageClassifier: TfliteSleepStageClassifierModel by lazy {
        TfliteSleepStageClassifierModel.DSN
    }

    companion object {
        val FALLBACK_INITIAL_SLEEP_STAGE = SleepStage.AWAKE.ordinal.toDouble()

        fun extractSleepStagesUserSession(
            timeseries: ArrayList<Double>,
            completedSeconds: Long
        ): SleepStagesUserSession {
            return MuseDataUtils.calcSecondsPerRange(
                timeseries, completedSeconds,
                arrayListOf(
                    SessionConsts.SLEEP_STAGES_MIN,
                    SessionConsts.SLEEP_STAGES_DEEP,
                    SessionConsts.SLEEP_STAGES_LIGHT,
                    SessionConsts.SLEEP_STAGES_REM,
                    SessionConsts.SLEEP_STAGES_MAX
                )
            ).let { seconds ->
                val percentages = MuseDataUtils.calcPercentages(seconds)
                SleepStagesUserSession(
                    seconds[SleepStage.AWAKE.ordinal].toInt(),
                    seconds[SleepStage.REM.ordinal].toInt(),
                    seconds[SleepStage.LIGHT.ordinal].toInt(),
                    seconds[SleepStage.DEEP.ordinal].toInt(),
                    percentages[SleepStage.DEEP.ordinal].toInt()
                )
            }
        }
    }

    data class SleepStageData(
        val timeseries: List<Float>,
        val stats: SleepStagesUserSession,
        val sleepScore: Int?
    )

    private var sleepStagesPredictions: TimestampedData? = null

    // Thread safe
    private val sleepStagesEegBuffer: DataWindowBuffer by lazy {
        tfliteFactory.createSleepStageEegBuffer(sleepStageClassifier)
    }

    private val sleepStagesClassifier: Classifier by lazy {
        tfliteFactory.createSleepStagesClassifier(sleepStageClassifier)
    }

    private var disposeable: Disposable? = null

    fun startTracking(inputData: Observable<Pair<Float, List<Float>>>) {
        inputData.subscribe { (elapsedTime, data) ->
            sleepStagesEegBuffer.updateBuffer(elapsedTime, ArrayList(data))
        }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun process(
        now: Float,
        connectTimes: List<Float>,
        disconnectTimes: List<Float>,
        completedSeconds: Long
    ): SleepStageData? {
        val sleepStageEegWindows = sleepStagesEegBuffer.flushWindows()

        sleepStagesPredictions = sleepStagesEegBuffer.let { _ ->
            if (sleepStageClassifier.requiresPreprocessedFft) {
//                NanInterpolateUtils.processNanInterpolate(
//                    sleepStageEegWindows,
//                    TfliteModelConsts.EEG_LABEL,
//                    TfliteModelConsts.EEG_LABEL
//                )
//                FftUtils.processFft(
//                    sleepStageEegWindows,
//                    TfliteModelConsts.EEG_LABEL,
//                    TfliteModelConsts.FFT_SQC_LABEL
//                )
            }

            sleepStagesClassifier.processData(sleepStageEegWindows)?.let {
                sleepStagesPredictions?.append(it) ?: it
            } ?: sleepStagesPredictions
        }
        Timber.d("Automatic:SleepStage:${sleepStagesPredictions?.toString()}")

        return sleepStagesPredictions?.let { data ->
            val sleepStages =
                tfliteFactory.createPresenter(TflitePresenterModel.SLEEP_STAGE_GRAPH)
                    .setNowTimestamp(now)
                    .setTimestampedData(data)
                    .setReconnectTimestamps(ArrayList(connectTimes))
                    .setDisconnectTimestamps(ArrayList(disconnectTimes))
                    .processData()
            Timber.d("Automatic:SleepStageProcess-Presenter: ${sleepStages}")

            val sleepScore = tfliteFactory.createPresenter(TflitePresenterModel.SLEEP_SCORE)
                .setTimestampedData(data)
                .setReconnectTimestamps(ArrayList(connectTimes))
                .setDisconnectTimestamps(ArrayList(disconnectTimes))
                .processData()
                .firstOrNull()
                ?.toInt()
            Timber.d("Automatic:SleepStageProcess-Score:${sleepScore}")
            SleepStageData(
                timeseries = sleepStages,
                stats = extractSleepStagesUserSession(
                    DataTrackingUtils.createGaplessTimeseries(
                        FALLBACK_INITIAL_SLEEP_STAGE,
                        ArrayList(sleepStages.map(Float::toDouble))
                    ),
                    completedSeconds
                ),
                sleepScore = sleepScore
            )
        }
    }

}