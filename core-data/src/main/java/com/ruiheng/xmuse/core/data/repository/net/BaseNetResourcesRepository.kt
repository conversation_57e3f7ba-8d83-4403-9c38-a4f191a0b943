@file:OptIn(ExperimentalTypeInference::class)

package com.ruiheng.xmuse.core.data.repository.net

import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.catch
import kotlin.experimental.ExperimentalTypeInference


public suspend fun <T, D> FlowCollector<Result<D>>.requestCatch(
    result: ApiRequestResult<T>,
    dataResponse: suspend (T?) -> D? = { request ->
        request as D
    },
    successCallback: suspend (D) -> Unit,
    onErrorCallback: suspend ((Throwable) -> Unit) = {}
) {
    if (result.code != 200) {
        val exception = PetivityThrowable(message = result.msg)
        onErrorCallback.invoke(exception)
        throw PetivityThrowable(message = result.msg)
    }

    //TODO 提示语
    val data = result.data
    val resultData = dataResponse.invoke(data) ?: throw PetivityThrowable()
    successCallback.invoke(resultData)
}

public fun <T> Flow<Result<T>>.globalCatch() = catch { e ->
    e.printStackTrace()
    emit(Result.Error(e))
}

public fun <T> ApiRequestResult<T>.toResult(): Result<T?> {
    if (code != 200) {
        val exception = PetivityThrowable(message = msg)
        return Result.Error(exception)
    }
    return Result.Success(data)
}
