//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse
//
//import androidx.lifecycle.MutableLiveData
//import com.blankj.utilcode.util.Utils
//import com.choosemuse.libmuse.ConnectionState
//import com.choosemuse.libmuse.Muse
//import com.choosemuse.libmuse.MuseListener
//import com.choosemuse.libmuse.MuseManagerAndroid
//import com.choosemuse.libmuse.MuseModel
//import com.choosemuse.libmuse.internal.BusymindPacket
//import com.ruiheng.xmuse.core.common.result.Result
//import com.ruiheng.xmuse.core.data.postLiveDataChange
//import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
//import io.reactivex.rxjava3.core.Observable
//import io.reactivex.rxjava3.subjects.BehaviorSubject
//import javax.inject.Singleton
//
//@Singleton
//class MuseDeviceSelector {
//    private val museAndroidManager by lazy {
//        MuseManagerAndroid.getInstance()
//    }
//
//    private val _discoveredLeMuses =
//        BehaviorSubject.createDefault<Result<List<Muse>>>(Result.Loading())
//
//    private var selectedMuse: Muse? = null
//
//    fun getSelectedMuse() = selectedMuse
//
//    fun isStreamingData() = selectedMuse?.connectionState == ConnectionState.CONNECTED
//
//    fun isNeedingUpdate() = selectedMuse?.connectionState == ConnectionState.NEEDS_UPDATE
//
//    val discoveredLeMuses = mutableListOf<Muse>()
//
//    var savedMuseMacAddress: String? = null
//
//
//    private val museSelectedListeners = mutableListOf<MuseSelectedListener>()
//
//    private val museDeviceListener = object : MuseListener() {
//        override fun museListChanged() {
//            val museList = museAndroidManager.muses
//            _discoveredLeMuses.onNext(Result.Loading(museList))
//        }
//    }
//
//    init {
//        museAndroidManager.setContext(Utils.getApp())
//        museAndroidManager.setMuseListener(museDeviceListener)
//    }
//
//    fun registerMuseSelectedListener(listener: MuseSelectedListener) {
//        museAndroidManager.startListening()
//        museSelectedListeners.add(listener)
//    }
//
//    fun observerDiscovererMuse(): Observable<Result<List<Muse>>> = _discoveredLeMuses
//
//    fun setSavedMuse(macString: String, museModel: MuseModel) {
//        savedMuseMacAddress = macString
//    }
//
//    fun turnOnMuseScan() {
//        museAndroidManager.stopListening()
//        museAndroidManager.startListening()
//    }
//
//    fun turnOffMuseScan() {
//        museAndroidManager.stopListening()
//    }
//
//    fun setAutomaticallyConnectToSavedMuse(enable: Boolean) {
//
//    }
//
//    fun setDisconnectMuseOnAppBackground(disconnect: Boolean) {
//
//    }
//
//    fun disconnectSelectedMuse() {
//        val selectedConnectState = selectedMuse?.connectionState
//        when (selectedConnectState) {
//            ConnectionState.CONNECTED, ConnectionState.CONNECTING, ConnectionState.NEEDS_UPDATE -> {
//                selectedMuse?.unregisterAllListeners()
//                selectedMuse?.disconnect()
//            }
//
//            else -> {
//
//            }
//        }
//    }
//
//}