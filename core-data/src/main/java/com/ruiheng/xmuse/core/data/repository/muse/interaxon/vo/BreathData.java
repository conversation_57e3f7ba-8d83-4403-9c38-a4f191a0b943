// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from session.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;

public final class BreathData {


    /*package*/ final ArrayList<Double> timeseries;

    /*package*/ final int lowBreathSyncSeconds;

    /*package*/ final int mediumBreathSyncSeconds;

    /*package*/ final int highBreathSyncSeconds;

    /*package*/ final int highBreathSyncPercentage;

    public BreathData(
            ArrayList<Double> timeseries,
            int lowBreathSyncSeconds,
            int mediumBreathSyncSeconds,
            int highBreathSyncSeconds,
            int highBreathSyncPercentage) {
        this.timeseries = timeseries;
        this.lowBreathSyncSeconds = lowBreathSyncSeconds;
        this.mediumBreathSyncSeconds = mediumBreathSyncSeconds;
        this.highBreathSyncSeconds = highBreathSyncSeconds;
        this.highBreathSyncPercentage = highBreathSyncPercentage;
    }

    public ArrayList<Double> getTimeseries() {
        return timeseries;
    }

    public int getLowBreathSyncSeconds() {
        return lowBreathSyncSeconds;
    }

    public int getMediumBreathSyncSeconds() {
        return mediumBreathSyncSeconds;
    }

    public int getHighBreathSyncSeconds() {
        return highBreathSyncSeconds;
    }

    public int getHighBreathSyncPercentage() {
        return highBreathSyncPercentage;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "BreathData{" +
                "timeseries=" + timeseries +
                "," + "lowBreathSyncSeconds=" + lowBreathSyncSeconds +
                "," + "mediumBreathSyncSeconds=" + mediumBreathSyncSeconds +
                "," + "highBreathSyncSeconds=" + highBreathSyncSeconds +
                "," + "highBreathSyncPercentage=" + highBreathSyncPercentage +
        "}";
    }

}
