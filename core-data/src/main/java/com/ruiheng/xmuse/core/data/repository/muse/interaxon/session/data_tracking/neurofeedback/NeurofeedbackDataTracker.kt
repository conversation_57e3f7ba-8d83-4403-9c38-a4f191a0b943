package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.ResultsMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionBusymindDataMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.PresleepUserSession
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.SessionUpdate
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.UserSessionTimeSeries
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.MuseTimestampedDataMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.SleepPositionDataTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.schedulers.Schedulers
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class NeurofeedbackDataTracker(
    private val museTimestampedDataMonitor: MuseTimestampedDataMonitor,
    private val busymindDataMonitor: SessionBusymindDataMonitor,
    tfliteFactory: TfliteFactory,
    validatedSleepStagingEnabled: Boolean,
    useLegacyDsiModel: Boolean,
    private val alphaPeakEnabled: Boolean,
    private val alphaPowerEnabled: Boolean,
    resultsMode: ResultsMode?
) {

    @Inject
    constructor(
        museTimestampedDataMonitor: MuseTimestampedDataMonitor,
        busymindDataMonitor: SessionBusymindDataMonitor,
        tfliteFactory: TfliteFactory,
//        featureFlags: PlatformFeatureFlags,
//        config: DataTrackingConfig
    ) : this(
        museTimestampedDataMonitor,
        busymindDataMonitor,
        tfliteFactory,
        false,
        false,
//        featureFlags.isFeatureFlagEnabled(
//            FEATURE_FLAG_VALIDATED_SLEEP_STAGING_ENABLED,
//            false
//        ),
//        featureFlags.isFeatureFlagEnabled(
//            FEATURE_FLAG_USE_LEGACY_DEEP_SLEEP_INTENSITY,
//            false
//        ),
        SessionManager.dataTrackingConfig?.alphaPeakEnabled ?: false,
        SessionManager.dataTrackingConfig?.alphaPowerEnabled ?: false,
        SessionManager.dataTrackingConfig?.resultsMode
    )

    companion object {
        private val SLEEP_BUSYMIND_DATA = BusymindData.entries.filter {
            when (it) {
                BusymindData.ALPHA, BusymindData.BETA, BusymindData.DELTA,
                BusymindData.GAMMA, BusymindData.THETA, BusymindData.BODY,
                BusymindData.HEART -> true

                BusymindData.MIND, BusymindData.BREATH, BusymindData.FOX_SCORE,
                BusymindData.HBO_LEFT, BusymindData.HBO_RIGHT, BusymindData.HBR_LEFT, BusymindData.HBR_RIGHT,
                BusymindData.PPG_HRV_RMSSD, BusymindData.PPG_HRV_HF, BusymindData.PPG_HRV_LF, BusymindData.PPG_HRV_LFvsHF, BusymindData.PPG_HRV_SDNN, BusymindData.PPG_HRV_PNN50 -> false
            }
        }
        private val MEDITATE_BUSYMIND_DATA = BusymindData.entries
    }

    private val busymindTimeseriesDataTrackers =
        when (resultsMode) {
            ResultsMode.SLEEP -> SLEEP_BUSYMIND_DATA
            else -> MEDITATE_BUSYMIND_DATA
        }.associateWith { busymindData ->
            BusymindTimeseriesDataTracker(
                tfliteFactory = tfliteFactory,
                presenterModel = busymindData.getPresenterModel()
            )
        }
    private val sleepPositionDataTracker = when (resultsMode) {
        ResultsMode.SLEEP -> SleepPositionDataTracker(tfliteFactory)
        else -> null
    }

    private val sleepStageDataTracker = when (resultsMode) {
        ResultsMode.SLEEP -> SleepStageDataTracker(
            tfliteFactory
//            , if (validatedSleepStagingEnabled) {
//                TfliteSleepStageClassifierModel.VALIDATED
//            } else {
//                TfliteSleepStageClassifierModel.COMBINED_FFT_SQC
//            }
        )

        else -> null
    }

    private val deepSleepIntensityDataTracker = when (resultsMode) {
        ResultsMode.SLEEP -> DeepSleepIntensityDataTracker(tfliteFactory, useLegacyDsiModel)
        else -> null
    }

//    private val alphaPeakDataTracker: AlphaPeakDataTracker? =
//        if (alphaPeakEnabled || alphaPowerEnabled) {
//            AlphaPeakDataTracker(
//                SessionAlphaDetector.create()
//            )
//        } else {
//            null
//        }

    private var tracking = false

    fun setupInitialConditions() {
        museTimestampedDataMonitor.setStartTime()
    }

    fun startTracking() {
        if (tracking) {
            return
        }

        tracking = true

        val eegObservable =
            museTimestampedDataMonitor.timestampedEeg.map {
                Pair(
                    it.first,
                    it.second.map(Double::toFloat)
                )
            }
        sleepStageDataTracker?.startTracking(eegObservable)
        deepSleepIntensityDataTracker?.startTracking(eegObservable)

        sleepPositionDataTracker?.startTracking(
            museTimestampedDataMonitor.timestampedAcc.map {
                Pair(
                    it.first,
                    it.second.map(Double::toFloat)
                )
            }
        )
//        alphaPeakDataTracker?.startTracking(
//            museTimestampedDataMonitor.timestampedEeg
//                .map { it.second }
//                .observeOn(Schedulers.computation())
//        )

        busymindTimeseriesDataTrackers.forEach { (busymindData, tracker) ->
            tracker.startTracking(
                busymindDataMonitor.busymindData
                    .filter { it.containsKey(busymindData) }
                    .map { it[busymindData]!! }
            )
        }
        museTimestampedDataMonitor.startTracking()
    }

    fun stopTracking() {
        busymindTimeseriesDataTrackers.forEach { it.value.stopTracking() }
        sleepPositionDataTracker?.stopTracking()
        sleepStageDataTracker?.stopTracking()
        deepSleepIntensityDataTracker?.stopTracking()
        museTimestampedDataMonitor.stopTracking()
//        alphaPeakDataTracker?.stopTracking()
        tracking = false
    }

    fun startNewDataGap() {
        museTimestampedDataMonitor.pause()
        busymindDataMonitor.setGapEnabled(true)
    }

    fun endDataGap() {
        museTimestampedDataMonitor.resume()
        busymindDataMonitor.setGapEnabled(false)
    }

    fun createProcessSleepDataObservable(
        session: SessionUpdate,
        disableSleepBusymindDataUpdates: Boolean
    ): Single<SessionUpdate> {
        return Single.fromCallable {
            Timber.d("Automatic:createProcessSleepDataObservable")
            val connectTimes = museTimestampedDataMonitor.getGapEndTimes()
            val disconnectTimes = museTimestampedDataMonitor.getGapStartTimes()
            val now = museTimestampedDataMonitor.getTimeSinceStart()
//
            Timber.d("Automatic:createProcessSleepDataObservable:${connectTimes.size}:${now}")

            processSleepData(
                session = session,
                connectTimes = connectTimes,
                disconnectTimes = disconnectTimes,
                now = now
            )
            if (!disableSleepBusymindDataUpdates) {
                processBusymindData(
                    connectTimes = connectTimes,
                    disconnectTimes = disconnectTimes,
                    session = session
                )
            }
//                if (!disableSleepBusymindDataUpdates) {
//                    processBusymindData(
//                        connectTimes = connectTimes,
//                        disconnectTimes = disconnectTimes,
//                        session = session
//                    )
//                }
//            }
            session
        }
    }

//    fun createProcessMeditateDataObservable(
//        session: SessionUpdate,
//        historicalAlphaPowerDescending: Observable<List<Double>>
//    ): Single<SessionUpdate> {
//        val trace = Firebase.performance.newTrace("processMeditateData").apply {
//            putAttribute(
//                "meditateBodyPresenter",
//                busymindTimeseriesDataTrackers[BusymindData.BODY]?.presenterModel.toString()
//            )
//            putAttribute(
//                "meditateBreathPresenter",
//                busymindTimeseriesDataTrackers[BusymindData.BREATH]?.presenterModel.toString()
//            )
//            putAttribute(
//                "meditateHeartPresenter",
//                busymindTimeseriesDataTrackers[BusymindData.HEART]?.presenterModel.toString()
//            )
//            putAttribute(
//                "meditateMindPresenter",
//                busymindTimeseriesDataTrackers[BusymindData.MIND]?.presenterModel.toString()
//            )
//        }
//        return Single.fromCallable {
//            processBusymindData(
//                connectTimes = museTimestampedDataMonitor.getGapEndTimes(),
//                disconnectTimes = museTimestampedDataMonitor.getGapStartTimes(),
//                session = session
//            )
//            session
//        }.flatMap {
//            if (alphaPeakDataTracker != null) {
//                historicalAlphaPowerDescending
//                    .take(1)
//                    .map {
//                        if (alphaPeakEnabled) {
//                            setAlphaPeak(session, alphaPeakDataTracker.alphaPeakHz)
//                        }
//                        if (alphaPowerEnabled) {
//                            setAlphaPower(session, alphaPeakDataTracker.getAlphaPower(it))
//                        }
//                        session
//                    }.singleOrError()
//            } else {
//                Single.just(session)
//            }
//        }.doOnSubscribe {
//            trace.start()
//        }.doOnSuccess {
//            trace.stop()
//        }
//    }

    private fun processSleepData(
        session: SessionUpdate,
        connectTimes: ArrayList<Float>,
        disconnectTimes: ArrayList<Float>,
        now: Float
    ) {
        sleepPositionDataTracker?.process(
            now,
            connectTimes,
            disconnectTimes,
            0L
//            session.completedSeconds.toLong()
        )?.let {
            getTimeSeries(session).sleepPositions = it.timeseries.toTypedArray()
//                RealmList(*it.timeseries.map(Float::toDouble).toTypedArray())
            //TODO need to confirm what is sleepPositionsDownSampled
//            getTimeSeries(session).sleepPositionsDownSampled =
//                session.timeSeries!!.sleepPositions
            session.sleepPositions = it.stats
        }

        sleepStageDataTracker?.process(
            now,
            connectTimes,
            disconnectTimes,
            0L
//            session.completedSeconds.toLong()
        )?.let {
            getTimeSeries(session).sleepStages = it.timeseries.toTypedArray()
//                RealmList(*it.timeseries.map(Float::toDouble).toTypedArray())
//            getTimeSeries(session).sleepStagesDownSampled = session.timeSeries!!.sleepStages
            session.sleepStages = it.stats
            getPresleep(session).sleepScore = it.sleepScore
        }

//        deepSleepIntensityDataTracker?.process(now, connectTimes, disconnectTimes)?.let {
//            getTimeSeries(session).deepSleepIntensity =
//                RealmList(*it.timeseries.map(Float::toDouble).toTypedArray())
//            getTimeSeries(session).deepSleepIntensityDownSampled =
//                session.timeSeries!!.deepSleepIntensity
//            getPresleep(session).deepSleepIntensityPoints = it.points
//        }
    }

    private fun getTimeSeries(session: SessionUpdate): UserSessionTimeSeries {
        if (session.timeSeries == null) {
            session.timeSeries = UserSessionTimeSeries()
        }
        return session.timeSeries!!
    }

    //
    private fun getPresleep(session: SessionUpdate): PresleepUserSession {
        if (session.presleep == null) {
            session.presleep = PresleepUserSession()
        }
        return session.presleep!!
    }

    private fun setAlphaPeak(session: SessionUpdate, alphaPeakHz: Double?) {
//        if (session.alphaPeak == null) {
//            session.alphaPeak = AlphaPeakUserSession()
//        }
//        session.alphaPeak?.alphaPeakHz = alphaPeakHz
    }

//    private fun setAlphaPower(session: SessionUpdate, alphaPower: AlphaPower) {
//        if (session.alphaPower == null) {
//            session.alphaPower = AlphaPowerUserSession()
//        }
//        session.alphaPower?.apply {
//            this.alphaPower = alphaPower.alphaPower
//            remainingCalibrationSessions = alphaPower.remainingCalibrationSessions
//            result = when (val ordinal = alphaPower.resultOrdinal) {
//                SessionAlphaDetector.NO_READING_ORDINAL -> Result.NO_READING.parsableValue
//                SessionAlphaDetector.CALIBRATING_ORDINAL -> Result.CALIBRATING.parsableValue
//                SessionAlphaDetector.FAIR_ORDINAL -> Result.FAIR.parsableValue
//                SessionAlphaDetector.GOOD_ORDINAL -> Result.GOOD.parsableValue
//                SessionAlphaDetector.GREAT_ORDINAL -> Result.GREAT.parsableValue
//                SessionAlphaDetector.OPTIMAL_ORDINAL -> Result.OPTIMAL.parsableValue
//                else -> {
//                    logNonFatal(
//                        RuntimeException("unknown result ordinal"),
//                        mapOf("ordinal" to ordinal.toString())
//                    )
//                    Result.NO_READING.parsableValue
//                }
//            }
//            lowAlphaPower = alphaPower.lowAlphaPower
//            highAlphaPower = alphaPower.highAlphaPower
//            alphaPowerNormalized = alphaPower.alphaPowerNormalized
//            lowAlphaPowerNormalized = alphaPower.lowAlphaPowerNormalized
//            highAlphaPowerNormalized = alphaPower.highAlphaPowerNormalized
//        }
//    }

    private fun processBusymindData(
        connectTimes: List<Float>,
        disconnectTimes: List<Float>,
        session: SessionUpdate
    ) {
        busymindTimeseriesDataTrackers.forEach { (busymindData, tracker) ->
            val data = tracker.process(connectTimes, disconnectTimes).toTypedArray()

            when (busymindData) {
                BusymindData.BODY -> {
                    session.timeSeries!!.bodyMovementDownSampled = data
                }

                BusymindData.ALPHA -> {}
                BusymindData.BETA -> {}
                BusymindData.DELTA -> {}
                BusymindData.GAMMA -> {}
                BusymindData.THETA -> {}
                BusymindData.MIND -> {}
                BusymindData.HEART -> {
                    session.timeSeries!!.heartRateDownSampled = data
                }

                BusymindData.BREATH -> {}
                BusymindData.FOX_SCORE -> {}
                BusymindData.HBO_LEFT -> {}
                BusymindData.HBO_RIGHT -> {}
                BusymindData.HBR_LEFT -> {}
                BusymindData.HBR_RIGHT -> {}
                else -> {}
            }
        }
//        busymindTimeseriesDataTrackers.forEach { (busymindData, tracker) ->
//            val data = RealmList(
//                *tracker.process(connectTimes, disconnectTimes).map(Float::toDouble).toTypedArray()
//            )
//            when (busymindData) {
//                BusymindData.ALPHA -> session.timeSeries!!.alpha = data
//                BusymindData.BETA -> session.timeSeries!!.beta = data
//                BusymindData.DELTA -> session.timeSeries!!.delta = data
//                BusymindData.GAMMA -> session.timeSeries!!.gamma = data
//                BusymindData.THETA -> session.timeSeries!!.theta = data
//                BusymindData.MIND -> session.timeSeries!!.mindCalmDownSampled = data
//                BusymindData.BODY -> session.timeSeries!!.bodyMovementDownSampled = data
//                BusymindData.HEART -> session.timeSeries!!.heartRateDownSampled = data
//                BusymindData.BREATH -> session.timeSeries!!.breathSyncDownSampled = data
//                BusymindData.FOX_SCORE -> {
//                    session.timeSeries!!.frontalOxygenation = data
//                    session.frontalOxygenation =
//                        BusymindTimeseriesDataTracker.extractFrontalOxygenationUserSession(
//                            ArrayList(data), session.completedSeconds.toLong()
//                        )
//                }
//
//                BusymindData.HBO_LEFT -> session.timeSeries!!.hboLeft = data
//                BusymindData.HBO_RIGHT -> session.timeSeries!!.hboRight = data
//                BusymindData.HBR_LEFT -> session.timeSeries!!.hbrLeft = data
//                BusymindData.HBR_RIGHT -> session.timeSeries!!.hbrRight = data
//            }
//        }
    }

}
