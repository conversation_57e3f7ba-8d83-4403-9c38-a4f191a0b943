package com.ruiheng.xmuse.core.data

import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.ThreadUtils

public inline fun <reified T> postLiveDataChange(
    appExecutors: AppExecutors,
    liveData: MutableLiveData<T>,
    data: T,
    changeThread: Boolean = true
) {
    if (ThreadUtils.isMainThread()) {
        liveData.value = data
    } else {
        if (changeThread) {
            appExecutors.mainThread().execute {
                liveData.value = data
            }
        } else {
            liveData.postValue(data)
        }
    }
}