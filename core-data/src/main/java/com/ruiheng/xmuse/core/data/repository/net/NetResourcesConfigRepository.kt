package com.ruiheng.xmuse.core.data.repository.net

import android.util.Log
import androidx.annotation.WorkerThread
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import com.ruiheng.xmuse.core.network.other.ConfigService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 配置相关的网络资源仓库
 * 负责获取远程配置信息
 */
@Singleton
class NetResourcesConfigRepository @Inject constructor(
    private val configService: ConfigService
) {

    /**
     * 获取Xmuse域名配置
     */
    @WorkerThread
    fun getXmuseDomainConfig() = flow<Result<KvConfigItem>> {
        Log.d("ConfigRepository", "开始调用配置接口...")
        emit(Result.Loading())
        try {
            val response = configService.getXmuseDomainConfig()
            Log.d("ConfigRepository", "配置接口响应: $response")

            // 处理配置API的特殊响应格式
            if (response.code == 20000 && response.data != null) {
                Log.d("ConfigRepository", "配置解析成功: ${response.data}")
                emit(Result.Success(response.data!!))
            } else {
                Log.e("ConfigRepository", "配置接口返回错误: code=${response.code}, message=${response.message}")
                emit(Result.Error(PetivityThrowable(response.message ?: "获取配置失败")))
            }
        } catch (e: Exception) {
            Log.e("ConfigRepository", "配置接口调用失败", e)
            emit(Result.Error(PetivityThrowable(e.message ?: "网络请求失败")))
        }
    }.globalCatch().flowOn(Dispatchers.IO)
}
