package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.SessionDataTracker
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import javax.inject.Inject
import javax.inject.Singleton

@ActivityRetainedScoped
class MuseDeadBatteryMonitor(
    private val museConnector: MuseConnector,
    private val museBatteryPercentageMonitor: MuseBatteryPercentageMonitor,
    private val trackingState: Observable<SessionDataTracker.TrackingState>
) {
    @Inject
    constructor(
        museConnector: MuseConnector,
        museBatteryPercentageMonitor: MuseBatteryPercentageMonitor,
        dataTracker: SessionDataTracker
    ) : this(museConnector, museBatteryPercentageMonitor, dataTracker.dataTrackingState)

    companion object {
        const val BATTERY_DEATH_PERCENT = 6
    }

    private val disposableBag = CompositeDisposable()

    val deathFromBattery = BehaviorSubject.createDefault(false)

    fun startMonitoring() {
        disposableBag.addAll(
            trackingState.subscribe {
                if (it == SessionDataTracker.TrackingState.ENDED) {
                    val battery = museBatteryPercentageMonitor.batteryPercentage.value
                    val streaming = museConnector.isStreamingData()
                    if (battery != null && battery <= BATTERY_DEATH_PERCENT && !streaming) {
                        deathFromBattery.onNext(true)
                    }
                }
            }
        )
    }

    fun stopMonitoring() {
        disposableBag.clear()
    }
}