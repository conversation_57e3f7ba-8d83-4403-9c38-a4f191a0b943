package com.ruiheng.xmuse.core.data.repository

import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.database.dao.UserDao
import com.ruiheng.xmuse.core.database.model.asExternalModel
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.model.data.UserThirdParty
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

internal class OfflineUserRepository @Inject constructor(
    private val userDao: UserDao
) : UserDataRepository {
    override val userData: Flow<UserData?> = userDao.getUser().map { it?.asExternalModel() }
    override val userListData: Flow<List<UserData>?> =
        userDao.getUsers().map { list -> list?.map { it.asExternalModel() } }

    override val thirdPartyBondList = userDao.getThirdPartyUser().map { thirdPartyEntityList ->
        val thirdPartyLogin = UserDataRepository.ThirdPartyLoginType.entries.map { enum ->
            thirdPartyEntityList?.find { it.thirdType == enum.requestType }?.asExternalModel()
                ?: UserThirdParty(thirdType = enum.requestType)
        }
        thirdPartyLogin
    }

    override fun insertUser(userData: UserData) = flow<Result<UserData>> {
        emit(Result.Loading())
//        userDao.insertUserAndRemove(userData.asFtsEntity())
        emit(Result.Success(userData))
    }.catch { e ->
        emit(Result.Error(e))
    }
}