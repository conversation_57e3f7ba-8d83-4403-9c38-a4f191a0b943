//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session
//
//import android.content.Context
//import android.content.Intent
//import android.util.Log
//import com.choosemuse.libmuse.Muse
//import com.choosemuse.libmuse.MuseManager
//import com.choosemuse.libmuse.internal.MuseSessionType
//import com.choosemuse.libmuse.internal.SessionArgs
//import javax.inject.Inject
//import javax.inject.Singleton
//
//@Singleton
//class SessionManager
//@Inject constructor(
////    private val sessionComponentFactory: SessionComponentFactory,
//    private val context: Context,
////    private val userManager: UserManager,
////    private val museManager: MuseManager,
////    private val museConnector: MuseConnector
//) {
//
//    companion object {
//        const val SESSION_ID_CRASHLYTICS_KEY = "sessionID"
//    }
//
////    private var sessionComponent: SessionComponent? = null
//
//    val sessionId: String?
//        get() = dataTrackingConfig?.sessionId
//
//    val dataTracker: SessionDataTracker
//        get() = sessionComponent!!.dataTracker
//
//    val durationTracker: SessionDurationTracker
//        get() = sessionComponent!!.durationTracker
//
//    val nfbAudioPlayer: NfbAudioPlayer
//        get() = sessionComponent!!.nfbAudioPlayer
//
//    val nfbMessageMonitor: NfbMessageMonitor
//        get() = sessionComponent!!.nfbMessageMonitor
//
//    val deadBatteryMonitor: MuseDeadBatteryMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.deadBatteryMonitor
//        } else {
//            null
//        }
//
//    val challengeCompletionMonitor: ChallengeCompletionMonitor?
//        get() = sessionComponent?.challengeCompletionMonitor
//
//    val sqcDisconnectionMonitor: MuseDisconnectionMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.sqcDisconnectionMonitor()
//        } else {
//            null
//        }
//
//    val meditationPlayerDisconnectionMonitor: MuseDisconnectionMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.meditationPlayerDisconnectionMonitor()
//        } else {
//            null
//        }
//
//    val badSignalMonitor: MuseBadSignalMonitor?
//        get() = if (dataTrackingConfig?.muse != null) {
//            sessionComponent!!.badSignalMonitor()
//        } else {
//            null
//        }
//
//    val busymindDataTracker: BusymindStatsDataTracker
//        get() = sessionComponent!!.busymindDataTracker
//
//    var dataTrackingConfig: DataTrackingConfig? = null
//
//    val storageSynchronizer: SessionJourneyUserStorageSynchronizer
//        get() = sessionComponent!!.storageSynchronizer
//
//    val alertRecoveryAnalytics: AlertRecoveryAnalytics
//        get() = sessionComponent!!.alertRecoveryAnalytics
//
//    val museDeviceSelector: MuseDeviceSelector
//        get() = museConnector.museSelector
//
//    val nfbProcessor: NfbProcessor?
//        get() = sessionComponent?.nfbProcessor
//
//    private var disposables: CompositeDisposable? = null
//
//    val sessionOngoing: Boolean
//        get() = sessionComponent != null
//
//    val webviewSessionAdapter: WebviewSessionAdapter?
//        get() = if (dataTrackingConfig?.isWebSession == true) {
//            sessionComponent?.webviewSessionAdapter
//        } else {
//            null
//        }
//
//    fun startSession(dataTrackingConfig: DataTrackingConfig, cppConfig: SessionConfig) {
//        if (sessionOngoing) {
//            Log.w("SessionManager", "Ignore startSession. Session is already running")
//            return
//        }
//
//        disposables = CompositeDisposable()
//        this.dataTrackingConfig = dataTrackingConfig
//        val legacySession = MuseApplication.getInstance().vmFactory.createSession(cppConfig)
//        legacySession.startSession()
//        MuseApplication.getInstance().vmFactory.setCurrentSession(legacySession)
//        val muse = if (dataTrackingConfig.muse == null) {
//            null
//        } else {
//            museConnector.museSelector.selectedMuse
//        }
//
//        sessionComponent = sessionComponentFactory.create(
//            muse, legacySession, userManager, dataTrackingConfig
//        ).apply {
//            setupMuseForSession(muse, this, dataTrackingConfig)
//
//            sessionFileWriter.startNewRecording()
//            oscMessageSender.startMonitoring()
//            signalAlertNotification.startMonitoringAlerts()
//            storageSynchronizer.startObservingStorage()
//
//            if (dataTrackingConfig.resultsMode == ResultsMode.MEDITATE) {
//                challengeCompletionMonitor.startMonitoring()
//            }
//        }
//        deadBatteryMonitor?.startMonitoring()
//        context.startService(Intent(context, SessionService::class.java))
//
//        with(userManager.remoteSessionSynchronizer) {
//            forestallUpload(dataTrackingConfig.utcTimestamp)
//            createSession(dataTrackingConfig.toUserSession())
//        }
//        logStartSession(dataTrackingConfig)
//        FirebaseCrashlytics.getInstance()
//            .setCustomKey(SESSION_ID_CRASHLYTICS_KEY, dataTrackingConfig.sessionId)
//        updateRecentsPlaylist(dataTrackingConfig)
//    }
//
//    private fun setupMuseForSession(
//        muse: Muse?,
//        session: SessionComponent,
//        dataTrackingConfig: DataTrackingConfig
//    ) {
//        if (muse != null) {
//            museManager.setBusymindSessionArgs(createBusymindSessionArgs(dataTrackingConfig))
//            session.packetLossCounter.start()
//            museManager.busymindMonitor.nfbPackets
//                .subscribe {
//                    session.dataStatusMonitor.receiveBusymindPacket(
//                        it,
//                        museConnector.museSelector.selectedMuse
//                    )
//                }.let { disposables?.add(it) }
//
//            session.museDisconnectionCounter.startCounting()
//        } else {
//            museManager.disableMuseUsage()
//        }
//    }
//
//    private fun createBusymindSessionArgs(dataTrackingConfig: DataTrackingConfig): SessionArgs {
//        val museSessionType = when (dataTrackingConfig.sessionType) {
//            SessionType.FRONTAL_OXYGENATION -> MuseSessionType.FRONTAL_OXYGENATION
//            SessionType.MIND -> MuseSessionType.MIND
//            SessionType.TIMED -> MuseSessionType.TIMED
//            SessionType.BODY -> MuseSessionType.BODY
//            SessionType.HEART -> MuseSessionType.HEART
//            SessionType.BREATH -> MuseSessionType.BREATH
//            SessionType.GUIDED -> MuseSessionType.GUIDED
//            SessionType.SLEEP -> MuseSessionType.SLEEP
//        }
//
//        return SessionArgs(
//            /* sessionType = */
//            museSessionType,
//            /* soundscapeContentId = */
//            dataTrackingConfig.sessionInfo!!.fmodContent?.config?.soundscapeContentId ?: "",
//            /* guidanceContentId = */
//            "",
//            /* sessionLength = */
//            dataTrackingConfig.sessionInfo.sessionLengthSeconds.toLong(),
//            /* guidanceLengthSecs = */
//            0.0,
//            /* fmodEvent = */
//            dataTrackingConfig.sessionInfo.fmodContent?.config?.fmodEvent ?: "",
//            /* nanChannelMirroringEnabled = */
//            museManager.museCharacteristicsManager.isCharacteristicsExperimentEnabled,
//            /* fmodMap = */
//            dataTrackingConfig.sessionInfo.fmodContent?.config?.fmodMap
//                ?: FmodMaps.getFmodParameterMap(
//                    dataTrackingConfig.sessionInfo.fmodContent?.config?.soundscapeContentId ?: ""
//                ),
//            /* tfFilePaths = */
//            HashMap()
//        )
//    }
//
//    private fun updateRecentsPlaylist(dataTrackingConfig: DataTrackingConfig) {
//        if (!MyLibraryExclusionRule.isVisibleInMyLibrary(dataTrackingConfig.tags)) {
//            return
//        }
//
//        val journeyId = dataTrackingConfig.content.journeyUniqueId
//        val meditationId = dataTrackingConfig.content.meditationUniqueId
//        val playlistContent = if (journeyId != null) {
//            PlaylistContent(journeyId, PlaylistContentType.JOURNEY)
//        } else if (meditationId != null) {
//            PlaylistContent(meditationId, PlaylistContentType.MEDITATION)
//        } else {
//            null
//        } ?: return
//
//        val playlist = when (dataTrackingConfig.resultsMode) {
//            ResultsMode.SLEEP -> Playlist.RECENT_SLEEP
//            ResultsMode.MEDITATE -> Playlist.RECENT_MEDITATE
//        }
//
//        userManager.playlists.createRecentsPlaylistEditor(playlist).add(playlistContent)
//    }
//
//    private fun logStartSession(config: DataTrackingConfig) = Analytics.instance.logStartSession(
//        config.content.legacyExerciseContentId ?: "",
//        config.content.legacySoundscapeContentId ?: "",
//        config.selectedSessionLengthSeconds ?: 0,
//        config.sessionType,
//        config.muse?.museModel
//    )
//
//    fun stopDataTracking(addExtraTime: Boolean) {
//        dataTracker.stopTracking(addExtraTime)
//        meditationPlayerDisconnectionMonitor?.stopMonitoring()
//        badSignalMonitor?.stopMonitoring()
//    }
//
//    fun endSession() {
//        if (!sessionOngoing) {
//            Log.w("SessionManager", "No session to end")
//            return
//        }
//
//        sessionComponent?.legacySession?.endSession()
//        MuseApplication.getInstance().vmFactory.setCurrentSession(null)
//
//        val disconnectionCount = sessionComponent?.museDisconnectionCounter?.stopCounting() ?: 0
//        val packetLossPercent = sessionComponent?.packetLossCounter?.packetLossPercent ?: 0.0
//        dataTrackingConfig?.let {
//            val realmSession = userManager.session.getSession(it.utcTimestamp)
//            logEndSession(it, realmSession, disconnectionCount, packetLossPercent)
//            with(userManager.remoteSessionSynchronizer) {
//                permitUpload(it.utcTimestamp)
//                refresh()
//            }
//        }
//        deadBatteryMonitor?.stopMonitoring()
//
//        if (dataTrackingConfig?.muse == null) {
//            museManager.enableMuseUsage()
//        }
//
//        sessionComponent?.let {
//            it.signalAlertNotification.stopMonitoringAlerts()
//            it.sessionFileWriter.closeRecording()
//            it.oscMessageSender.shutdown()
//            it.storageSynchronizer.stopObservingStorage()
//            it.challengeCompletionMonitor.stopMonitoring()
//            it.packetLossCounter.end()
//        }
//        sessionComponent = null
//        dataTrackingConfig = null
//        disposables?.clear()
//        disposables = null
//        context.stopService(Intent(context, SessionService::class.java))
//    }
//
//    private fun logEndSession(
//        config: DataTrackingConfig,
//        session: UserSession?,
//        disconnectionCount: Int,
//        packetLossPercent: Double
//    ) =
//        Analytics.instance.logEndSession(
//            config.utcTimestamp,
//            config.content.legacyExerciseContentId ?: "",
//            config.content.legacySoundscapeContentId ?: "",
//            session?.completedSeconds ?: 0,
//            config.selectedSessionLengthSeconds ?: 0,
//            session?.empty ?: true,
//            config.selectedSessionLengthSeconds == null ||
//                    (session?.completedSeconds ?: 0) < config.selectedSessionLengthSeconds,
//            config.muse?.museModel,
//            BuildConfig.VERSION_NAME,
//            config.sessionType,
//            disconnectionCount,
//            packetLossPercent,
//            when (dataTrackingConfig?.resultsMode) {
//                ResultsMode.MEDITATE -> {
//                    if (config.selectedSessionLengthSeconds != null) {
//                        (session?.completedSeconds ?: 0) < config.selectedSessionLengthSeconds
//                    } else {
//                        false
//                    }
//
//                }
//
//                ResultsMode.SLEEP -> false
//                else -> false
//            },
//            session?.alphaPeak?.alphaPeakHz,
//            session?.alphaPeak?.let { CognitivePerformance.create(it).result }
//        )
//}