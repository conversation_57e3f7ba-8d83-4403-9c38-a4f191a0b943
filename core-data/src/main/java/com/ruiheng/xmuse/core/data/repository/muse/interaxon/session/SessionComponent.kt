package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDeadBatteryMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.SessionPacketLossCounter
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.SessionDataTracker
import dagger.Component
import dagger.hilt.components.SingletonComponent

//@SessionScope
//@Component(
//    dependencies = [SingletonComponent::class],
//    modules = [SessionMuseModule::class, SessionModule::class]
//)
//interface SessionComponent {
//    val dataTracker: SessionDataTracker
//    val deadBatteryMonitor: MuseDeadBatteryMonitor
//    val packetLossCounter: SessionPacketLossCounter
//    val museDisconnectionCounter: SessionMuseDisconnectionCounter
//
//    @Component.Builder
//    interface Builder {
//        fun parentComponent(singletonComponent: SingletonComponent): Builder
//        fun build(): SessionComponent
//    }
//
//}