package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.*
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MuseDataObservableFactory @Inject constructor() {

    companion object {
        // Workaround for issue:
        // https://interaxon.atlassian.net/browse/COREAPP-10089
        private const val GLOBAL_REF_MAX = 40000
        private var GLOBAL_REF_COUNTER = 0
    }

    fun createDataObservable(
        muse: Muse,
        packetType: MuseDataPacketType
    ): Observable<MuseDataPacket> {
        return createDataObservable(muse, listOf(packetType))
    }

    fun createDataObservable(
        muse: Muse,
        packetTypes: List<MuseDataPacketType>
    ): Observable<MuseDataPacket> {
        return Observable.create { emitter ->
            var disposed = false
            val listener = object : MuseDataListener() {
                override fun receiveMuseDataPacket(packet: MuseDataPacket?, muse: Muse?) {
                    GLOBAL_REF_COUNTER++
                    if (GLOBAL_REF_COUNTER % GLOBAL_REF_MAX == 0) {
                        Runtime.getRuntime().gc()
                    }

                    packet ?: return
                    emitter.onNext(packet)
                }

                override fun receiveMuseArtifactPacket(
                    packet: MuseArtifactPacket?,
                    muse: Muse?
                ) {
                }
            }

            packetTypes.forEach { muse.registerDataListener(listener, it) }
            emitter.setDisposable(object : Disposable {
                override fun dispose() {
                    packetTypes.forEach { muse.unregisterDataListener(listener, it) }
                    disposed = true
                }

                override fun isDisposed() = disposed

            })
        }
    }

    fun createConnectionStateObservable(muse: Muse): Observable<ConnectionState> {
        return Observable.create { emitter ->
            var disposed = false
            val listener = object : MuseConnectionListener() {
                override fun receiveMuseConnectionPacket(
                    packet: MuseConnectionPacket?,
                    muse: Muse?
                ) {
                    val state = packet?.currentConnectionState ?: return
                    emitter.onNext(state)
                }
            }

            emitter.onNext(muse.connectionState)
            muse.registerConnectionListener(listener)
            emitter.setDisposable(object : Disposable {
                override fun dispose() {
                    muse.unregisterConnectionListener(listener)
                    disposed = true
                }

                override fun isDisposed() = disposed

            })
        }
    }
}