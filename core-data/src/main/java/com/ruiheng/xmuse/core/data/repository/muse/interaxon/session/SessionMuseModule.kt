//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session
//
//import com.choosemuse.libmuse.ConnectionState
//import com.choosemuse.libmuse.Muse
//import com.choosemuse.libmuse.MuseDataPacket
//import com.choosemuse.libmuse.MuseDataPacketType
//import com.choosemuse.libmuse.internal.BusymindPacket
//import com.choosemuse.libmuse.internal.MuseSessionType
//import com.choosemuse.libmuse.internal.PlatformMainOperationQueue
//import com.ruiheng.xmuse.core.data.repository.muse.MuseDeviceRepository
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
//import dagger.Module
//import dagger.Provides
//import dagger.hilt.InstallIn
//import dagger.hilt.components.SingletonComponent
//import io.reactivex.rxjava3.core.Observable
//import javax.inject.Named
//
//@Module
//@InstallIn(SingletonComponent::class)
//open class SessionMuseModule  {
//
//    companion object {
//        const val SQC_DISCONNECTION_MONITOR = "sqc"
//        const val MP_DISCONNECTION_MONITOR = "mp"
//
//        const val EEG_NAME = "eeg"
//        const val ACC_NAME = "acc"
//    }
//
//    class WithoutMuse : SessionMuseModule()
//
//    class WithMuse(private val muse: Muse) : SessionMuseModule() {
//
////        override fun provideSqcLegacyDisconnectionMonitor(
////            mainOperationQueue: PlatformMainOperationQueue,
////            museDataStatusMonitor: MuseDataStatusMonitor
////        ): DisconnectionMonitor {
////            return DisconnectionMonitor.create(
////                mainOperationQueue, museDataStatusMonitor,
////                MuseSessionType.SLEEP, InterruptSessionState.SQC
////            )
////        }
//
////        override fun provideMuseDataStatusMonitor(
////            museDeviceSelector: MuseDeviceSelector
////        ): MuseDataStatusMonitor {
////            return MuseDataStatusMonitor.create(museDeviceSelector)
////        }
//
////        override fun provideMuseSqcDisconnectionMonitor(
////            legacyDisconnectionMonitor: DisconnectionMonitor
////        ): MuseDisconnectionMonitor {
////            return MuseDisconnectionMonitor(legacyDisconnectionMonitor)
////        }
//
////        override fun provideMeditationPlayerLegacyDisconnectionMonitor(
////            mainOperationQueue: PlatformMainOperationQueue,
////            museDataStatusMonitor: MuseDataStatusMonitor
////        ): DisconnectionMonitor {
////            return DisconnectionMonitor.create(
////                mainOperationQueue, museDataStatusMonitor,
////                MuseSessionType.SLEEP, InterruptSessionState.EXERCISE
////            )
////        }
//
////        override fun provideMuseMeditationPlayerDisconnectionMonitor(
////            legacyDisconnectionMonitor: DisconnectionMonitor
////        ): MuseDisconnectionMonitor {
////            return MuseDisconnectionMonitor(legacyDisconnectionMonitor)
////        }
////
////        override fun provideMuseBadSignalMonitor(
////            legacyMonitor: BadSignalMonitor,
////            signalQualityProcessor: BusymindSignalQualityProcessor,
////            @Named(UserManagerModule.LENIENT_SQC) lenientSqcEnabled: Boolean
////        ): MuseBadSignalMonitor {
////            return MuseBadSignalMonitor(
////                legacyMonitor,
////                signalQualityProcessor,
////                lenientSqcEnabled
////            )
////        }
//
////        override fun provideBadSignalMonitor(
////            museDataStatusMonitor: MuseDataStatusMonitor
////        ): BadSignalMonitor {
////            return BadSignalMonitor.create(museDataStatusMonitor)
////        }
//
//        override fun provideMuse(): Muse {
//            return muse
//        }
//
//        override fun provideConnectionStateObservable(
//            museDataObservableFactory: MuseDataObservableFactory
//        ): Observable<ConnectionState> {
//            return museDataObservableFactory.createConnectionStateObservable(muse)
//        }
//
//        override fun provideEegObservable(
//            museDataObservableFactory: MuseDataObservableFactory
//        ): Observable<MuseDataPacket> {
//            return museDataObservableFactory.createDataObservable(muse, MuseDataPacketType.EEG)
//        }
//
//        override fun provideAccObservable(
//            museDataObservableFactory: MuseDataObservableFactory
//        ): Observable<MuseDataPacket> {
//            return museDataObservableFactory.createDataObservable(muse, MuseDataPacketType.ACCELEROMETER)
//        }
//    }
//
////    @Provides
////    @SessionScope
////    @Named(SQC_DISCONNECTION_MONITOR)
////    open fun provideSqcLegacyDisconnectionMonitor(
////        mainOperationQueue: PlatformMainOperationQueue,
////        museDataStatusMonitor: MuseDataStatusMonitor
////    ): DisconnectionMonitor {
////        return object : DisconnectionMonitor() {
////            override fun setupWithTimeout(secondsToDisconnect: Int) {
////            }
////
////            override fun teardown() {
////            }
////
////            override fun setDelegate(delegate: DisconnectionMonitorDelegate?) {
////            }
////
////        }
////    }
////
////    @Provides
////    @SessionScope
////    @Named(SQC_DISCONNECTION_MONITOR)
////    open fun provideMuseSqcDisconnectionMonitor(
////        @Named(SQC_DISCONNECTION_MONITOR) legacyDisconnectionMonitor: DisconnectionMonitor
////    ): MuseDisconnectionMonitor {
////        return MuseDisconnectionMonitor(legacyDisconnectionMonitor)
////    }
////
////    @Provides
////    @SessionScope
////    @Named(MP_DISCONNECTION_MONITOR)
////    open fun provideMeditationPlayerLegacyDisconnectionMonitor(
////        mainOperationQueue: PlatformMainOperationQueue,
////        museDataStatusMonitor: MuseDataStatusMonitor
////    ): DisconnectionMonitor {
////        return object : DisconnectionMonitor() {
////            override fun setupWithTimeout(secondsToDisconnect: Int) {
////            }
////
////            override fun teardown() {
////            }
////
////            override fun setDelegate(delegate: DisconnectionMonitorDelegate?) {
////            }
////        }
////    }
////
////    @Provides
////    @SessionScope
////    @Named(MP_DISCONNECTION_MONITOR)
////    open fun provideMuseMeditationPlayerDisconnectionMonitor(
////        @Named(MP_DISCONNECTION_MONITOR) legacyDisconnectionMonitor: DisconnectionMonitor
////    ): MuseDisconnectionMonitor? {
////        return null
////    }
////
////    @Provides
////    @SessionScope
////    open fun provideMuseDataStatusMonitor(
////        museDeviceSelector: MuseDeviceSelector
////    ): MuseDataStatusMonitor {
////        return object : MuseDataStatusMonitor() {
////            override fun getMuseDataStatus() = MuseDataStatus.NO_DATA
////
////            override fun getMuseInfo() = MuseInfo("", "", MuseDeviceModel.MU_01)
////
////            override fun receiveBusymindPacket(packet: BusymindPacket?, muse: Muse?) {
////            }
////        }
////    }
////
////    @Provides
////    @SessionScope
////    open fun provideMuseBadSignalMonitor(
////        legacyMonitor: BadSignalMonitor,
////        signalQualityProcessor: BusymindSignalQualityProcessor,
////        @Named(UserManagerModule.LENIENT_SQC) lenientSqcEnabled: Boolean
////    ): MuseBadSignalMonitor? {
////        return null
////    }
////
////    @Provides
////    @SessionScope
////    open fun provideBadSignalMonitor(
////        museDataStatusMonitor: MuseDataStatusMonitor
////    ): BadSignalMonitor {
////        return object : BadSignalMonitor() {
////            override fun reset() {
////            }
////
////            override fun isBadSignalDetected(): Boolean {
////                return false
////            }
////        }
////    }
//
//    @Provides
//    @SessionScope
//    open fun provideMuse(): Muse? {
//        return null
//    }
//
//    @Provides
//    @SessionScope
//    open fun provideConnectionStateObservable(
//        museDataObservableFactory: MuseDataObservableFactory
//    ): Observable<ConnectionState> {
//        return Observable.empty()
//    }
//
//    @Provides
//    @SessionScope
//    @Named(EEG_NAME)
//    open fun provideEegObservable(
//        museDataObservableFactory: MuseDataObservableFactory
//    ): Observable<MuseDataPacket> {
//        return Observable.empty()
//    }
//
//    @Provides
//    @SessionScope
//    @Named(ACC_NAME)
//    open fun provideAccObservable(
//        museDataObservableFactory: MuseDataObservableFactory
//    ): Observable<MuseDataPacket> {
//        return Observable.empty()
//    }
//}
