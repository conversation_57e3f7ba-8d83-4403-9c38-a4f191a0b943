package com.ruiheng.xmuse.core.data.repository.muse

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseConnectionPacket
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.internal.BusymindPacket
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.model.data.EEGType
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.subjects.BehaviorSubject

fun MuseConnectionPacket.isOffline() =
    currentConnectionState == ConnectionState.DISCONNECTED || currentConnectionState == ConnectionState.UNKNOWN

fun MuseConnectionPacket.isLoading() =
    currentConnectionState == ConnectionState.CONNECTING || currentConnectionState == ConnectionState.NEEDS_LICENSE

fun MuseConnectionPacket.isOnline() =
    currentConnectionState == ConnectionState.NEEDS_UPDATE || currentConnectionState == ConnectionState.CONNECTED

fun ConnectionState.isOnline() =
    this == ConnectionState.NEEDS_UPDATE || this == ConnectionState.CONNECTED

interface MuseDeviceRepositoryImp {

    val _scanXmuseResources: MutableLiveData<Result<MutableList<Muse>>>

    val _connectMuseLiveData: MutableLiveData<Result<Muse?>>

    val _xmuseEEGLiveData: MutableLiveData<Array<Float>?>

    val _xmuseHsiPLiveData: MutableLiveData<Array<Int>>

    val _xmuseAlphaAbsoluteLiveData: MutableLiveData<Array<Float>?>
    val _xmuseBetaAbsoluteLiveData: MutableLiveData<Array<Float>?>
    val _xmuseDeltaAbsoluteLiveData: MutableLiveData<Array<Float>?>
    val _xmuseThetaAbsoluteLiveData: MutableLiveData<Array<Float>?>
    val _xmuseGammaAbsoluteLiveData: MutableLiveData<Array<Float>?>

    val _xmuseAlphaScoreLiveData: MutableLiveData<Float>
    val _xmuseBetaScoreLiveData: MutableLiveData<Float>
    val _xmuseDeltaScoreLiveData: MutableLiveData<Float>
    val _xmuseThetaScoreLiveData: MutableLiveData<Float>
    val _xmuseGammaScoreLiveData: MutableLiveData<Float>

    val _xmuseAlphaRelativeLiveData: MutableLiveData<Array<Double>?>
    val _xmuseBetaRelativeLiveData: MutableLiveData<Array<Double>?>
    val _xmuseDeltaRelativeLiveData: MutableLiveData<Array<Double>?>
    val _xmuseThetaRelativeLiveData: MutableLiveData<Array<Double>?>
    val _xmuseGammaRelativeLiveData: MutableLiveData<Array<Double>?>

    val _xmuseDrlRefLiveData: MutableLiveData<Array<Float>?>

    val _xmuseIRLiveData: MutableLiveData<Float>
    val _xmuseJawClenchLiveData: MutableLiveData<Boolean>
    val _xmuseBlinkLiveData: MutableLiveData<Boolean>
    val _xmusePPGIsGood: MutableLiveData<Boolean>
    val _xmuseGyroLiveData: MutableLiveData<Array<Double>?>
    val _xmuseAccLiveData: MutableLiveData<Array<Double>?>

    fun observerScanList() = _scanXmuseResources as LiveData<Result<MutableList<Muse>>>
    fun obseverXmuseConnectLiveData() = _connectMuseLiveData as LiveData<Result<Muse?>>

    fun observerEEG() = _xmuseEEGLiveData as LiveData<Array<Float>?>
    fun observerHSIP() = _xmuseHsiPLiveData as LiveData<Array<Int>>

    fun observerEEGTypeObsolute(eegType: EEGType) = when (eegType) {
        EEGType.Alpha -> _xmuseAlphaAbsoluteLiveData
        EEGType.Beta -> _xmuseBetaAbsoluteLiveData
        EEGType.Delta -> _xmuseDeltaAbsoluteLiveData
        EEGType.Gamma -> _xmuseGammaAbsoluteLiveData
        EEGType.Theta -> _xmuseThetaAbsoluteLiveData
    } as LiveData<Array<Float>?>

    fun observerAlphaAbsolute() = _xmuseAlphaAbsoluteLiveData as LiveData<Array<Float>?>
    fun observerBetaAbsolute() = _xmuseBetaAbsoluteLiveData as LiveData<Array<Float>?>
    fun observerDeltaAbsolute() = _xmuseDeltaAbsoluteLiveData as LiveData<Array<Float>?>
    fun observerThetaAbsolute() = _xmuseThetaAbsoluteLiveData as LiveData<Array<Float>?>
    fun observerGammaAbsolute() = _xmuseGammaAbsoluteLiveData as LiveData<Array<Float>?>
    fun observerAlphaScore() = _xmuseAlphaScoreLiveData as LiveData<Float>
    fun observerBetaScore() = _xmuseBetaScoreLiveData as LiveData<Float>
    fun observerDeltaScore() = _xmuseDeltaScoreLiveData as LiveData<Float>
    fun observerThetaScore() = _xmuseThetaScoreLiveData as LiveData<Float>
    fun observerGammaScore() = _xmuseGammaScoreLiveData as LiveData<Float>
    fun observerIR() = _xmuseIRLiveData as LiveData<Float>
    fun observerDrlRef() = _xmuseDrlRefLiveData as LiveData<Array<Float>?>
    fun observerJawClench() = _xmuseJawClenchLiveData as LiveData<Boolean>
    fun observerBlink() = _xmuseBlinkLiveData as LiveData<Boolean>
    fun observerPPGIsGood() = _xmusePPGIsGood as LiveData<Boolean>
    fun observerGyro() = _xmuseGyroLiveData as LiveData<Array<Double>?>
    fun observerAcc() = _xmuseAccLiveData as LiveData<Array<Double>?>
    fun observerAlphaRelative() = _xmuseAlphaRelativeLiveData as LiveData<Array<Double>?>
    fun observerBetaRelative() = _xmuseBetaRelativeLiveData as LiveData<Array<Double>?>
    fun observerGammaRelative() = _xmuseGammaRelativeLiveData as LiveData<Array<Double>?>
    fun observerThetaRelative() = _xmuseThetaRelativeLiveData as LiveData<Array<Double>?>
    fun observerDeltaRelative() = _xmuseDeltaRelativeLiveData as LiveData<Array<Double>?>

    val nfbPackets: Observable<BusymindPacket>
    val sleepStageClassifier: BehaviorSubject<List<Float>>

    val eegPackets: Observable<MuseDataPacket>
    val accPackets:Observable<MuseDataPacket>

    val museConnectionState: BehaviorSubject<ConnectionState>
}