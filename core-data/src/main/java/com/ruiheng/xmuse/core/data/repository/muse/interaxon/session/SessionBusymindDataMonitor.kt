package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.internal.BusymindPacket
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityRetainedScoped
class SessionBusymindDataMonitor(
    private val busymindMonitor: BusymindMonitor,
    museConnectionState: Observable<ConnectionState>
) {

    companion object {
        private const val HEART_DATA_INVERSE_FREQUENCY_MS = 15L // (64hz)
        private const val OTHER_DATA_INVERSE_FREQUENCY_MS = 100L // (10hz)
        private const val GAP_VALUE = -1.0

        private val OUTPUTS_AT_64_HZ = BusymindData.entries.filter {
            when (it) {
                BusymindData.ALPHA,
                BusymindData.BETA,
                BusymindData.DELTA,
                BusymindData.GAMMA,
                BusymindData.THETA,
                BusymindData.MIND,
                BusymindData.BODY,
                BusymindData.BREATH -> false

                BusymindData.PPG_HRV_HF,
                BusymindData.PPG_HRV_LFvsHF,
                BusymindData.PPG_HRV_LF,
                BusymindData.PPG_HRV_SDNN,
                BusymindData.PPG_HRV_RMSSD,
                BusymindData.PPG_HRV_PNN50,

                BusymindData.HEART,
                BusymindData.FOX_SCORE,
                BusymindData.HBO_LEFT,
                BusymindData.HBO_RIGHT,
                BusymindData.HBR_LEFT,
                BusymindData.HBR_RIGHT -> true
            }
        }

        private val OUTPUTS_AT_10_HZ =
            BusymindData.entries.filter { !OUTPUTS_AT_64_HZ.contains(it) }
    }

    @Inject
    constructor(
        busymindMonitor: BusymindMonitor,
        factory: MuseDataObservableFactory,
        museConnector: MuseConnector
    ) : this(
        busymindMonitor, if (museConnector.getCurrentMuse() != null) {
            factory.createConnectionStateObservable(museConnector.getCurrentMuse()!!)
        } else {
            Observable.empty()
        }
    )

    private val gapEnabled = BehaviorSubject.createDefault(false)

    val busymindData: Observable<Map<BusymindData, Double>> =
        Observable.combineLatest(
            gapEnabled,
            museConnectionState
        ) { gapEnabled, connectionState ->
            gapEnabled || connectionState != ConnectionState.CONNECTED
        }.switchMap { outputGapFillerData ->
            if (!outputGapFillerData) {
                busymindMonitor.nfbPackets
                    .map {
                        if (it.isProcEeg) {
                            mapOf(
                                BusymindData.ALPHA to it.bandAvgPowsRawAlpha,
                                BusymindData.BETA to it.bandAvgPowsRawBeta,
                                BusymindData.DELTA to it.bandAvgPowsRawDelta,
                                BusymindData.GAMMA to it.bandAvgPowsRawGamma,
                                BusymindData.THETA to it.bandAvgPowsRawTheta,
                                BusymindData.MIND to it.busymind
                            )
                        } else if (it.isBodyScoreUpdated) {
                            mapOf(BusymindData.BODY to it.bodyScore)
                        } else if (it.isHeartUpdated && it.isPpgGood && it.isDrlGood) {
                            mapOf(BusymindData.HEART to it.heartScore)
                        } else if (it.isBreathScoreUpdated) {
                            mapOf(BusymindData.BREATH to it.breathScore)
                        } else if (it.isProcOptics) {
                            mapOf(
                                BusymindData.FOX_SCORE to it.foxScore,
                                BusymindData.HBO_LEFT to it.hboLeft,
                                BusymindData.HBO_RIGHT to it.hboRight,
                                BusymindData.HBR_LEFT to it.hbrLeft,
                                BusymindData.HBR_RIGHT to it.hbrRight,
                            )
                        } else if (it.isProcPpg) {

                            mapOf(
                                BusymindData.PPG_HRV_HF to it.ppgHf,
                                BusymindData.PPG_HRV_LF to it.ppgLf,
                                BusymindData.PPG_HRV_SDNN to it.ppgSdrr,
                                BusymindData.PPG_HRV_RMSSD to it.ppgRmssd,
                                BusymindData.PPG_HRV_PNN50 to it.ppgPrr50,
                                BusymindData.PPG_HRV_LFvsHF to it.ppgLfVsHf,
                            )
                        } else {
                            //TODO 等interaxon确认问题后
                            if (isHrvValueRight(it)) {
                                mapOf(
                                    BusymindData.PPG_HRV_HF to it.ppgHf,
                                    BusymindData.PPG_HRV_LF to it.ppgLf,
                                    BusymindData.PPG_HRV_SDNN to it.ppgSdrr,
                                    BusymindData.PPG_HRV_RMSSD to it.ppgRmssd,
                                    BusymindData.PPG_HRV_PNN50 to it.ppgPrr50,
                                    BusymindData.PPG_HRV_LFvsHF to it.ppgLfVsHf,
                                )
                            } else {
                                emptyMap()
                            }
                        }
                    }
                    .filter { it.isNotEmpty() }

            } else {
                Observable.merge(
                    Observable.interval(
                        HEART_DATA_INVERSE_FREQUENCY_MS,
                        TimeUnit.MILLISECONDS,
                        AndroidSchedulers.mainThread()
                    ).map { OUTPUTS_AT_64_HZ.associateWith { GAP_VALUE } },
                    Observable.interval(
                        OTHER_DATA_INVERSE_FREQUENCY_MS,
                        TimeUnit.MILLISECONDS,
                        AndroidSchedulers.mainThread()
                    ).map { OUTPUTS_AT_10_HZ.associateWith { GAP_VALUE } }
                )
            }
        }

    fun setGapEnabled(gapEnabled: Boolean) {
        this.gapEnabled.onNext(gapEnabled)
    }

    private fun isHrvValueRight(busymindPacket: BusymindPacket): Boolean {
        fun valueRight(value: Double) = value != 0.0 && value != -1.0
        return valueRight(busymindPacket.ppgHf)
                && valueRight(busymindPacket.ppgLfVsHf)
                && valueRight(busymindPacket.ppgLf)
                && valueRight(busymindPacket.ppgPrr50)
                && valueRight(busymindPacket.ppgRmssd)
                && valueRight(busymindPacket.ppgSdrr)
    }
}