package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.FrontalOxygenationUserSession
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import kotlin.math.max

class BusymindTimeseriesDataTracker(
    private val tfliteFactory: TfliteFactory,
    val presenterModel: TflitePresenterModel
) {

    companion object {
        const val FOX_SCORE_MIN = 0.0
        const val FOX_SCORE_LOW = 33.0
        const val FOX_SCORE_MED = 67.0
        const val FOX_SCORE_MAX = 100.0

        fun extractFrontalOxygenationUserSession(
            timeseries: java.util.ArrayList<Double>,
            completedSeconds: Long
        ): FrontalOxygenationUserSession {
            return MuseDataUtils.calcSecondsPerRange(
                timeseries, completedSeconds,
                arrayListOf(
                    FOX_SCORE_MIN,
                    FOX_SCORE_LOW,
                    FOX_SCORE_MED,
                    FOX_SCORE_MAX
                )
            ).let { seconds ->
                FrontalOxygenationUserSession(
                    lowSeconds = seconds[0].toInt(),
                    mediumSeconds = seconds[1].toInt(),
                    highSeconds = seconds[2].toInt(),
                    score = timeseries.maxOrNull()?.toInt()?.let { max(0, it) }
                )
            }
        }
    }

    /**
     * Synchronizes access to [inputBuffer]. Writing to [inputBuffer] and reading from it
     * via [process] can happen on different threads
     */
    private val lock = Object()
    private val inputBuffer = ArrayList<Double>()

    private var disposeable: Disposable? = null

    fun startTracking(inputData: Observable<Double>) {
        inputData
            .map {
                if (it.isNaN()) {
                    -1.0
                } else {
                    it
                }
            }
            .subscribe {
                synchronized(lock) {
                    inputBuffer.add(it)
                }
            }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun process(connectTimes: List<Float>, disconnectTimes: List<Float>): ArrayList<Float> {
        val input = synchronized(lock) { ArrayList<Float>(inputBuffer.map(Double::toFloat)) }
        return tfliteFactory.createPresenter(presenterModel)
            .setData(input)
            .setReconnectTimestamps(ArrayList(connectTimes))
            .setDisconnectTimestamps(ArrayList(disconnectTimes))
            .processData()
    }
}