package com.interaxon.muse.session.realtime_neurofeedback

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.PlaybackState
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.PreciseTimer
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import java.lang.Long.max
import javax.inject.Inject

/**
 * 高精度倒计时器 - 基于 PreciseTimer 实现精确的毫秒级倒计时功能
 * 支持设置倒计时时长，并在计时过程中实时发布剩余毫秒数
 * 当倒计时结束时自动停止，并将剩余时间置为0
 */
@ActivityRetainedScoped
class PreciseCountdownTimer
@Inject constructor(
    private val preciseTimer: PreciseTimer
) {

    /**
     * Counts down to zero
     */
    val millisRemaining = BehaviorSubject.create<Long>()

    val state: BehaviorSubject<PlaybackState>
        get() = preciseTimer.state

    private var startMillis: Long = 0
    private var disposable = CompositeDisposable()

    fun restart(millisRemaining: Long) {
        setup(millisRemaining)
        preciseTimer.restart()
    }

    fun start(millisRemaining: Long) {
        setup(millisRemaining)
        preciseTimer.start()
    }

    private fun setup(startMillis: Long) {
        disposable.clear()
        disposable = CompositeDisposable()

        this.startMillis = startMillis
        preciseTimer.millisElapsed
            .map { max(0L, this.startMillis - it) }
            .distinctUntilChanged()
            .subscribe {
                if (it <= 0) {
                    preciseTimer.end()
                }
                millisRemaining.onNext(it)
            }.let { disposable.add(it) }
    }

    fun resume() {
        preciseTimer.resume()
    }

    fun pause() {
        preciseTimer.pause()
    }

    fun end() {
        preciseTimer.end()
        disposable.clear()
    }
}