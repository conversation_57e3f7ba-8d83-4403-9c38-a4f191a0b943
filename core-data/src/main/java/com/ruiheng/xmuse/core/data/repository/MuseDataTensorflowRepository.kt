//package com.ruiheng.xmuse.core.data.repository
//
//import com.blankj.utilcode.util.Utils
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
//import io.reactivex.rxjava3.schedulers.Schedulers
//import org.tensorflow.lite.Interpreter
//import timber.log.Timber
//import java.io.FileInputStream
//import java.io.IOException
//import java.nio.MappedByteBuffer
//import java.nio.channels.FileChannel
//import java.util.LinkedList
//import javax.inject.Inject
//import javax.inject.Singleton
//
//@Singleton
//class MuseDataTensorflowRepository @Inject constructor(private val museConnector: MuseConnector) {
//    companion object {
//        private const val signalSize = 256
//        private val batchSize = 1
//        private val sequenceLength = 4
//    }
//
//    private var signalEegValue = LinkedList<Array<Double>>()
//    private lateinit var tflite: Interpreter
//
//    init {
//        try {
//            // 加载模型
//            val modelBuffer = loadModelFile()
//            tflite = Interpreter(modelBuffer)
////            tflite.allocateTensors()
////            val inputIndex  = tflite.getInputIndex("0")
//        } catch (e: IOException) {
//            e.printStackTrace()
//        }
//    }
//
////    private val eegObservable = Observer<Array<Float>?> { eeg ->
////        if (eeg == null) return@Observer
////        signalEegValue.add(eeg)
////        if (signalEegValue.size >= signalSize) {
////            startCalculate(signalEegValue.toMutableList())
////            signalEegValue.clear()
////        }
////    }
//
//    private fun startCalculate(dataList: List<Array<Double>>) {
//        if (dataList.size != signalSize) return
//        val input = Array(batchSize) { Array(sequenceLength) { FloatArray(signalSize) } }
//        for (b in 0 until batchSize) {
//            for (s in 0 until sequenceLength) {
//                for (f in 0 until signalSize) {
//                    input[b][s][f] = dataList[f][s].toFloat()
//                }
//            }
//        }
//
////        val inputIndex = tflite.input
////        val outputIndex = tflite.getOutputIndex(0)
////
//        val output = Array(1) { FloatArray(4) }
//        tflite.run(input, output)
//        Timber.d("!!!!!!!startCalculate:${output[0].withIndex().maxByOrNull { it.value }}")
////        tflite.resizeInput(inputIndex, intArrayOf(batchSize, sequenceLength, featureDimension))
////        tflite.allocateTensors()
////        tflite.setInput(inputIndex, input)
////
////        // 运行模型
////        tflite.invoke()
//    }
//
//    fun startMonitoring() {
//
//        museConnector.provideEegObservable()
//            .observeOn(Schedulers.io())
//            .subscribe { eegPacket ->
//                val values: Array<Double> = eegPacket.values().toTypedArray()
//                Timber.d("!!!!!!!provideEegObservable:${values.toString()}")
//                signalEegValue.add(values)
//                if (signalEegValue.size >= signalSize) {
//                    startCalculate(signalEegValue.toMutableList())
//                    signalEegValue.clear()
//                }
//            }.let { }
////        museDeviceRepository._xmuseEEGLiveData.observeForever(eegObservable)
////        tflite.run()
//    }
//
//    @Throws(IOException::class)
//    private fun loadModelFile(): MappedByteBuffer {
//        val fileDescriptor = Utils.getApp().assets.openFd("tflite_model_sqc.tflite")
//        val inputStream = FileInputStream(fileDescriptor.fileDescriptor)
//        val fileChannel = inputStream.channel
//        val startOffset = fileDescriptor.startOffset
//        val declaredLength = fileDescriptor.declaredLength
//        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
//    }
////    fun stopMo
//}