package com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf

import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.MuseDataPacket
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import org.threeten.bp.Instant
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

@ActivityRetainedScoped
class MuseTimestampedDataMonitor
@Inject constructor(
    private val museConnector: MuseConnector
//    private val connectionState: Observable<ConnectionState>,
//    @Named(EEG_NAME) eegPackets: Observable<MuseDataPacket>,
//    @Named(ACC_NAME) accPackets: Observable<MuseDataPacket>
) {
    private var startTimeEpochMs: Long? = null

    /**
     * [gapStartTimes] and [gapEndTimes] access is synchronized with lock
     */
    private val lock = Object()
    private val gapStartTimes = ArrayList<Float>()
    private val gapEndTimes = ArrayList<Float>()
    private var connected = true
    private var paused = false
    private var isInGap = false

    private val disposable = CompositeDisposable()

    val timestampedEeg: Observable<Pair<Float, ArrayList<Double>>> =
        museConnector.provideEegObservable().map { Pair(getTimeSinceStart(), it.values()) }
    val timestampedAcc: Observable<Pair<Float, ArrayList<Double>>> =
        museConnector.provideAccObservable().map { Pair(getTimeSinceStart(), it.values()) }

    fun setStartTime() {
        startTimeEpochMs = Instant.now().toEpochMilli()
    }

    fun startTracking() {
        museConnector.museConnectionState
            .subscribe { state ->
                synchronized(lock) {
                    when (state) {
                        ConnectionState.CONNECTED -> {
                            if (connected) {
                                return@synchronized
                            }
                            connected = true
                            if (!paused) {
                                addGapEndTime()
                            }
                        }

                        ConnectionState.NEEDS_LICENSE,
                        ConnectionState.DISCONNECTED,
                        ConnectionState.NEEDS_UPDATE,
                        ConnectionState.CONNECTING,
                        ConnectionState.UNKNOWN -> {
                            if (!connected) {
                                return@synchronized
                            }
                            connected = false
                            addGapStartTime()
                        }
                    }
                }
            }.let { disposable.add(it) }
    }

    fun stopTracking() {
        disposable.clear()
    }

    fun pause() {
        synchronized(lock) {
            if (paused) {
                return
            }
            paused = true
            addGapStartTime()
        }
    }

    fun resume() {
        synchronized(lock) {
            if (!paused) {
                return
            }
            paused = false
            if (connected) {
                addGapEndTime()
            }
        }
    }

    fun getTimeSinceStart(): Float {
        val start = startTimeEpochMs ?: return 0.0f
        return (Instant.now().toEpochMilli() - start) / 1000.0f
    }

    private fun addGapStartTime() {
        if (isInGap) {
            return
        }
        isInGap = true
        gapStartTimes.add(getTimeSinceStart())
    }

    private fun addGapEndTime() {
        if (!isInGap) {
            return
        }
        isInGap = false
        gapEndTimes.add(getTimeSinceStart())
    }

    fun getGapStartTimes(): ArrayList<Float> {
        synchronized(lock) {
            return ArrayList(gapStartTimes)
        }
    }

    fun getGapEndTimes(): ArrayList<Float> {
        synchronized(lock) {
            return ArrayList(gapEndTimes)
        }
    }

}