package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel

enum class BusymindData(val requestParam: String) {
    ALPHA("bandAvgPowsRawAlphaList"),
    BETA("bandAvgPowsRawBetaList"),
    DELTA("bandAvgPowsRawDeltaList"),
    GAMMA("bandAvgPowsRawGammaList"),
    THETA("bandAvgPowsRawThetaList"),
    MIND("busymind"),
    BODY("busybody"),
    HEART("heartRateList"),
    BREATH("breathList"),
    FOX_SCORE("foxScoreList"),
    HBO_LEFT("hboLeftList"),
    HBO_RIGHT("hboRightList"),
    HBR_LEFT("hbrLeftList"),
    HBR_RIGHT("hbrRightList"),
    PPG_HRV_HF("ppgHfList"),
    PPG_HRV_LF("ppgLfList"),
    PPG_HRV_LFvsHF("ppgLfVsHfList"),
    PPG_HRV_PNN50("ppgPrr50List"),
    PPG_HRV_RMSSD("ppgRmssdList"),
    PPG_HRV_SDNN("ppgSdrrList");

    fun getPresenterModel(): TflitePresenterModel {
        return when (this) {
            ALPHA, BETA, DELTA, GAMMA, THETA -> TflitePresenterModel.BAND_POWER_GRAPH

            BREATH, BODY, MIND -> TflitePresenterModel.TEN_HZ_INPUT_TIMESERIES

            HEART, PPG_HRV_HF, PPG_HRV_LF, PPG_HRV_LFvsHF, PPG_HRV_PNN50, PPG_HRV_RMSSD, PPG_HRV_SDNN -> TflitePresenterModel.SIXTY_FOUR_HZ_INPUT_TIMESERIES
            FOX_SCORE -> TflitePresenterModel.FOX_SCORE_GRAPH
            HBO_LEFT, HBO_RIGHT, HBR_LEFT, HBR_RIGHT -> TflitePresenterModel.CONCENTRATIONS_SCORE_GRAPH
        }
    }

}