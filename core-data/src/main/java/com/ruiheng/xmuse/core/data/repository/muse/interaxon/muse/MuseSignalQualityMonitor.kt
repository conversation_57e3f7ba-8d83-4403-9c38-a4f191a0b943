package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.ConnectionState
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.BusymindSignalQualityProcessor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQuality
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQualityProcessor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SignalQualityQueue
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.SqcSensor
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import org.threeten.bp.Instant
import java.util.Collections
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.min

@Singleton
class MuseSignalQualityMonitor(
    val signalQualityProcessor: SignalQualityProcessor,
    private val museConnectionState: Observable<ConnectionState>,
    private val frameRateEpochMillisClock: Observable<Long>
) {

    @Inject
    constructor(
        signalQualityProcessor: BusymindSignalQualityProcessor,
        museConnector: MuseConnector,
    ) : this(
        signalQualityProcessor,
        museConnector.museConnectionState,
        Observable.interval(1000L / FPS, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
            .map { Instant.now().toEpochMilli() }
    )

    companion object {
        private const val FPS = 30
        const val BLACKCOMB_PPG_FILL_MILLIS = 5500L
        const val SLEEP_MUSE_PPG_FILL_MILLIS = 3000L
        const val EEG_FILL_MILLIS = 3000L
        const val DRL_REF_FILL_MILLIS = 1000L
    }

    private var signalQualityQueueUpdaters =
        SqcSensor.entries.associateWith { sensor ->
            val millisToFill = when (sensor) {
                SqcSensor.DRL_REF -> DRL_REF_FILL_MILLIS
                SqcSensor.PPG -> BLACKCOMB_PPG_FILL_MILLIS
                else -> EEG_FILL_MILLIS
            }
            SignalQualityQueueUpdater(FPS, millisToFill)
        }

    val sensorSignalQualityQueues = BehaviorSubject.createDefault(
        SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.queue }
    )
    val sensorSignalQualityProgress = BehaviorSubject.createDefault(
        SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.progress }
    )
    val sensorSignalQualityAntiprogress = BehaviorSubject.createDefault(
        SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.antiprogress }
    )
    val sensorContactStrengths = BehaviorSubject.createDefault(
        SqcSensor.entries.associateWith { 0.0 }
    )

    val heartbeatDetected = BehaviorSubject.createDefault(false)

    private var disposable: CompositeDisposable? = null

    var eegSensorSignalQualityProxiesForPpgSignalQuality = false

    fun setPpgFillMillis(ppgMillis: Long) {
        signalQualityQueueUpdaters = SqcSensor.entries.associateWith { sensor ->
            val secondsToFill = when (sensor) {
                SqcSensor.DRL_REF -> DRL_REF_FILL_MILLIS
                SqcSensor.PPG -> ppgMillis
                else -> EEG_FILL_MILLIS
            }
            SignalQualityQueueUpdater(FPS, secondsToFill)
        }
        sensorSignalQualityQueues.onNext(
            SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.queue }
        )
        sensorSignalQualityProgress.onNext(
            SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.progress }
        )
        sensorSignalQualityAntiprogress.onNext(
            SqcSensor.entries.associateWith { signalQualityQueueUpdaters[it]!!.antiprogress }
        )
    }

    fun startMonitoring() {
        if (disposable != null && disposable?.isDisposed == false) return
        disposable = CompositeDisposable()
        setupBluetoothDisconnectionMonitoring()
        setupSignalQualityUpdatesAtAnimationFrameRate()
        setupSignalQualityQueueUpdates()
        signalQualityProcessor.startProcessing()
    }

    private fun setupBluetoothDisconnectionMonitoring() {
        museConnectionState
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { connectionState ->
                when (connectionState) {
                    ConnectionState.CONNECTED -> {
                    }

                    else -> {
                        SqcSensor.entries.forEach { sensor ->
                            signalQualityQueueUpdaters[sensor]?.latestValue = SignalQuality.BAD
                        }
                        updateContactStrengths(SqcSensor.entries.associateWith { 0.0 })
                    }
                }
            }.let { disposable?.add(it) }
    }

    private fun setupSignalQualityUpdatesAtAnimationFrameRate() {
        frameRateEpochMillisClock
            .subscribe { now ->
                val queueUpdate = SqcSensor.entries.associateWith { sensor ->
                    val update = signalQualityQueueUpdaters[sensor]!!
                    update.updateQueue(now)
                    update.queue
                }
                sensorSignalQualityQueues.onNext(queueUpdate)

                val progressUpdate = SqcSensor.entries
                    .associateWith { sensor -> signalQualityQueueUpdaters[sensor]!!.progress }
                sensorSignalQualityProgress.onNext(progressUpdate)

                val antiprogressUpdate = SqcSensor.entries
                    .associateWith { sensor -> signalQualityQueueUpdaters[sensor]!!.antiprogress }
                sensorSignalQualityAntiprogress.onNext(antiprogressUpdate)
            }.let { disposable?.add(it) }
    }

    private fun setupSignalQualityQueueUpdates() {
        signalQualityProcessor.drlRefGood
            .subscribe { drlRefGood ->
                updateContactStrengths(mapOf(SqcSensor.DRL_REF to if (drlRefGood) 1.0 else 0.0))
                signalQualityQueueUpdaters[SqcSensor.DRL_REF]?.latestValue =
                    if (drlRefGood) {
                        SignalQuality.GOOD
                    } else {
                        SignalQuality.BAD
                    }
            }.let { disposable?.add(it) }

        signalQualityProcessor.eegConveyorValues
            .subscribe { conveyorValues ->
                conveyorValues?.forEach { (sensor, quality) ->
                    signalQualityQueueUpdaters[sensor]?.latestValue = quality
                }
            }.let { disposable?.add(it) }

        signalQualityProcessor.fnirsSensorIndicatorValues
            .subscribe { sensorIndicatorValues ->
                updateContactStrengths(sensorIndicatorValues)
            }.let { disposable?.add(it) }

        signalQualityProcessor.eegSensorIndicatorValues
            .subscribe { sensorIndicatorValues ->
                updateContactStrengths(sensorIndicatorValues)
            }.let { disposable?.add(it) }

        Observable.combineLatest(
            signalQualityProcessor.eegConveyorValues,
            signalQualityProcessor.ppgConveyorValue
        ) { eegConveyorValues, ppgGood ->
            signalQualityQueueUpdaters[SqcSensor.PPG]?.latestValue =
                if (eegSensorSignalQualityProxiesForPpgSignalQuality) {
                    if (ppgGood || (eegConveyorValues.isNotEmpty() && eegConveyorValues.all { it.value == SignalQuality.GOOD })) {
                        SignalQuality.GOOD
                    } else {
                        SignalQuality.BAD
                    }
                } else {
                    if (ppgGood) {
                        SignalQuality.GOOD
                    } else {
                        SignalQuality.BAD
                    }
                }
        }.subscribe().let { disposable?.add(it) }

        Observable.combineLatest(
            signalQualityProcessor.eegSensorIndicatorValues,
            signalQualityProcessor.ppgSensorIndicatorValue
        ) { eeg, ppgGood ->
            updateContactStrengths(
                mapOf(
                    SqcSensor.PPG to
                            if (eegSensorSignalQualityProxiesForPpgSignalQuality) {
                                if (ppgGood || eeg.all { (_, value) -> value >= 1.0 }) 1.0 else 0.0
                            } else {
                                if (ppgGood) 1.0 else 0.0
                            }
                )
            )
        }.subscribe().let { disposable?.add(it) }

        signalQualityProcessor.heartbeatDetected
            .subscribe {
                heartbeatDetected.onNext(true)
                heartbeatDetected.onNext(false)
            }.let { disposable?.add(it) }
    }

    fun stopMonitoring() {
        signalQualityProcessor.stopProcessing()
        disposable?.clear()
        disposable = null
    }

    fun restart() {
        signalQualityQueueUpdaters.forEach { it.value.restartQueue() }
        val progressUpdate = SqcSensor.entries
            .associateWith { sensor -> signalQualityQueueUpdaters[sensor]!!.progress }
        sensorSignalQualityProgress.onNext(progressUpdate)

        val antiprogressUpdate = SqcSensor.entries
            .associateWith { sensor -> signalQualityQueueUpdaters[sensor]!!.antiprogress }
        sensorSignalQualityAntiprogress.onNext(antiprogressUpdate)
    }

    private class SignalQualityQueueUpdater(
        framesPerSecond: Int,
        conveyorFillTimeMillis: Long
    ) {
        private val framesToFillConveyor =
            (conveyorFillTimeMillis / 1000.0 * framesPerSecond).toInt()
        private val millisPerFrame = conveyorFillTimeMillis.toDouble() / framesToFillConveyor

        private var accumulatedTime: Long? = null
        private var prevTime: Long? = null
        private var progressCount = 0
        private var antiprogressCount = framesToFillConveyor

        var queue = SignalQualityQueue(Collections.nCopies(framesToFillConveyor, SignalQuality.BAD))
        var latestValue = SignalQuality.BAD
        val progress
            get() = progressCount.toDouble() / framesToFillConveyor
        val antiprogress
            get() = antiprogressCount.toDouble() / framesToFillConveyor

        fun updateQueue(now: Long) {
            val timeDelta = (accumulatedTime ?: 0) + now - (prevTime ?: now)
            prevTime = now
            val numAddedFrames = (timeDelta / millisPerFrame).toInt()
            for (i in 0 until numAddedFrames) {
                queue.enqueue(latestValue)
            }
            accumulatedTime = timeDelta - (numAddedFrames * millisPerFrame).toLong()
            if (numAddedFrames > 0) {
                progressCount = if (latestValue == SignalQuality.GOOD) {
                    min(progressCount + numAddedFrames, framesToFillConveyor)
                } else {
                    0
                }

                antiprogressCount = if (latestValue == SignalQuality.BAD) {
                    min(antiprogressCount + numAddedFrames, framesToFillConveyor)
                } else {
                    0
                }
            }
        }

        fun restartQueue() {
            queue = SignalQualityQueue(Collections.nCopies(framesToFillConveyor, SignalQuality.BAD))
            latestValue = SignalQuality.BAD
            progressCount = 0
            antiprogressCount = framesToFillConveyor
            accumulatedTime = null
            prevTime = null
        }
    }

    private fun updateContactStrengths(update: Map<SqcSensor, Double>) {
        sensorContactStrengths.value?.toMutableMap()?.let { contacts ->
            update.forEach { (sensor, strength) -> contacts[sensor] = strength }
            sensorContactStrengths.onNext(contacts)
        }
    }
}