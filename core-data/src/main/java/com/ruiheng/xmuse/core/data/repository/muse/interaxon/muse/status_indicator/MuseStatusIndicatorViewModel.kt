package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.status_indicator

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import javax.inject.Inject
import io.reactivex.rxjava3.disposables.CompositeDisposable
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseBatteryPercentageMonitor
import dagger.hilt.android.lifecycle.HiltViewModel

@HiltViewModel
class MuseStatusIndicatorViewModel @Inject constructor(
    museBatteryMonitor: MuseBatteryPercentageMonitor,
) : ViewModel() {

    val batteryPercentage: LiveData<Int>
        get() = mutableBatteryPercentage
    private val mutableBatteryPercentage = MutableLiveData<Int>()

    private val disposableBag = CompositeDisposable()

    init {
        museBatteryMonitor.batteryPercentage
            .subscribe(mutableBatteryPercentage::postValue)
            .let { disposableBag.add(it) }
    }
}