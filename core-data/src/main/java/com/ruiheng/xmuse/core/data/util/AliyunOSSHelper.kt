package com.ruiheng.xmuse.core.data.util

import android.content.Context
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.common.auth.OSSPlainTextAKSKCredentialProvider
import com.alibaba.sdk.android.oss.signer.SignVersion
import com.blankj.utilcode.util.Utils

object AliyunOSSHelper {
    private var ossClient: OSSClient? = null
    fun getOSSClient(context: Context): OSSClient? {
        if (ossClient == null) {
            // 配置参数
            val endpoint: String = "oss-cn-beijing.aliyuncs.com"
            val region: String = "cn-beijing"

            val accessKeyId = "LTAI5tNCjkTFvY9cxcc7qnTS"
            val accessKeySecret = "******************************"
            val credentialProvider = OSSPlainTextAKSKCredentialProvider(accessKeyId, accessKeySecret)
            val config = ClientConfiguration()
            config.signVersion = SignVersion.V4
            ossClient = OSSClient(Utils.getApp(), endpoint, credentialProvider)
            ossClient!!.setRegion(region)
        }
        return ossClient
    }
}