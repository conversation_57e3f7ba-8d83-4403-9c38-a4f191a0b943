package com.ruiheng.xmuse.core.data.repository

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.os.Environment
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.common.result.network.di.ApplicationScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FileDownloadRepository @Inject constructor(@ApplicationScope private val appScope: CoroutineScope) {

    companion object {
        private const val FPS = 5

    }

    private val downloadManager by lazy {
        Utils.getApp().getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
    }

    fun startDownload(url: String, fileName: String,setNotificationVisibility:Int = DownloadManager.Request.VISIBILITY_HIDDEN): StateFlow<Result<Uri>> {
        val _downloadProgress = MutableStateFlow<Result<Uri>>(Result.Loading())

        val request = DownloadManager.Request(Uri.parse(url))
        request.apply {
            // 存储到应用私有目录（无需权限）
            setDestinationInExternalFilesDir(
                Utils.getApp(),
                Environment.DIRECTORY_DOWNLOADS,
                fileName
            )
            setAllowedOverMetered(true)
            setNotificationVisibility(setNotificationVisibility)
        }
        // 发起下载
        val downloadId = downloadManager.enqueue(request)
        fun startPollingProgress() {
            appScope.launch {
                while (isActive) {
                    val result = withContext(Dispatchers.IO) {
                        getDownloadProgress(downloadId)
                    }
                    _downloadProgress.value = result
                    if (!result.isLoading()) {
                        break
                    }
                    delay(1000L / FPS)
                }
            }
        }
        startPollingProgress()
        return _downloadProgress
    }

    // 查询下载进度
    private fun getDownloadProgress(downloadId: Long): Result<Uri> {
        return try {

            val query = DownloadManager.Query().setFilterById(downloadId)
            downloadManager.query(query).use { cursor ->
                if (cursor.moveToFirst()) {
                    val statusCode =
                        cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS))
                    val uriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                    var fileUri:Uri? = null
                    if (uriIndex != -1) {
                        val localUri = cursor.getString(uriIndex)
                        if (localUri != null) {
                             fileUri = Uri.parse(localUri)
                        }
                    }
                    val bytesDownloaded = cursor.getLong(
                        cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
                    )
                    val bytesTotal = cursor.getLong(
                        cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)
                    )
                    val progress =
                        if (bytesTotal == 0L) 0 else (bytesDownloaded * 100 / bytesTotal).toInt()
                    when (statusCode) {
                        DownloadManager.STATUS_SUCCESSFUL -> {
                            if (fileUri != null){
                                Result.Success(fileUri)
                            }else{
                                Result.Error(PetivityThrowable())
                            }
                        }
                        DownloadManager.STATUS_FAILED -> Result.Error(PetivityThrowable())
                        else -> Result.Loading(progress = progress)
                    }
                } else {
                    Result.Error(PetivityThrowable())
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Result.Error(e)
        }
    }
}