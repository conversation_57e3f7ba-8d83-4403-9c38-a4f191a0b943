package com.ruiheng.xmuse.core.data.repository.muse.interaxon

import com.choosemuse.libmuse.MuseModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import javax.inject.Inject
import javax.inject.Singleton


interface SignalQualityProcessor {

    val drlRefGood: BehaviorSubject<Boolean>
    val eegConveyorValues: BehaviorSubject<Map<SqcSensor, SignalQuality>>
    val eegSensorIndicatorValues: BehaviorSubject<Map<SqcSensor, Double>>
    val ppgConveyorValue: BehaviorSubject<Boolean>
    val ppgSensorIndicatorValue: BehaviorSubject<Boolean>
    val heartbeatDetected: BehaviorSubject<Boolean>
    val fnirsSensorIndicatorValues: BehaviorSubject<Map<SqcSensor, Double>>

    fun startProcessing()
    fun stopProcessing()
}

@Singleton
class BusymindSignalQualityProcessor @Inject constructor(
    private val busymind: BusymindMonitor
) : SignalQualityProcessor {

    override val drlRefGood = BehaviorSubject.create<Boolean>()
    override val eegConveyorValues = BehaviorSubject.create<Map<SqcSensor, SignalQuality>>()
    override val eegSensorIndicatorValues = BehaviorSubject.create<Map<SqcSensor, Double>>()
    override val ppgConveyorValue = BehaviorSubject.create<Boolean>()
    override val ppgSensorIndicatorValue = BehaviorSubject.create<Boolean>()
    override val heartbeatDetected = BehaviorSubject.create<Boolean>()
    override val fnirsSensorIndicatorValues = BehaviorSubject.create<Map<SqcSensor, Double>>()

    var debugOn = false

    private var disposable: CompositeDisposable? = null

    var lenientSqcEnabled = false
    var museModel: MuseModel? = null

    private fun toSqcSensor(index: Int): SqcSensor? {
        return when (index) {
            0 -> SqcSensor.EEG_LEFT_BACK
            1 -> SqcSensor.EEG_LEFT_FRONT
            2 -> SqcSensor.EEG_RIGHT_FRONT
            3 -> SqcSensor.EEG_RIGHT_BACK
            else -> null
        }
    }

    private fun toSignalQuality(value: Int): SignalQuality {
        return when (value) {
            1 -> SignalQuality.GOOD
            2 -> SignalQuality.MEDIOCRE
            else -> SignalQuality.BAD
        }
    }

    override fun startProcessing() {
        if (disposable != null && disposable?.isDisposed == false)return
        busymind.nfbPackets
            .subscribe {
                if (debugOn) return@subscribe

                drlRefGood.onNext(it.isDrlGood)

                eegSensorIndicatorValues.onNext(
                    it.noisePerChannelAlpha.mapIndexed { index, value ->
                        Pair(toSqcSensor(index), value)
                    }.filter { (sensor, _) ->
                        sensor != null
                    }.associate { (sensor, value) ->
                        Pair(sensor!!, value)
                    })

                if (it.isProcOptics) {
                    fnirsSensorIndicatorValues.onNext(
                        mapOf(
                            SqcSensor.FNIRS_LEFT to it.isFnirsGood[0],
                            SqcSensor.FNIRS_RIGHT to it.isFnirsGood[1]
                        )
                    )
                }

                val noisePerChannel =
                    if (lenientSqcEnabled && museModel == MuseModel.MS_03) {
                        it.noisePerChannel
                    } else {
                        it.noisePerChannelMirrored
                    }

                eegConveyorValues.onNext(
                    noisePerChannel.mapIndexed { index, value ->
                        Pair(toSqcSensor(index), toSignalQuality(value))
                    }.filter { (sensor, _) ->
                        sensor != null
                    }.associate { (sensor, value) ->
                        Pair(sensor!!, value)
                    })

                ppgSensorIndicatorValue.onNext(it.isPpgGood)

                if (it.isHeartUpdated) {
                    val heartbeatDetected = it.contiguousHeartBeatCount > 0
                    ppgConveyorValue.onNext(heartbeatDetected)

                    if (it.heartBeatDetected && it.isPpgGood) {
                        this.heartbeatDetected.onNext(true)
                    }
                }
            }.let { disposable?.add(it) }
    }

    override fun stopProcessing() {
        disposable?.clear()
        disposable = null
    }

}