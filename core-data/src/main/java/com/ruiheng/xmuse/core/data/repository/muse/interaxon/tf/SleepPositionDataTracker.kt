package com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf

import com.choosemuse.libmuse.internal.Busymind
import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.TimestampedData
import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionConsts
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SleepPosition
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.DataTrackingUtils.Companion.createGaplessTimeseries
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.MuseDataUtils
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepPositionsUserSession
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SleepPositionDataTracker
@Inject constructor(
    private val tfliteFactory: TfliteFactory
) {

    companion object {
        val FALLBACK_INITIAL_SLEEP_POSITION = SleepPosition.SITTING.ordinal.toDouble()

        fun extractSleepPositionsUserSession(
            timeseries: ArrayList<Double>,
            completedSeconds: Long
        ): SleepPositionsUserSession {

            return MuseDataUtils.calcSecondsPerRange(
                timeseries, completedSeconds,
                arrayListOf(
                    SessionConsts.SLEEP_POSITIONS_MIN,
                    SessionConsts.SLEEP_POSITIONS_FRONT,
                    SessionConsts.SLEEP_POSITIONS_RIGHT,
                    SessionConsts.SLEEP_POSITIONS_BACK,
                    SessionConsts.SLEEP_POSITIONS_LEFT,
                    SessionConsts.SLEEP_POSITIONS_MAX
                )
            ).let { seconds ->
                val percentages = MuseDataUtils.calcPercentages(seconds)
                SleepPositionsUserSession(
                    seconds[SleepPosition.SITTING.ordinal].toInt(),
                    seconds[SleepPosition.LEFT.ordinal].toInt(),
                    seconds[SleepPosition.BACK.ordinal].toInt(),
                    seconds[SleepPosition.RIGHT.ordinal].toInt(),
                    seconds[SleepPosition.FRONT.ordinal].toInt(),
                    percentages.maxOrNull()?.toInt()
                )
            }
        }
    }

    data class SleepPositionData(
        val timeseries: List<Float>,
        val stats: SleepPositionsUserSession
    )

    private var sleepPositionsPredictions: TimestampedData? = null

    // DataWindowBuffer is thread safe
    private val accBuffer: DataWindowBuffer by lazy {
        tfliteFactory.createAccBuffer()
    }

    private var disposeable: Disposable? = null

    fun startTracking(inputData: Observable<Pair<Float, List<Float>>>) {
        inputData.subscribe { (elapsedTime, data) ->
            accBuffer.updateBuffer(elapsedTime, ArrayList(data))
        }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun process(
        now: Float,
        connectTimes: List<Float>,
        disconnectTimes: List<Float>,
        completedSeconds: Long
    ):SleepPositionData? {
        val accWindows = accBuffer.flushWindows()
        sleepPositionsPredictions = tfliteFactory.createSleepPositionsClassifier()
            .processData(accWindows)?.let {
                sleepPositionsPredictions?.append(it) ?: it
            } ?: sleepPositionsPredictions
         return sleepPositionsPredictions?.let { data ->
            val sleepPositions =
                tfliteFactory.createPresenter(TflitePresenterModel.SLEEP_POSITION_GRAPH)
                    .setNowTimestamp(now)
                    .setTimestampedData(data)
                    .setReconnectTimestamps(ArrayList(connectTimes))
                    .setDisconnectTimestamps(ArrayList(disconnectTimes))
                    .processData()
             Timber.d("Automatic:SleepPositionPredictions:${sleepPositions?.toString()}")
            SleepPositionData(
                timeseries = sleepPositions,
                extractSleepPositionsUserSession(
                    createGaplessTimeseries(
                        FALLBACK_INITIAL_SLEEP_POSITION,
                        ArrayList(sleepPositions.map(Float::toDouble))
                    ),
                    completedSeconds
                )
            )
        }
    }
}