// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from session.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;

public final class BodyData {


    /*package*/ final ArrayList<Double> timeseries;

    /*package*/ final int relaxedSeconds;

    /*package*/ final int activeSeconds;

    /*package*/ final int relaxedPercentage;

    public BodyData(
            ArrayList<Double> timeseries,
            int relaxedSeconds,
            int activeSeconds,
            int relaxedPercentage) {
        this.timeseries = timeseries;
        this.relaxedSeconds = relaxedSeconds;
        this.activeSeconds = activeSeconds;
        this.relaxedPercentage = relaxedPercentage;
    }

    public ArrayList<Double> getTimeseries() {
        return timeseries;
    }

    public int getRelaxedSeconds() {
        return relaxedSeconds;
    }

    public int getActiveSeconds() {
        return activeSeconds;
    }

    public int getRelaxedPercentage() {
        return relaxedPercentage;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "BodyData{" +
                "timeseries=" + timeseries +
                "," + "relaxedSeconds=" + relaxedSeconds +
                "," + "activeSeconds=" + activeSeconds +
                "," + "relaxedPercentage=" + relaxedPercentage +
        "}";
    }

}
