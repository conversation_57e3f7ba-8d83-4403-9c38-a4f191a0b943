package com.ruiheng.xmuse.core.data.repository.muse.interaxon

import java.util.*

class SignalQualityQueue(initialArray: List<SignalQuality>) {

    var elements: LinkedList<SignalQuality> = LinkedList(initialArray)
    fun isEmpty(): Boolean = elements.isEmpty()
    fun count(): Int = elements.size
    fun peek(): SignalQuality? = if (!isEmpty()) elements[elements.lastIndex] else null
    fun enqueue(item: SignalQuality) {
        with(elements) {
            if (!isEmpty()) {
                removeLast()
            }
            addFirst(item)
        }
    }

    override fun toString(): String = elements.toString()
}

class SignalValueQueue(initialArray: List<Float>) {

    var elements: LinkedList<Float> = LinkedList(initialArray)
    fun isEmpty(): Boolean = elements.isEmpty()
    fun count(): Int = elements.size
    fun peek(): Float? = if (!isEmpty()) elements[elements.lastIndex] else null
    fun enqueue(item: Float) {
        with(elements) {
            if (!isEmpty()) {
                removeFirst()
            }
            add(item)
//            addFirst(item)
        }
    }

    override fun toString(): String = elements.toString()
}