package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.DataTrackingConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.scopes.ActivityRetainedScoped
import dagger.hilt.components.SingletonComponent

//@Module
//@InstallIn(SingletonComponent::class)
//open class SessionModule {
//
//    class WithConfig(private val config: DataTrackingConfig?) : SessionModule() {
//
//        override fun provideDataTrackingConfig(): DataTrackingConfig =
//            config ?: super.provideDataTrackingConfig()
//    }
//
//    @Provides
//    @ActivityRetainedScoped
//    open fun provideDataTrackingConfig(): DataTrackingConfig {
//        return DataTrackingConfig.createDefault()
//    }
//}