package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.internal.BusymindMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionDurationTracker
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.DataTrackingConfig
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.disposables.CompositeDisposable
import javax.inject.Inject

/**
 * Tracks birds, points, and calm seconds
 *
 *
 */
@ActivityRetainedScoped
class BusymindStatsDataTracker(
    private val museDeviceSelector: MuseConnector,
    private val busymind: BusymindMonitor,
    private val durationTracker: SessionDurationTracker,
//    private val legacyBusymindDataTracker: BusymindDataTracker,
    private val initialBusymindMode: BusymindMode
) {


    companion object {
        private fun parseInitialBusymindMode(dataTrackingConfig: DataTrackingConfig?): BusymindMode {
            val mode = dataTrackingConfig?.sessionInfo?.fmodContent?.config?.initialBusymindMode
            return if (mode != null) {
                when (mode) {
                    "idle" -> BusymindMode.IDLE
                    "calibration" -> BusymindMode.CALIBRATION
                    "feedback" -> BusymindMode.FEEDBACK
                    "signalTest" -> BusymindMode.SIGNAL_TEST
                    else -> BusymindMode.FEEDBACK
                }
            } else {
                BusymindMode.FEEDBACK
            }
        }
    }

    @Inject
    constructor(
        museDeviceSelector: MuseConnector,
        busymind: BusymindMonitor,
        durationTracker: SessionDurationTracker,
//        legacyBusymindDataTracker: BusymindDataTracker,
    ) : this(
        museDeviceSelector,
        busymind,
        durationTracker,
//        legacyBusymindDataTracker,
        parseInitialBusymindMode(SessionManager.dataTrackingConfig)
    )

    private val disposableBag = CompositeDisposable()
    private var pausedBusymindMode: BusymindMode? = null

//    val sessionData: SessionData
//        get() = legacyBusymindDataTracker.sessionData
//    val mindData: MindData
//        get() = legacyBusymindDataTracker.mindData
//    val bodyData: BodyData
//        get() = legacyBusymindDataTracker.bodyData
//    val heartData: HeartData
//        get() = legacyBusymindDataTracker.heartData
//    val breathData: BreathData
//        get() = legacyBusymindDataTracker.breathData

    var busymindMode
        set(value) {
            busymind.busymindMode = value
        }
        get() = busymind.busymindMode

    fun setInitialBusymindMode() {
        busymind.busymindMode = initialBusymindMode
    }

    fun startTracking() {
        //监听计时器更新
        durationTracker.completedSeconds
            .subscribe {
//                val hasConnectedMuse = museDeviceSelector._connectMuseLiveData.value?.data?.connectionState == ConnectionState.CONNECTED
                //调用busymindDataTracker 增加时间统计--同时底层busymind_data_tracker.cpp文件将进行数据统计
//                legacyBusymindDataTracker.incrementCompletedSeconds(
//                    hasConnectedMuse  && pausedBusymindMode == null
//                )
            }.let { disposableBag.add(it) }
    }

    fun pauseTracking() {
        pausedBusymindMode = busymind.busymindMode
        busymind.busymindMode = BusymindMode.IDLE
    }

    fun resumeTracking() {
        pausedBusymindMode?.let { mode ->
            pausedBusymindMode = null
            busymind.busymindMode = mode
        }
    }

    fun stopTracking() {
        busymind.busymindMode = BusymindMode.IDLE
        disposableBag.clear()
    }

}