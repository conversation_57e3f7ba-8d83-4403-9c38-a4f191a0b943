//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking
//
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.neurofeedback.NeurofeedbackDataTracker
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionDurationTracker
//import io.reactivex.rxjava3.core.Single
//import io.realm.RealmList
//import org.threeten.bp.OffsetDateTime
//import javax.inject.Inject
//
//class ProcessSessionProgressJobFactory
//@Inject constructor(
//    private val durationTracker: SessionDurationTracker,
//    private val neurofeedbackDataTracker: NeurofeedbackDataTracker,
//    private val busymindDataTracker: BusymindStatsDataTracker,
//    private val config: DataTrackingConfig,
//    private val trendsRepo: TrendsRepository
//) {
//
//    fun createProcessSessionProgress(disableSleepBusymindDataUpdates: Boolean): Single<SessionUpdate> {
//        val session = createBusymindSessionUpdate()
//        return if (config.muse == null) {
//            Single.just(session)
//                .map { updateWithAwards(it) }
//        } else if (config.resultsMode == ResultsMode.MEDITATE) {
//            neurofeedbackDataTracker.createProcessMeditateDataObservable(
//                session,
//                trendsRepo.alphaPowerHistoryDateAscending.map { history ->
//                    history
//                        .filter { it.startDatetimeLocalWithTimezone != config.rawStartDatetimeLocalWithTimezone }
//                        .asReversed()
//                        .mapNotNull { it.alphaPower }
//                }.toObservable()
//            ).map { updateWithAwards(it) }
//                .flatMap {
//                    CognitivePerformance
//                        .updateSessionWithPrevAlphaPeakValuesAndInsights(
//                            trendsRepo.alphaPeakHistoryDateAscending.toObservable(),
//                            it,
//                            config.rawStartDatetimeLocalWithTimezone
//                        )
//                }
//        } else {
//            neurofeedbackDataTracker.createProcessSleepDataObservable(
//                session,
//                disableSleepBusymindDataUpdates
//            )
//        }.map { update ->
//            update.apply {
//                timeSeries?.apply {
//                    mindCalmPerSecond = null
//                    bodyMovementPerSecond = null
//                    heartRatePerSecond = null
//                    breathSyncPerSecond = null
//                    deepSleepIntensity = null
//                    sleepPositions = null
//                    sleepStages = null
//                }
//            }
//        }
//    }
//
//    private fun createBusymindSessionUpdate(): SessionUpdate {
//        val data = busymindDataTracker.sessionData
//        val mind = busymindDataTracker.mindData
//        val body = busymindDataTracker.bodyData
//        val heart = busymindDataTracker.heartData
//        val breath = busymindDataTracker.breathData
//
//        return SessionUpdate(
//            rawStartDataTrackingDatetimeLocalWithTimezone = durationTracker.startTime.toString(),
//            rawEndDataTrackingDatetimeLocalWithTimezone = (
//                    durationTracker.endTime ?: OffsetDateTime.now()).toString(),
//            completedSeconds = data.completedSeconds,
//            mind = MindUserSession(
//                calmPercentage = mind.calmPercentage,
//                calmSeconds = mind.calmSeconds,
//                neutralSeconds = mind.neutralSeconds,
//                activeSeconds = mind.activeSeconds
//            ),
//            heart = HeartUserSession(
//                lowRatePercentage = heart.lowHrPercentage,
//                historicalAbsMinRate = config.heart.historicalAbsMinRate,
//                historicalAbsMaxRate = config.heart.historicalAbsMaxRate,
//                historicalAvgMinRate = config.heart.historicalAvgMinRate,
//                historicalAvgMaxRate = config.heart.historicalAvgMaxRate
//            ),
//            breath = BreathUserSession(
//                highSyncPercentage = breath.highBreathSyncPercentage,
//                highHarmonySeconds = breath.highBreathSyncSeconds,
//                mediumHarmonySeconds = breath.mediumBreathSyncSeconds,
//                lowHarmonySeconds = breath.lowBreathSyncSeconds
//            ),
//            body = BodyUserSession(
//                relaxedPercentage = body.relaxedPercentage,
//                relaxedSeconds = body.relaxedSeconds,
//                activeSeconds = body.activeSeconds
//            ),
//            timeSeries = UserSessionTimeSeries(
//                mindCalmPerSecond = RealmList(*mind.timeseries.toTypedArray()),
//                heartRatePerSecond = RealmList(*heart.timeseries.toTypedArray()),
//                bodyMovementPerSecond = RealmList(*body.timeseries.toTypedArray()),
//                breathSyncPerSecond = RealmList(*breath.timeseries.toTypedArray()),
//                birdTimestampsSecondsSinceStart = RealmList(*data.birdTimes.toTypedArray()),
//                recoveryTimestampsSecondsSinceStart = RealmList(*data.recoveryTimes.toTypedArray())
//            ),
//            stats = UserSessionStats(
//                points = if (config.sessionType == SessionType.MIND) {
//                    mind.calmPoints + mind.neutralPoints
//                } else {
//                    data.totalPoints
//                },
//                birdCount = data.birdCount,
//                recoveryCount = data.recoveryCount
//            )
//        )
//    }
//
//    private fun updateWithAwards(update: SessionUpdate): SessionUpdate {
//        val awards = SessionAwardsCalculator.calculate(
//            config.sessionType,
//            update.completedSeconds,
//            ArrayList(update.timeSeries?.mindCalmDownSampled ?: ArrayList()),
//            update.stats?.points ?: 0,
//            update.stats?.birdCount ?: 0,
//            update.mind?.calmPercentage ?: 0,
//            update.mind?.calmSeconds ?: 0
//        )
//        if (update.stats == null) {
//            update.stats = UserSessionStats()
//        }
//        update.stats?.awards = RealmList(*awards.toTypedArray())
//        return update
//    }
//}