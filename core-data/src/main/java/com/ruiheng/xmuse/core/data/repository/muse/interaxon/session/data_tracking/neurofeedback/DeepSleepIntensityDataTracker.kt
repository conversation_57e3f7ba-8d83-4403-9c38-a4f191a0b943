package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.TfliteModelConsts
import com.choosemuse.libmuse.internal.TimestampedData
import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import timber.log.Timber

class DeepSleepIntensityDataTracker(
    private val tfliteFactory: TfliteFactory,
    val useLegacyDsi: Boolean = false
) {

    data class DeepSleepIntensityData(
        val timeseries: List<Float>,
        val points: Int?
    )

    private var deepSleepIntensityPredictions: TimestampedData? = null

    // Thread safe
    private val deepSleepIntensityEegBuffer: DataWindowBuffer by lazy {
        tfliteFactory.createDeepSleepIntensityEegBuffer(useLegacyDsi)
    }

    private var disposeable: Disposable? = null

    fun startTracking(inputData: Observable<Pair<Float, List<Float>>>) {
        inputData.subscribe { (elapsedTime, data) ->
            deepSleepIntensityEegBuffer.updateBuffer(elapsedTime, ArrayList(data))
        }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun process(
        now: Float,
        connectTimes: List<Float>,
        disconnectTimes: List<Float>
    ): DeepSleepIntensityData? {
        val dsiEegWindows = deepSleepIntensityEegBuffer.flushWindows()

        deepSleepIntensityPredictions = deepSleepIntensityEegBuffer.let { _ ->
//            if (!useLegacyDsi) {
//                NanInterpolateUtils.processNanInterpolate(
//                    dsiEegWindows,
//                    TfliteModelConsts.EEG_LABEL,
//                    TfliteModelConsts.EEG_LABEL
//                )
//                FftUtils.processFft(
//                    dsiEegWindows,
//                    TfliteModelConsts.EEG_LABEL,
//                    TfliteModelConsts.FFT_SQC_LABEL
//                )
//            }
            tfliteFactory.createDeepSleepIntensityClassifier(useLegacyDsi)
                .processData(dsiEegWindows)?.let {
                    deepSleepIntensityPredictions?.append(it) ?: it
                } ?: deepSleepIntensityPredictions
        }
        Timber.d("Automatic:DeepSleepIntensity:${deepSleepIntensityPredictions?.toString()}")

        return deepSleepIntensityPredictions?.let { data ->
            val deepSleepIntensity =
                tfliteFactory.createPresenter(
                    if (useLegacyDsi) {
                        TflitePresenterModel.LEGACY_DEEP_SLEEP_INTENSITY_GRAPH
                    } else {
                        TflitePresenterModel.DEEP_SLEEP_INTENSITY_GRAPH
                    }
                ).setNowTimestamp(now)
                    .setTimestampedData(data)
                    .setReconnectTimestamps(ArrayList(connectTimes))
                    .setDisconnectTimestamps(ArrayList(disconnectTimes))
                    .processData()
            Timber.d("Automatic:DeepSleepIntensity:${deepSleepIntensity?.toString()}")

            DeepSleepIntensityData(
                timeseries = deepSleepIntensity,
                points = tfliteFactory.createPresenter(
                    if (useLegacyDsi) {
                        TflitePresenterModel.LEGACY_DEEP_SLEEP_INTENSITY_SCORE
                    } else {
                        TflitePresenterModel.DEEP_SLEEP_INTENSITY_SCORE
                    }
                ).setNowTimestamp(now)
                    .setTimestampedData(data)
                    .setReconnectTimestamps(ArrayList(connectTimes))
                    .setDisconnectTimestamps(ArrayList(disconnectTimes))
                    .processData().firstOrNull()?.toInt()
            )
        }
    }
}