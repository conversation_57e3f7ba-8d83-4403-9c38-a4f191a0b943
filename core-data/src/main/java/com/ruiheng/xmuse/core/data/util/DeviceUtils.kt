package com.ruiheng.xmuse.core.data.util

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import com.blankj.utilcode.util.EncryptUtils
import java.net.NetworkInterface
import java.security.MessageDigest

object DeviceUtils {

    /**
     * 获取设备唯一标识符（兼容各 Android 版本）
     * Android 10+ 使用 ANDROID_ID，Android 10- 使用 MAC 地址
     */
    fun getDeviceId(context: Context): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+：使用 ANDROID_ID
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        } else {
            // Android 10-：尝试获取 MAC 地址
            getMacAddress(context)
        }
    }

    /**
     * 获取 MAC 地址（Android 10 以下有效）
     */
    private fun getMacAddress(context: Context): String {
        return try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            interfaces.toList().firstOrNull { it.name.equals("wlan0", ignoreCase = true) }
                ?.hardwareAddress
                ?.let { macBytes ->
                    macBytes.joinToString(separator = ":") { byte ->
                        String.format("%02X", byte)
                    }
                } ?: "02:00:00:00:00:00"
        } catch (e: Exception) {
            e.printStackTrace()
            "02:00:00:00:00:00"
        }
    }

    /**
     * 生成设备唯一标识符（基于 ANDROID_ID 和 Build.SERIAL 的 MD5 值）
     */
    @SuppressLint("HardwareIds")
    @JvmStatic
    fun getUniqueId(context: Context): String {
        val androidID = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        val id = androidID + Build.SERIAL

        return try {
            EncryptUtils.encryptMD5ToString(id)
        } catch (e: Exception) {
            e.printStackTrace()
            id // 失败时返回原始 ID 拼接
        }
    }
}