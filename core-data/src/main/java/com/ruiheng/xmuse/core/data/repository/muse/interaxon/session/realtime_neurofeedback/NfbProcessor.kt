//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.realtime_neurofeedback
//
//import com.choosemuse.libmuse.MuseDataPacketType
//import com.choosemuse.libmuse.internal.Busymind
//import com.choosemuse.libmuse.internal.BusymindMode
//import com.choosemuse.libmuse.internal.SignalQualityState
//import com.choosemuse.libmuse.internal.VolumeArgs
//import com.google.gson.Gson
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.PlaybackState
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.FmodContent
//import io.reactivex.rxjava3.core.Observable
//import org.threeten.bp.Instant
//import java.util.EnumMap
//import java.util.concurrent.TimeUnit
//import io.reactivex.rxjava3.subjects.BehaviorSubject
//import kotlin.math.roundToInt
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlaybackCommand
//import io.reactivex.rxjava3.core.Scheduler
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf.TfliteFactory
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.BusymindSetting
//import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
//import io.reactivex.rxjava3.disposables.CompositeDisposable
//
//class NfbProcessor(
//    private val fmodClock: Observable<Double>, // Elapsed seconds
//    private val busymind: Busymind,
//    val tfliteProcessors: List<Pair<TfliteProcessor, TfliteProcessorProperties>>,
//    private val fmodParameterMapper: FmodParameterMapper?,
//    private val pureDataProcessor: PureDataProcessor?
//) {
//
//    companion object {
//        const val FMOD_CLOCK_PERIOD_MS = 1000L / 60
//        private fun toMuseDataPacketType(hopInputType: String): MuseDataPacketType? {
//            return when (hopInputType) {
//                "eeg" -> MuseDataPacketType.EEG
//                "acc" -> MuseDataPacketType.ACCELEROMETER
//                "ppg" -> MuseDataPacketType.PPG
//                "gyro" -> MuseDataPacketType.GYRO
//                "therm" -> MuseDataPacketType.THERMISTOR
//                else -> null
//            }
//        }
//    }
//
//    constructor(
//        busymind: Busymind,
//        tfliteFactory: TfliteFactory,
//        fmodContent: FmodContent?,
//        scheduler: Scheduler
//    ) : this(
//        Observable.create<Double> { emitter ->
//            val startEpochTime = Instant.now().toEpochMilli()
//            val disposable = scheduler.schedulePeriodicallyDirect({
//                emitter.onNext((Instant.now().toEpochMilli() - startEpochTime) / 1000.0)
//            }, FMOD_CLOCK_PERIOD_MS, FMOD_CLOCK_PERIOD_MS, TimeUnit.MILLISECONDS)
//            emitter.setDisposable(disposable)
//        },
//        busymind,
//        when (val models = fmodContent?.config?.realTimeModels) {
//            null -> emptyList()
//            else -> {
//                models.mapNotNull { model ->
//                    val packetType =
//                        toMuseDataPacketType(model.hopInputType)
//                            ?: return@mapNotNull null
//                    val tfliteProcessor =
//                        TfliteProcessor.createFromConfig(tfliteFactory, model, scheduler)
//                            ?: return@mapNotNull null
//                    Pair(
//                        tfliteProcessor,
//                        TfliteProcessorProperties(model.fmodParameters, packetType)
//                    )
//                }
//            }
//        },
//        when (val fmodMap = fmodContent?.config?.fmodMap) {
//            null -> null
//            else -> FmodParameterMapper.create(fmodMap)
//        },
//        when (val pureData = fmodContent?.pureDataFilename) {
//            null -> null
//            else -> PureDataProcessor.create(fmodContent.absoluteDirectoryPath, pureData)
//        }
//    )
//
//    data class TfliteProcessorProperties(
//        val fmodParameters: ArrayList<Int>,
//        val museDataPacketType: MuseDataPacketType
//    )
//
//    val fmodParameters: Observable<ArrayList<Float>>
//        get() = mutableFmodParameters.observeOn(AndroidSchedulers.mainThread())
//    private val mutableFmodParameters = BehaviorSubject.create<ArrayList<Float>>()
//
//    val message: Observable<String>
//        get() = pdMessage
//    private val pdMessage = BehaviorSubject.create<String>()
//
//    val command: Observable<PlaybackCommand>
//        get() = mutableCommand.observeOn(AndroidSchedulers.mainThread())
//    private val mutableCommand = BehaviorSubject.create<PlaybackCommand>()
//
//    private var disposable = CompositeDisposable()
//    private var active = false
//
//    fun activate() {
//        if (active) {
//            return
//        }
//
//        checkResult(pureDataProcessor?.load(), "Failed to load")
//        arrayOf(
//            PureDataProcessor.MESSAGE_OUT,
//            PureDataProcessor.FMOD_PARAMS_OUT,
//            PureDataProcessor.PLAYBACK_STATE_CMD_OUT,
//            PureDataProcessor.BUSYMIND_MODE_CMD_OUT
//        ).forEach { label ->
//            checkResult(pureDataProcessor?.bindOutput(label), "Failed to bind $label")
//        }
//
//        fmodClock
//            .map { busymind.processElapsedTime(it) }
//            .map { checkInvalidValues(it) }
//            .map { fmodParameterMapper?.updateParameters(it) ?: it }
//            .map {
//                val update =
//                    pureDataProcessor?.processData(PureDataProcessor.BM_TF_INPUT_IN, it)
//                if (update != null) {
//                    processUpdate(update)
//                    update.data[PureDataProcessor.FMOD_PARAMS_OUT] ?: it
//                } else {
//                    it
//                }
//            }
//            .subscribe { mutableFmodParameters.onNext(it) }
//            .let { disposable.add(it) }
//
//        for ((tflite, props) in tfliteProcessors) {
//            tflite.output
//                .subscribe { fmodParameterMapper?.setFmodParameters(props.fmodParameters, it) }
//                .let { disposable.add(it) }
//            tflite.activate()
//        }
//        active = true
//    }
//
//    private var loggedNonFatal = false
//
//    private fun checkInvalidValues(fmodParameters: ArrayList<Float>): ArrayList<Float> {
//        return ArrayList(fmodParameters.mapIndexed { index, value ->
//            if (!value.isFinite()) {
//                if (!loggedNonFatal) {
//                    val indices = fmodParameters.indices.filter { !fmodParameters[it].isFinite() }
//                    val json = Gson().toJson(indices)
//                    logNonFatal(
//                        IllegalArgumentException("FMOD update has non-finite value"),
//                        mapOf("indices" to json)
//                    )
//                    loggedNonFatal = true
//                }
//                -1.0f
//            } else {
//                value
//            }
//        })
//    }
//
//    private fun checkResult(result: Boolean?, message: String) {
//        if (result == false) {
//            logWarn("NfbProcessor", message)
//        }
//    }
//
//    private fun processUpdate(update: PureDataUpdate) {
//        for ((key, values) in update.data) {
//            val cmdIndex = values.getOrNull(0)?.roundToInt() ?: continue
//            when (key) {
//                PureDataProcessor.PLAYBACK_STATE_CMD_OUT ->
//                    PlaybackCommand.entries.getOrNull(cmdIndex)?.let { mutableCommand.onNext(it) }
//
//                PureDataProcessor.BUSYMIND_MODE_CMD_OUT ->
//                    BusymindMode.entries.getOrNull(cmdIndex)?.let { busymind.setBusymindMode(it) }
//            }
//        }
//
//        for ((key, value) in update.messages) {
//            if (key == PureDataProcessor.MESSAGE_OUT) {
//                pdMessage.onNext(value)
//            }
//        }
//    }
//
//    fun deactivate() {
//        disposable.clear()
//        tfliteProcessors.forEach { it.first.deactivate() }
//        pureDataProcessor?.unload()
//        active = false
//    }
//
//    fun addData(timestamp: Float, data: ArrayList<Float>, dataType: MuseDataPacketType) {
//        tfliteProcessors.forEach { (tflite, props) ->
//            if (props.museDataPacketType == dataType) {
//                tflite.addData(timestamp, data)
//            }
//        }
//    }
//
//    fun setFmodParameter(parameterId: Int, value: Float) =
//        fmodParameterMapper?.setFmodParameter(parameterId, value)
//
//    fun setFmodParameters(parameters: Map<Int, Float>) {
//        fmodParameterMapper?.setFmodParameters(
//            ArrayList(parameters.map { it.key }),
//            ArrayList(parameters.map { it.value })
//        )
//    }
//
//    fun setBusymindParameter(index: Int, value: Double) = busymind.setAux(index, value)
//
//    fun setBusymindSetting(setting: BusymindSetting, value: Double) {
//        val volumeArgs = busymind.volume
//        val values = EnumMap<BusymindSetting, Double>(BusymindSetting::class.java)
//        BusymindSetting.entries.forEach {
//            if (it == setting) {
//                values[it] = value
//            } else {
//                values[it] = when (it) {
//                    BusymindSetting.GUIDANCE_SOUNDSCAPE_CROSSFADE -> volumeArgs.guidanceSoundscapeCrossfade
//                    BusymindSetting.FEEDBACK_VOLUME -> volumeArgs.feedbackVolume
//                    BusymindSetting.BIRDS_VOLUME -> volumeArgs.birdsVolume
//                    BusymindSetting.BACKGROUND_VOLUME -> volumeArgs.backgroundVolume
//                    BusymindSetting.GUIDANCE_VOLUME -> volumeArgs.guidanceVolume
//                    BusymindSetting.ALERTS_VOLUME -> volumeArgs.alertsVolume
//                }
//            }
//        }
//        busymind.volume = VolumeArgs(
//            values[BusymindSetting.GUIDANCE_VOLUME]!!,
//            values[BusymindSetting.FEEDBACK_VOLUME]!!,
//            values[BusymindSetting.BIRDS_VOLUME]!!,
//            values[BusymindSetting.BACKGROUND_VOLUME]!!,
//            values[BusymindSetting.ALERTS_VOLUME]!!,
//            volumeArgs.volumeControlOn,
//            values[BusymindSetting.GUIDANCE_SOUNDSCAPE_CROSSFADE]!!
//        )
//    }
//
//    fun getBusymindSetting(setting: BusymindSetting): Double {
//        val volumeArgs = busymind.volume
//        return when (setting) {
//            BusymindSetting.GUIDANCE_SOUNDSCAPE_CROSSFADE -> volumeArgs.guidanceSoundscapeCrossfade
//            BusymindSetting.FEEDBACK_VOLUME -> volumeArgs.feedbackVolume
//            BusymindSetting.BIRDS_VOLUME -> volumeArgs.birdsVolume
//            BusymindSetting.BACKGROUND_VOLUME -> volumeArgs.backgroundVolume
//            BusymindSetting.GUIDANCE_VOLUME -> volumeArgs.guidanceVolume
//            BusymindSetting.ALERTS_VOLUME -> volumeArgs.alertsVolume
//        }
//    }
//
//    fun setPlaybackTime(timeLengthSecs: Float, elapsedSecs: Float) {
//        val update = pureDataProcessor?.processData(
//            PureDataProcessor.PLAYBACK_TIME_IN,
//            arrayListOf(timeLengthSecs, elapsedSecs)
//        ) ?: return
//        processUpdate(update)
//    }
//
//    fun setPlaybackState(state: PlaybackState) {
//        val update = pureDataProcessor?.processData(
//            PureDataProcessor.PLAYBACK_STATE_IN,
//            arrayListOf(state.ordinal.toFloat())
//        ) ?: return
//        processUpdate(update)
//    }
//
//    fun setSignalQualityState(state: SignalQualityState) {
//        busymind.setSignalQualityState(state)
//        fmodParameterMapper?.setFmodParameter(
//            FmodParameterMapper.SIGNAL_QUALITY_PARAM_ID,
//            state.ordinal.toFloat()
//        )
//    }
//}