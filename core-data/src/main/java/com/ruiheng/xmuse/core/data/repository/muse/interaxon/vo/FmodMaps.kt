package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo

class FmodMaps private constructor() {
    companion object {
        private val DEFAULT_FMOD_MAP = listOf(
            0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 30, 31,
            32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43,
            50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 70, 71
        )

//        private val DEFAULT_FMOD_MAP = listOf(
//            300, 301, 302, 303
//        )

        private val FMOD_MAPS = mapOf(
            "body_wind_chimes" to listOf(),
            "breath_46_long_exhale" to listOf(),
            "heart_healing_drum" to listOf(),
            "fresh_air" to listOf(),
            "cosmic_space" to listOf(),
            "night_crickets" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 10, 11,
                30, 42, 43, 70, 71, 50, 52, 53, 51, 55, 56, 57, 58,
                59, 44, 12, 34, 36, 38, 41, 33, 32, 8
            ),
            "night_crickets_music" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 10, 11, 30,
                42, 43, 70, 71, 50, 52, 53, 51, 55, 56, 57, 58,
                59, 44, 12, 34, 36, 38, 41, 33, 32, 8
            ),
            "crystal_cavern" to listOf(),
            "crystal_cavern_music" to listOf(),
            "journey_69_S16" to listOf(0, 1, 2, 3, 4, 5, 6, 7, 30, 35, 32, 10, 50, 42, 43),
            "journey_1_S100" to listOf(0, 1, 2, 3, 4, 5, 6, 7, 10, 50, 42, 43),
            "journey_1_S101" to listOf(0, 1, 2, 3, 4, 5, 6, 7, 10, 50, 42, 43),
            "journey_2_S200" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 55, 56, 57,
                58, 59, 30, 31, 10, 50, 42, 43
            ),
            "journey_62_S62" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 55, 56, 57,
                58, 59, 30, 32, 10, 50, 42, 43
            ),
            "journey_30_S50" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 55, 56, 57,
                58, 59, 30, 32, 10, 50, 42, 43
            ),
            "journey_pianopad" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 30, 44, 128,
                36, 40, 41, 10, 50, 42, 43
            ),
            "journey_heartbeach" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 30, 44, 32, 36,
                40, 41, 10, 50, 42, 43
            ),
            "template_1" to listOf(
                0, 1, 2, 3, 4, 5, 6, 7, 10, 11, 30, 42, 43, 70,
                71, 50, 52, 53, 51, 55, 56, 57, 58, 59, 44, 12, 34, 36, 38, 41, 33, 32
            ),
            "body_wind_chimes_beach" to listOf(),
            "body_wind_chimes_koshi" to listOf(),
            "body_wind_chimes_silent" to listOf(),
            "body_wind_chimes_1" to listOf(),
            "body_wind_chimes_2" to listOf(),
            "body_wind_chimes_3x" to listOf(),
            "heart_healing_drum_beach" to listOf(),
            "heart_healing_drum_cosmic" to listOf(),
            "heart_healing_drum_silent" to listOf(),
            "heart_copy_1" to listOf(),
            "heart_copy_2" to listOf(),
            "breath_combined" to listOf(),
            "breath_combined_waves" to listOf(),
            "breath_combined_silent" to listOf(),
            "breath_box_chime" to listOf(),
            "breath_box_drum" to listOf(),
            "waves_of_breath" to listOf(),
        )

        fun getFmodParameterMap(contentId: String): ArrayList<Int> {
            return ArrayList(FMOD_MAPS[contentId] ?: DEFAULT_FMOD_MAP)
        }
    }
}