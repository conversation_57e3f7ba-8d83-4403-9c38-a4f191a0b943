package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionDurationTracker
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import javax.inject.Inject

@ActivityRetainedScoped
class MaxDataTrackingDurationMonitor
@Inject constructor(
    private val durationTracker: SessionDurationTracker,
//    userPrefs: UserPreferencesRepository
) {

    companion object {
        private const val MAX_DATA_TRACKING_SECONDS = (12.5 * 60 * 60).toInt()
        private const val DEBUG_MAX_DATA_TRACKING_SECONDS = 60
    }

//    private val maxSessionLengthOneMinEnabled = userPrefs.maxSessionLengthOneMinEnabled
private val maxSessionLengthOneMinEnabled = false

    val maxDataTrackingTimeReached: Observable<Boolean>
        get() = mutableMaxDataTrackingTimeReached
    private val mutableMaxDataTrackingTimeReached = BehaviorSubject.create<Boolean>()

    private val disposableBag = CompositeDisposable()

    fun startMonitoring() {
        val maxSeconds = if (maxSessionLengthOneMinEnabled) {
            DEBUG_MAX_DATA_TRACKING_SECONDS
        } else {
            MAX_DATA_TRACKING_SECONDS
        }
        durationTracker.completedSeconds
            .observeOn(AndroidSchedulers.mainThread())
            .map { it >= maxSeconds }
            .distinct()
            .filter { it }
            .subscribe {
                mutableMaxDataTrackingTimeReached.onNext(true)
            }
            .let { disposableBag.add(it) }
    }

    fun stopMonitoring() {
        disposableBag.clear()
    }
}