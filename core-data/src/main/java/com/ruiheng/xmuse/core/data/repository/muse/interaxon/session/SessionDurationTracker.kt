package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.PreciseTimer
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import org.threeten.bp.OffsetDateTime
import javax.inject.Inject

/**
 * 会话时长跟踪器 - 基于 PreciseTimer 实现秒级精度的会话时间管理
 * 负责记录会话的开始/结束时间，并提供完整秒数的进度更新
 * 适用于冥想训练、音频播放等需要精确计时的场景
 */
@ActivityRetainedScoped
class SessionDurationTracker
@Inject constructor(
    private val preciseTimer: PreciseTimer
) {
    //会话开始时间（UTC偏移时间）
    lateinit var startTime: OffsetDateTime
    //会话结束时间（UTC偏移时间），null表示会话未结束
    var endTime: OffsetDateTime? = null

    //已完成的秒数（进度），每次整数秒更新一次
    val completedSeconds = BehaviorSubject.create<Long>()
    val durationTrackingState = preciseTimer.state //会话状态流，直接映射自 PreciseTimer 的状态

    private val disposable = CompositeDisposable() //管理所有订阅，确保资源正确释放

    /**
     * 开始跟踪会话时长
     * 1. 记录当前时间为会话开始时间
     * 2. 启动计时器并订阅毫秒级时间流
     * 3. 将毫秒转换为秒，确保每秒只发布一次更新
     */
    fun startTracking() {
        startTime = OffsetDateTime.now()
        preciseTimer.start()
        // 订阅毫秒级时间流，转换为秒级进度
        preciseTimer.millisElapsed
            .subscribe {
                val elapsedSeconds = it / 1000
                if (elapsedSeconds > 0) {
                    val prevSecs = completedSeconds.value ?: 0
                    // 确保每个完整的秒数只发布一次
                    if (prevSecs != elapsedSeconds) {
                        // 补发中间可能跳过的秒数
                        for (secs in (prevSecs + 1)..elapsedSeconds) {
                            completedSeconds.onNext(secs)
                        }
                    }
                }
            }.let { disposable.add(it) }
    }

    fun stopTracking() {
        disposable.clear()
        preciseTimer.end()
        endTime = OffsetDateTime.now()
    }

    fun pauseTracking() {
        preciseTimer.pause()
    }

    fun resumeTracking() {
        preciseTimer.resume()
    }
}