package com.ruiheng.xmuse.core.data.repository.net

import android.os.Build
import com.blankj.utilcode.util.AppUtils
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.network.model.other.AppVersion
import com.ruiheng.xmuse.core.network.model.other.PlatformAppInfo
import com.ruiheng.xmuse.core.network.other.OtherService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesSettingRepository @Inject constructor(
    private val otherService: OtherService
) {
    /**
     * 获取当前App最新版本
     */
    fun fetchLatestVersion() =
        flow<Result<AppVersion>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(*AppVersion.getLatestVersionParam())
            val response = otherService.getLatestVersionList(requestBody)
            requestCatch(response,
                dataResponse = { versionList ->
                    var latestVersion: AppVersion? = versionList?.maxBy { it.versionNum }
                    if (latestVersion != null) {
                        if ((latestVersion.versionNum) > AppUtils.getAppVersionCode()) {

                        } else {
                            latestVersion = null
                        }
                    }
                    latestVersion
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun fetchPlatformVersionInfo() =
        flow<Result<PlatformAppInfo>> {
            emit(Result.Loading())
            val requestBody =
                generateRequestDataJson(Pair("platformType", Build.BRAND))
            val response = otherService.getPlatformStoreInfo(requestBody)
            requestCatch(response,
                dataResponse = { info ->
                    info
                },
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)
}


