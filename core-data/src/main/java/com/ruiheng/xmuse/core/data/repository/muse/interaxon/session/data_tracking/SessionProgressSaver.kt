package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking

import com.choosemuse.libmuse.MuseFileWriter
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.SessionUpdate
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesSleepRepository
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Scheduler
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.reactivex.rxjava3.subjects.BehaviorSubject
import timber.log.Timber
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * 会话进度保存器 - 管理会话进度的自动保存、缓存和远程同步
 * 支持定时保存进度、缓存最终状态、处理不同类型会话（冥想/睡眠）的更新逻辑
 */
@ActivityRetainedScoped
class SessionProgressSaver(
    private val jobFactory: ProcessSessionProgressJobFactory, // 任务工厂，用于创建进度处理任务
//    private val sessionSynchronizer: UserRemoteSessionSynchronizer, // 任务工厂，用于创建进度处理任务
    private val serialScheduler: Scheduler, // 串行调度器（用于保证任务顺序执行）
    private val sessionUtcTimestamp: Long,// 会话创建时的UTC时间戳
    private val netReSeRepository: NetResourcesSleepRepository
//    private val sessionId: String,// 会话创建时的UTC时间戳
//    featureFlags: PlatformFeatureFlags // 平台功能开关
) {

    companion object {
        const val SESSION_REPORT_PROCESS_INTERVAL_MIN = 10L

        /**
         * 创建定时保存进度的Observable（10分钟间隔）
         * 用于触发 SAVE_PROGRESS 任务
         */
        private val SESSION_REPORT_PROCESS_INTERVALS = Observable.interval(
            SESSION_REPORT_PROCESS_INTERVAL_MIN,
            TimeUnit.SECONDS, //TODO change to minute
            AndroidSchedulers.mainThread()
        ).map { Job.SAVE_PROGRESS }

        /**
         * 创建冥想会话的更新Observable
         * - 当完成会话秒数等于音频长度时触发 CACHE_PROGRESS 任务
         * - 结合定时任务触发 SAVE_PROGRESS 任务
         */
        fun createMeditationSessionUpdateObservable(
            audioLengthSeconds: Long, // 音频总长度（秒）
            completedSessionSeconds: Observable<Long> // 已完成的会话秒数流
        ): Observable<Job> {
            return Observable.merge(
                completedSessionSeconds.filter { it == audioLengthSeconds }
                    .map { Job.CACHE_PROGRESS },
                // 定时触发保存进度
                SESSION_REPORT_PROCESS_INTERVALS
            )
        }

        /**
         * 创建睡眠会话的更新Observable（仅依赖定时任务）
         */
        fun createSleepSessionUpdateObservable(): Observable<Job> {
            return SESSION_REPORT_PROCESS_INTERVALS
        }
    }

    /** 定义需要处理的任务类型 */
    enum class Job {
        SAVE_PROGRESS,   // 保存实时进度
        CACHE_PROGRESS,  // 缓存最终进度（用于会话结束时）
        SAVE_CACHE       // 提交缓存的进度
    }

    @Inject
    constructor(
        jobFactory: ProcessSessionProgressJobFactory,
        netReSeRepository: NetResourcesSleepRepository
//        sessionSynchronizer: UserRemoteSessionSynchronizer,
//        config: DataTrackingConfig,
//        featureFlags: PlatformFeatureFlags
    ) : this(
        jobFactory,
//        sessionSynchronizer,
        Schedulers.from(Executors.newSingleThreadExecutor()),// 单线程执行器
        SessionManager.dataTrackingConfig!!.utcTimestamp,
        netReSeRepository
//        config.sessionId,
//        featureFlags
    )

    private val processingJobDisposables = CompositeDisposable() // 管理任务订阅
    private var automaticSavingDisposables: Disposable? = null // 自动保存任务的订阅

    private var cachedUpdateWillBeAvailable = false // 是否有缓存的更新待提交
    private var cachedUpdate = BehaviorSubject.create<SessionUpdate>() // 缓存的会话更新数据

    val finishedSavingProgress = BehaviorSubject.createDefault(false) // 保存完成状态

    // 功能开关：是否禁用睡眠场景的busymind数据更新
//    private val disableSleepBusymindDataUpdates = featureFlags.isFeatureFlagEnabled(
//        PlatformFeatureFlags.FEATURE_FLAG_DISABLE_SLEEP_BUSYMIND_DATA_UPDATES,
//        false
//    )
    private val disableSleepBusymindDataUpdates = false

    /**
     * 启动自动进度保存
     * @param jobObservable 任务触发源（如定时任务或会话完成事件）
     */
    fun startAutomaticProgressSaving(jobObservable: Observable<Job>) {
        jobObservable
            .subscribe { job ->
                Timber.d("Automatic:startAutomaticProgressSaving")
                queueSaveSessionProgressJob(job, false)
            }.let { automaticSavingDisposables = it }
    }

    /**
     * 停止自动进度保存
     * @param addExtraTime 是否需要额外时间处理（如会话未正常结束）
     */
    fun stopAutomaticProgressSaving(addExtraTime: Boolean) {
        if (automaticSavingDisposables == null) {
            return
        }

        automaticSavingDisposables?.dispose()
        automaticSavingDisposables = null
        queueSaveSessionProgressJob(
            // 根据是否有缓存数据，决定提交 SAVE_CACHE 或 SAVE_PROGRESS 任务
            if (!addExtraTime && cachedUpdateWillBeAvailable) {
                Job.SAVE_CACHE // 提交缓存的进度
            } else {
                Job.SAVE_PROGRESS // 提交缓存的进度
            },
            finalizeProcessing = true
        )
    }

    /**
     * 提交会话进度保存任务
     * @param job 任务类型
     * @param finalizeProcessing 是否为最终处理（结束时清理资源）
     */
    private fun queueSaveSessionProgressJob(job: Job, finalizeProcessing: Boolean) {
        Timber.d("Automatic:queueSaveSessionProgressJob:${job}")
        if (job == Job.CACHE_PROGRESS) {
            cachedUpdateWillBeAvailable = true
        }

//        Analytics.instance.logSessionReportUpdate(
//            sessionId,
//            when (job) {
//                Job.SAVE_PROGRESS -> Analytics.SessionReportUpdate.INITIATE_SAVE_PROGRESS
//                Job.CACHE_PROGRESS -> Analytics.SessionReportUpdate.INITIATE_CACHE_PROGRESS
//                Job.SAVE_CACHE -> Analytics.SessionReportUpdate.INITIATE_SAVE_CACHE
//            }
//        )

        // 根据任务类型获取数据源
        when (job) {
            Job.SAVE_PROGRESS,
            Job.CACHE_PROGRESS -> jobFactory.createProcessSessionProgress(
                disableSleepBusymindDataUpdates && !finalizeProcessing
            ).toObservable()

            Job.SAVE_CACHE -> cachedUpdate
        }.subscribeOn(serialScheduler) // 串行执行（保证顺序）
            .observeOn(AndroidSchedulers.mainThread())
            .doOnComplete {
//                Analytics.instance.logSessionReportUpdate(
//                    sessionId,
//                    Analytics.SessionReportUpdate.SUCCESSFULLY_GENERATED
//                )
            }
            .flatMap {
                when (job) {
                    Job.CACHE_PROGRESS -> {
                        cachedUpdate.onNext(it)
                        Observable.just(Unit)
                    }

                    Job.SAVE_PROGRESS,
                    Job.SAVE_CACHE -> {
                        // 同步到远程服务器
                        val sessionId = SessionManager.dataTrackingConfig?.sessionId ?: ""
                        val sessionUtcTimestamp =
                            SessionManager.dataTrackingConfig?.utcTimestampS ?: ""
                        SessionManager.dataTrackingConfig?.savedSessionUpdate = it
                        netReSeRepository.startRxPushSessionProgress(
                            sessionId,
                            it,
                            sessionUtcTimestamp
                        )
                    }
                }
            }
            .subscribe({
                finishProcessing(finalizeProcessing)
            }, {
                finishProcessing(finalizeProcessing)
//                logNonFatal(it)
            }).let { processingJobDisposables.add(it) }
    }

    private fun finishProcessing(finalizeProcessing: Boolean) {
        if (finalizeProcessing) {
            finishedSavingProgress.onNext(true)
            processingJobDisposables.dispose()
        }
    }
}