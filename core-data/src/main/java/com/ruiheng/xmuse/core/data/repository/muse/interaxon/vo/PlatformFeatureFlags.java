// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import java.util.ArrayList;
import java.util.HashMap;

public abstract class PlatformFeatureFlags {
    public static final String FEATURE_FLAG_SUBSCRIPTION_GROUP = "subscription_group";

    public static final String FEATURE_FLAG_SUBSCRIPTION_GROUP_DEFAULT_VALUE = "Muse Subscription";

    public static final String FEATURE_FLAG_SAAS_PROMO_COUPON = "android_saas_promo_coupon_code";

    public static final String FEATURE_FLAG_SAAS_PROMO_COUPON_DEFAULT = "none";

    public static final String FEATURE_FLAG_MIN_SESSIONS_FOR_GIFT30 = "min_sessions_for_gift30";

    public static final int FEATURE_FLAG_MIN_SESSIONS_FOR_GIFT30_DEFAULT = 3;

    public static final String FEATURE_GIFT_CAMPAIGN = "gift_campaign";

    public static final String FEATURE_FLAG_SHOW_DEBUG_UI = "show_debug_ui";

    public static final String FEATURE_FLAG_INTERNAL_DEV_TOOLS = "internal_dev_tools";

    public static final String FEATURE_FLAG_EXTERNAL_DEV_TOOLS = "external_dev_tools";

    public static final String FEATURE_FLAG_FIRMWARE_UPDATE = "firmware_update";

    public static final String FEATURE_FLAG_DSP_FOR_GEN1 = "dsp_for_gen1";

    public static final String FEATURE_FLAG_DSP_FOR_GEN2 = "dsp_for_gen2";

    public static final String FEATURE_FLAG_DIGITAL_SLEEPING_PILL = "digital_sleeping_pill";

    public static final String FEATURE_FLAG_USE_LEGACY_DEEP_SLEEP_INTENSITY = "use_legacy_deep_sleep_intensity";

    public static final String FEATURE_FLAG_NEW_MEDITATE = "new_meditate";

    public static final String FEATURE_FLAG_ENFORCE_MAX_SPACE_FOR_SESSION_FILES = "enforce_max_space_for_session_files";

    public static final String FEATURE_FLAG_ENFORCE_MAX_SESSION_LENGTH = "enforce_max_session_length";

    public static final String FEATURE_FLAG_VALIDATED_SLEEP_STAGING_ENABLED = "validated_sleep_staging_enabled";

    public static final String FEATURE_FLAG_LENIENT_SQC = "lenient_sqc";

    public static final String FEATURE_FLAG_ALPHA_PEAK_ENABLED = "alpha_peak_enabled";

    public static final String FEATURE_FLAG_ALPHA_POWER_ENABLED = "alpha_power_enabled";

    public static final String FEATURE_FLAG_MUSE_TIME_SYNC_ENABLED = "muse_time_sync_enabled";

    public static final String FEATURE_FLAG_DISABLE_SLEEP_BUSYMIND_DATA_UPDATES = "disable_sleep_busymind_data_updates";

    public static final String FEATURE_FLAG_TODAY_TAB_ENABLED = "today_tab_enabled";

    public abstract void registerAllItems(ArrayList<String> featureFlagList, HashMap<String, String> configuration);

    public abstract boolean isFeatureFlagEnabled(String featureFlagKey, boolean defaultValue);

    public abstract String getFeatureConfiguration(String configurationKey, String defaultValue);

    public abstract void setStringProperty(String propName, String value);

    public abstract void setIntProperty(String propName, int value);

    public abstract void setDoubleProperty(String propName, double value);

    public abstract void setBoolProperty(String propName, boolean value);

    public abstract void setSemverProperty(String propName, String value);
}
