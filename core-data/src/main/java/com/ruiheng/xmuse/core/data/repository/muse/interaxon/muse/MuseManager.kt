package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseModel
import com.choosemuse.libmuse.internal.Busymind
import com.choosemuse.libmuse.internal.BusymindListener
import com.choosemuse.libmuse.internal.BusymindMode
import com.choosemuse.libmuse.internal.BusymindPacket
import com.choosemuse.libmuse.internal.BusymindVersion
import com.choosemuse.libmuse.internal.SessionArgs
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.BusymindSignalQualityProcessor
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MuseManager
@Inject constructor(
//    private val appComponent: ApplicationComponent,
    private val batteryPercentageMonitor: MuseBatteryPercentageMonitor,
    val busymindMonitor: BusymindMonitor,
    private val signalQualityMonitor: MuseSignalQualityMonitor,
    private val busymindSignalQualityProcessor: BusymindSignalQualityProcessor,
    private val museConnector: MuseConnector,
//    private val museSignalQualityTracker: MuseSignalQualityTracker,
    private val musePresetSetter: MusePresetSetter
) {

//    val museCharacteristicsManager: MuseCharacteristicsManager
//        get() = appComponent.getMuseCharacteristicsManager()

    private var disposables = CompositeDisposable()

    private var museTimeSyncEnabled = false

    fun enableMuseUsage(
        museTimeSyncEnabled: Boolean,
        lenientSqcEnabled: Boolean,
        busymindVersion: BusymindVersion
    ) {
        this.museTimeSyncEnabled = museTimeSyncEnabled
        busymindSignalQualityProcessor.lenientSqcEnabled = lenientSqcEnabled
        busymindMonitor.busymindVersion = busymindVersion
        enableMuseUsage()
    }

    fun enableMuseUsage() {
        signalQualityMonitor.startMonitoring()
        museConnector.turnOnDiscovery()
        musePresetSetter.startMusePresetMonitoring()

        museConnector.muse
            .subscribe { muse ->
                batteryPercentageMonitor.startMonitoring(muse)
//                startBusymind(muse)
                busymindMonitor.startMonitoring(muse)
            }.let { disposables.add(it) }

        Observable.combineLatest(
            museConnector.museConnectionState
                .filter { it == ConnectionState.CONNECTED },
            museConnector.muse.distinctUntilChanged()
        ) { _, muse ->
            if (muse.model == MuseModel.MU_05) {
//                MuseInternal.enableTimeSync(muse, museTimeSyncEnabled)
            }
//            museCharacteristicsManager.setupCharacteristics(muse)
            busymindSignalQualityProcessor.museModel = muse.model
            @Suppress("WHEN_ENUM_CAN_BE_NULL_IN_JAVA")
            when (muse.model) {
                MuseModel.MU_01,
                MuseModel.MU_02,
                MuseModel.MU_03,
                MuseModel.MU_06 -> {
                    signalQualityMonitor.setPpgFillMillis(MuseSignalQualityMonitor.BLACKCOMB_PPG_FILL_MILLIS)
                    signalQualityMonitor.eegSensorSignalQualityProxiesForPpgSignalQuality = false
                }

                MuseModel.MU_04,
                MuseModel.MU_05 -> {
                    signalQualityMonitor.setPpgFillMillis(MuseSignalQualityMonitor.SLEEP_MUSE_PPG_FILL_MILLIS)
                    signalQualityMonitor.eegSensorSignalQualityProxiesForPpgSignalQuality = true
                }

                MuseModel.MS_03 -> {
                    signalQualityMonitor.setPpgFillMillis(MuseSignalQualityMonitor.SLEEP_MUSE_PPG_FILL_MILLIS)
                    signalQualityMonitor.eegSensorSignalQualityProxiesForPpgSignalQuality = false
                }
            }
        }.subscribe().let { disposables.add(it) }

//        museSignalQualityTracker.startMuseSignalQualityTracking()
    }

    fun disableMuseUsage() {
        disposables.clear()
        disposables = CompositeDisposable()
        signalQualityMonitor.stopMonitoring()
//        museSignalQualityTracker.stopMuseSignalQualityTracking()
        batteryPercentageMonitor.stopMonitoring()
        busymindMonitor.stopMonitoring()
        museConnector.turnOffDiscovery()
        museConnector.disconnectMuse()
        musePresetSetter.stopMusePresetMonitoring()
    }

    fun setBusymindSessionArgs(sessionArgs: SessionArgs) {
        busymindMonitor.resetCalibration()
        busymindMonitor.setSessionArgs(sessionArgs)
    }
}