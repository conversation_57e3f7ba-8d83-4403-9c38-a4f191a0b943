package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import com.choosemuse.libmuse.Battery
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseDataPacketType
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 电池电量监控
 */

@Singleton
class MuseBatteryPercentageMonitor
@Inject constructor(
    private val museDataObservableFactory: MuseDataObservableFactory
) {
    val batteryPercentage = BehaviorSubject.createDefault(0)

    private var museDisposables = CompositeDisposable()

    fun startMonitoring(muse: Muse) {
        museDisposables.dispose()
        museDisposables = CompositeDisposable()
        updateBatteryFromMuseConfig(muse)
        museDataObservableFactory.createDataObservable(muse, MuseDataPacketType.BATTERY)
            .subscribe {
                val percentage = it.getBatteryValue(Battery.CHARGE_PERCENTAGE_REMAINING)
                if (!percentage.isNaN()) {
                    batteryPercentage.onNext(percentage.toInt())
                }
            }.let { museDisposables.add(it) }

        museDataObservableFactory.createConnectionStateObservable(muse)
            .subscribe {
                updateBatteryFromMuseConfig(muse)
            }.let { museDisposables.add(it) }
    }

    private fun updateBatteryFromMuseConfig(muse: Muse) {
        val config = muse.museConfiguration
        batteryPercentage.onNext(config?.batteryPercentRemaining?.toInt() ?: 0)
    }

    fun stopMonitoring() {
        museDisposables.clear()
    }
}