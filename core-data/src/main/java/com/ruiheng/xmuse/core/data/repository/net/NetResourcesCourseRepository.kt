package com.ruiheng.xmuse.core.data.repository.net

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.os.Environment
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.common.result.network.di.ApplicationScope
import com.ruiheng.xmuse.core.network.course.CourseService
import com.ruiheng.xmuse.core.network.course.RequestCourse
import com.ruiheng.xmuse.core.network.course.RequestCourseCategory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesCourseRepository @Inject constructor(
    private val courseService: CourseService,
    @ApplicationScope private val appScope: CoroutineScope
) {

    companion object {
        private const val FPS = 5
        const val NORMAL_COURSE_MUSE_FILE_TYPE = 1
        const val NOISE_COURSE_MUSE_FILE_TYPE = 2
    }

    private val downloadManager by lazy {
        Utils.getApp().getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
    }

    /**
     * 获取首页课程分类列表
     */
    fun fetchCourseCategory() =
        flow<Result<List<RequestCourseCategory>>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson()
            val response = courseService.loadHomeCourseCategory(requestBody)
            requestCatch(response,
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 获取首页课程详情
     */
    fun fetchCourseDetail(courseId: String) =
        flow<Result<RequestCourse>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson(Pair("courseId", courseId))
            val response = courseService.loadCourseDetail(requestBody)
            requestCatch(response,
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 保存课程数据
     */
    fun saveCourseRecord(
        deviceId: Long,
        courseId: String?,
        type: Int,
        rawDataFile: String,
        courseStartTime: String,
        courseEndTime: String,
        startVolume: Int,
        endVolume: Int
    ) = flow<Result<Any>> {
        emit(Result.Loading())
        val requestBody = generateRequestDataJson(
            Pair("deviceId", deviceId.toString()),
            Pair("courseId", courseId ?: "0"),
            Pair("type", type.toString()),
            Pair("rawDataFile", rawDataFile),
            Pair("courseStartTime", courseStartTime),
            Pair("courseEndTime", courseEndTime),
            Pair("startVolume", startVolume.toString()),
            Pair("endVolume", endVolume.toString()),
        )
        val response = courseService.saveCourseRecord(requestBody)
        requestCatch(response,
            successCallback = { result ->
                emit(Result.Success(true))
            })
    }.globalCatch().flowOn(Dispatchers.IO)

    fun startCheckCourseFile(course: RequestCourse) =
        flow<Result<RequestCourse>> {
            emit(Result.Loading())
            val bankPair = course.getCourseMasterBankFile(Utils.getApp())
            if (bankPair == null) {
                emit(Result.Error(PetivityThrowable()))
            } else {
                emit(Result.Success(course))
            }
        }.globalCatch().flowOn(Dispatchers.IO)

}


