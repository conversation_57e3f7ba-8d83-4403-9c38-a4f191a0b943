package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session;

public abstract class SessionConsts {
    public static final double MIND_MIN = 0.0;

    public static final double MIND_MAX = 1.0;

    public static final double MIND_CALM = 0.33;

    public static final double MIND_ACTIVE = 0.67;

    public static final double BODY_MIN = 0.0;

    public static final double BODY_MAX = 1.0;

    public static final double BODY_STILL = 0.35;

    public static final double BREATH_MIN = 0.0;

    public static final double BREATH_MAX = 1.0;

    public static final double BREATH_HIGH_SYNC = 0.75;

    public static final double BREATH_LOW_SYNC = 0.4;

    public static final double SLEEP_STAGES_MIN = -0.5;

    public static final double SLEEP_STAGES_MAX = 3.5;

    public static final double SLEEP_STAGES_DEEP = 0.5;

    public static final double SLEEP_STAGES_LIGHT = 1.5;

    public static final double SLEEP_STAGES_REM = 2.5;

    public static final double SLEEP_POSITIONS_MIN = -0.5;

    public static final double SLEEP_POSITIONS_MAX = 4.5;

    public static final double SLEEP_POSITIONS_FRONT = 0.5;

    public static final double SLEEP_POSITIONS_RIGHT = 1.5;

    public static final double SLEEP_POSITIONS_BACK = 2.5;

    public static final double SLEEP_POSITIONS_LEFT = 3.5;

    public static final double DEEP_SLEEP_INTENSITY_MIN = 0.0;

    public static final double DEEP_SLEEP_INTENSITY_MAX = 260.0;

    public static final String TRACKING_ONLY_CONTENT_ID = "tracking_only";

}
