package com.interaxon.muse.session.data_tracking.neurofeedback

enum class TflitePresenterModel {
    SLEEP_STAGE_GRAPH,
    SLEEP_POSITION_GRAPH,
    SLEEP_SCORE,
    LEGACY_DEEP_SLEEP_INTENSITY_GRAPH,
    DEEP_SLEEP_INTENSITY_GRAPH,
    LEG<PERSON>Y_DEEP_SLEEP_INTENSITY_SCORE,
    DEEP_SLEEP_INTENSITY_SCORE,
    TEN_HZ_INPUT_TIMESERIES,
    SIXTY_FOUR_HZ_INPUT_TIMESERIES,
    BAND_POWER_GRAPH,
    FOX_SCORE_GRAPH,
    CONCENTRATIONS_SCORE_GRAPH;

    val tfliteFilename: String
        get() {
            return when (this) {
                SLEEP_STAGE_GRAPH -> "tf_sleep_stage_graph_presenter.tflite"
                SLEEP_POSITION_GRAPH -> "tf_sleep_position_graph_presenter.tflite"
                SLEEP_SCORE -> "tf_sleep_score.tflite"
                LEGACY_DEEP_SLEEP_INTENSITY_GRAPH -> "legacy_tf_deep_sleep_intensity_graph_presenter.tflite"
                DEEP_SLEEP_INTENSITY_GRAPH -> "tf_deep_sleep_intensity_graph_presenter.tflite"
                LEGACY_DEEP_SLEEP_INTENSITY_SCORE -> "legacy_tf_deep_sleep_intensity_score.tflite"
                DEEP_SLEEP_INTENSITY_SCORE -> "tf_deep_sleep_intensity_score.tflite"
                TEN_HZ_INPUT_TIMESERIES -> "tf_10hz_input_timeseries_presenter.tflite"
                SIXTY_FOUR_HZ_INPUT_TIMESERIES -> "tf_64hz_input_timeseries_presenter.tflite"
                BAND_POWER_GRAPH -> "tf_band_power_graph_presenter_40.tflite"
                FOX_SCORE_GRAPH -> "fox_score_graph_presenter.tflite"
                CONCENTRATIONS_SCORE_GRAPH -> "concentrations_score_graph_presenter.tflite"
            }
        }
}
