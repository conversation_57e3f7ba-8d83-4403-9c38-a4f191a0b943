package com.ruiheng.xmuse.core.data.repository.net

import androidx.lifecycle.LiveData
import com.blankj.utilcode.util.SPUtils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.AppExecutors
import com.ruiheng.xmuse.core.data.util.NetworkBoundResource
import com.ruiheng.xmuse.core.model.data.wxapi.WxAccessToken
import com.ruiheng.xmuse.core.model.data.wxapi.WxLogin
import com.ruiheng.xmuse.core.model.data.wxapi.WxTicket
import com.ruiheng.xmuse.core.model.data.wxapi.WxUserInfo
import com.ruiheng.xmuse.core.network.other.WxApiResponse
import com.ruiheng.xmuse.core.network.other.WxService
import timber.log.Timber
import java.util.SortedMap
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesWxLoginRepository @Inject constructor(
    private val appExecutors: AppExecutors,
    private val wxService: WxService
) {


    companion object {
        const val SP_WX_APP_CODE_ACCESS_TOKEN = "codeAccessToken"
        const val SP_WX_APP_CODE_EXPIRES_IN = "codeExpiresIn"

        const val SP_WX_TICKET = "wxTicket"
        const val SP_WX_TICKET_EXPIRES_IN = "wxTicketExpiresIn"

        fun createSign(valueMap: SortedMap<String, Any>): String {
            val stringbuilder = StringBuilder()
            val set = valueMap.entries
            val it = set.iterator()
            while (it.hasNext()) {
                val entry = it.next() as Map.Entry<String, Any>
                val key = entry.key
                val value = entry.value
                stringbuilder.append("$key=$value")
                if (it.hasNext()) {
                    stringbuilder.append("&")
                }
            }
            return stringbuilder.toString()
        }
    }


    fun getAccessToken(appId: String, appSecret: String, code: String): LiveData<Result<WxLogin>> {
        var wxLogin = WxLogin.createEmpty()
        return object : NetworkBoundResource<WxLogin, WxLogin>(appExecutors, false) {
            override fun saveCallResult(item: WxLogin) {
                wxLogin = item
            }

            override fun createCall() = wxService.getAccessToken(appId, appSecret, code)

        }.asLiveData()
    }

    fun getCodeAccessToken(appId: String, appSecret: String): LiveData<Result<WxAccessToken>> {
        return object : NetworkBoundResource<WxAccessToken, WxAccessToken>(appExecutors) {

            override fun loadFromDb() = object : LiveData<WxAccessToken>() {
                override fun onActive() {
                    super.onActive()
                    val codeAccessToken = SPUtils.getInstance().getString(
                        SP_WX_APP_CODE_ACCESS_TOKEN
                    )
                    val time = SPUtils.getInstance().getLong(SP_WX_APP_CODE_EXPIRES_IN)
                    if (codeAccessToken.isNullOrEmpty() || System.currentTimeMillis() - time > 7200000) {
                        postValue(null)
                    } else {
                        postValue(
                            WxAccessToken(
                                codeAccessToken,
                                null,
                                null,
                                null
                            )
                        )
                    }
                }
            }

            override fun shouldFetch(data: WxAccessToken?) =
                data == null || data.access_token.isNullOrEmpty()

            override fun saveCallResult(item: WxAccessToken) {
                super.saveCallResult(item)
                Timber.d("WxCodeAccessSave$item")
                if (!item.access_token.isNullOrEmpty()) {
                    SPUtils.getInstance().put(SP_WX_APP_CODE_ACCESS_TOKEN, item.access_token)
                    SPUtils.getInstance().put(SP_WX_APP_CODE_EXPIRES_IN, System.currentTimeMillis())
                }
            }

            override fun createCall(): LiveData<WxApiResponse<WxAccessToken>> =
                wxService.getCodeAccessToken(appId = appId, secret = appSecret)
        }.asLiveData()
    }

    fun getWxTicket(accessToken: String): LiveData<Result<WxTicket>> {
        return object : NetworkBoundResource<WxTicket, WxTicket>(appExecutors) {

            override fun loadFromDb() = object : LiveData<WxTicket>() {
                override fun onActive() {
                    super.onActive()
                    val ticket = SPUtils.getInstance().getString(SP_WX_TICKET)
                    val time = SPUtils.getInstance().getLong(SP_WX_TICKET_EXPIRES_IN)
                    if (ticket.isNullOrEmpty() || System.currentTimeMillis() - time > 7200000) {
                        postValue(null)
                    } else {
                        postValue(
                            WxTicket(
                                ticket,
                                null,
                                null,
                                null
                            )
                        )
                    }
                }
            }

            override fun shouldFetch(data: WxTicket?) = data == null || data.ticket.isNullOrEmpty()

            override fun saveCallResult(item: WxTicket) {
                super.saveCallResult(item)
                if (!item.ticket.isNullOrEmpty()) {
                    SPUtils.getInstance().put(SP_WX_TICKET, item.ticket)
                    SPUtils.getInstance().put(SP_WX_TICKET_EXPIRES_IN, System.currentTimeMillis())
                }
            }

            override fun createCall() = wxService.getWxTicket(accessToken)
        }.asLiveData()
    }

    fun clearCodeLogin() {
        SPUtils.getInstance().remove(SP_WX_TICKET)
        SPUtils.getInstance().remove(SP_WX_TICKET_EXPIRES_IN)
        SPUtils.getInstance().remove(SP_WX_APP_CODE_ACCESS_TOKEN)
        SPUtils.getInstance().remove(SP_WX_APP_CODE_EXPIRES_IN)
    }

    fun getWeChatUser(accessToken: String, openId: String): LiveData<Result<WxUserInfo>> {
        var wxUserInfo = WxUserInfo()
        return object : NetworkBoundResource<WxUserInfo, WxUserInfo>(appExecutors) {
            override fun saveCallResult(item: WxUserInfo) {
                wxUserInfo = item
            }

            override fun loadFromDb() = object : LiveData<WxUserInfo>() {
                override fun onActive() {
                    super.onActive()
                    postValue(wxUserInfo)
                }
            }

            override fun createCall() = wxService.getWxUserInfo(accessToken, openId)

        }.asLiveData()
    }
}