package com.ruiheng.xmuse.core.data.repository.muse.interaxon

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer


open class LiveEvent<T>(data: T) : MutableLiveData<T>(data) {
    private var pending = false

    override fun setValue(value: T) {
        pending = true
        super.setValue(value)
    }

    override fun postValue(value: T) {
        pending = true
        super.postValue(value)
    }

    override fun observeForever(observer: Observer<in T>) {
        super.observeForever {
            if (pending) {
                observer.onChanged(it)
                pending = false
            }
        }
    }
}