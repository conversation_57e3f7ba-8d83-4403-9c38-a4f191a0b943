package com.ruiheng.xmuse.core.data.di

///*
// * Copyright (C) 2022 The Android Open Source Project
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *      http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */


import com.ruiheng.xmuse.core.data.repository.OfflineUserRepository
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.data.util.TimeBroadcastMonitor
import com.ruiheng.xmuse.core.data.util.TimeMonitor
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {

    @Singleton
    @Binds
    internal abstract fun bindsUserDataRepository(userDataRepository: OfflineUserRepository): UserDataRepository

//    @Singleton
//    @Binds
//    internal abstract fun bindNetResourcesSettingRepository(
//        netResourcesSettingRepository: NetResourcesSettingRepository
//    ): NetResourcesSettingRepository

    @Singleton
    @Binds
    internal abstract fun bindTimeMonitor(
        impl: TimeBroadcastMonitor
    ): TimeMonitor
}

