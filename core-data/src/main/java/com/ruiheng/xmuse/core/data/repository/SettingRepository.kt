package com.ruiheng.xmuse.core.data.repository

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.os.Environment
import androidx.annotation.WorkerThread
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.common.result.network.di.ApplicationScope
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesConfigRepository
import com.ruiheng.xmuse.core.data.repository.net.asDaoEntity
import com.ruiheng.xmuse.core.data.repository.net.generateRequestDataJson
import com.ruiheng.xmuse.core.data.repository.net.globalCatch
import com.ruiheng.xmuse.core.data.repository.net.requestCatch
import com.ruiheng.xmuse.core.database.AppDatabase
import com.ruiheng.xmuse.core.database.RoomHelper
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import com.ruiheng.xmuse.core.network.model.user.RequestBindPhoneResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingRepository @Inject constructor(
    @ApplicationScope private val appScope: CoroutineScope,
    private val roomHelper: RoomHelper,
    private val configRepository: NetResourcesConfigRepository
) {


    @WorkerThread
    fun startApp(isLocalTest: Boolean, isTest: Boolean) = flow<Result<Boolean>> {
        emit(Result.Loading())
        roomHelper.clearAllTables()
        RetrofitNetworkModule.startApp(isLocalTest, isTest)
        delay(1500)
        emit(Result.Success(true))
    }.globalCatch().flowOn(Dispatchers.IO)

    /**
     * 获取Xmuse域名配置
     */
    @WorkerThread
    fun getXmuseDomainConfig() = configRepository.getXmuseDomainConfig()
}