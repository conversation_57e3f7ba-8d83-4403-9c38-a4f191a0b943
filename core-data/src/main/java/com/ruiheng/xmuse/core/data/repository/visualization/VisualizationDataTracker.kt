package com.ruiheng.xmuse.core.data.repository.visualization

import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.ResultsMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionBusymindDataMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionManager
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import io.reactivex.rxjava3.core.Single
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class VisualizationDataTracker @Inject constructor(
    private val busymindDataMonitor: SessionBusymindDataMonitor,
) {

    companion object {

        private val VISUALIZATION_BUSYMIND_DATA = BusymindData.entries.filter {
            when (it) {
                BusymindData.ALPHA, BusymindData.BETA, BusymindData.DELTA,
                BusymindData.GAMMA, BusymindData.THETA,
                BusymindData.HEART,
                BusymindData.PPG_HRV_PNN50, BusymindData.PPG_HRV_LF, BusymindData.PPG_HRV_HF, BusymindData.PPG_HRV_LFvsHF, BusymindData.PPG_HRV_SDNN, BusymindData.PPG_HRV_RMSSD
                    -> true

                BusymindData.BODY, BusymindData.MIND, BusymindData.BREATH, BusymindData.FOX_SCORE,
                BusymindData.HBO_LEFT, BusymindData.HBO_RIGHT, BusymindData.HBR_LEFT,
                BusymindData.HBR_RIGHT -> false
            }
        }

    }

    private val busymindTimeseriesDataTrackers =
        VISUALIZATION_BUSYMIND_DATA.associateWith { busymindData ->
            BusymindVisualizationTimeseriesDataTracker()
        }

    private var tracking = false

    fun startTracking() {
        if (tracking) {
            return
        }

        tracking = true

        busymindTimeseriesDataTrackers.forEach { (busymindData, tracker) ->
            tracker.startTracking(
                busymindDataMonitor.busymindData
                    .filter { it.containsKey(busymindData) }
                    .map { it[busymindData]!! }
            )
        }
    }

    fun stopTracking() {
        busymindTimeseriesDataTrackers.forEach { it.value.stopTracking() }
        tracking = false
    }

    fun loadLastValueMap() = VISUALIZATION_BUSYMIND_DATA.associateWith { busymindData ->
        busymindTimeseriesDataTrackers[busymindData]?.processValue() ?: 0.0
    }

    fun startNewDataGap() {
        busymindDataMonitor.setGapEnabled(true)
    }

    fun endDataGap() {
        busymindDataMonitor.setGapEnabled(false)
    }

}
