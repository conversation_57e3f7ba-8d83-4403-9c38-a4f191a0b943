package com.ruiheng.xmuse.core.data.di

import com.ruiheng.xmuse.core.data.request.AccountExpiredInterceptor
import com.ruiheng.xmuse.core.database.dao.UserDao
import com.ruiheng.xmuse.core.network.RequestResultInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object InterceptorModule {

    @Provides
    @Singleton
    fun provideAccountExpiredInterceptor(userDao: UserDao): RequestResultInterceptor {
        return AccountExpiredInterceptor(userDao)
    }

}