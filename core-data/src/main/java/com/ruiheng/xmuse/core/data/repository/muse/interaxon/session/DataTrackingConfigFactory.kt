//package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session
//
//import com.choosemuse.libmuse.internal.MuseSessionType
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseRequirement
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.DataTrackingConfig
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.FmodContent
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlatformFeatureFlags
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlatformFeatureFlags.FEATURE_FLAG_ALPHA_PEAK_ENABLED
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.PlatformFeatureFlags.FEATURE_FLAG_ALPHA_POWER_ENABLED
//import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepSessionInfo
//import org.threeten.bp.OffsetDateTime
//
//class DataTrackingConfigFactory private constructor() {
//    companion object {
//        private val trackingOnlySessionInfo = SleepSessionInfo(
//            "",
//            "",
//            -1,
//            null,
//            FmodContent(
//                "",
//                null,
//                null,
//                "silent.bank",
//                "silent.strings.bank",
//                null,
//                FmodConfig(
//                    "session",
//                    "silent",
//                    arrayListOf(0, 1, 2, 3, 4, 5, 6, 7, 10, 50, 42, 43),
//                    null,
//                    null,
//                    null,
//                    null,
//                    null
//                )
//            )
//        )
//
//        fun createDataTrackingConfigForSleepTrackingOnly(
//            title: String,
//            museDeviceSelector: MuseDeviceSelector,
//            aggregatedSessionData: Map<String, Int>,
//            tags: List<String>,
//            featureFlags: PlatformFeatureFlags
//        ): DataTrackingConfig {
//            val now = OffsetDateTime.now()
//            val utcTimestamp = now.toEpochSecond()
//            val muse = if (!museDeviceSelector.isStreamingData) {
//                null
//            } else {
//                with(museDeviceSelector) {
//                    UserSessionMuse(
//                        macAddress,
//                        serialNumber,
//                        fabricBandSerialNumber,
//                        firmwareVersion
//                    )
//                }
//            }
//            return DataTrackingConfig(
//                sessionInfo = trackingOnlySessionInfo,
//                requestAudioFocus = false,
//                audioControlsVisible = false,
//                playEndingBell = false,
//                fadeAudio = true,
//                sessionId = SessionIdUtils.createSessionId(),
//                rawStartDatetimeLocalWithTimezone = now.toString(),
//                utcTimestamp = utcTimestamp,
//                sessionType = MuseSessionType.SLEEP,
//                resultsMode = ResultsMode.SLEEP,
//                selectedSessionLengthSeconds = null,
//                muse = muse,
//                content = UserSessionContent(
//                    soundscapeUniqueId = SessionConsts.TRACKING_ONLY_CONTENT_ID,
//                    legacySoundscapeContentId = SessionConsts.TRACKING_ONLY_CONTENT_ID,
//                    title = title
//                ),
//                heart = toHeartUserSession(aggregatedSessionData),
//                tags = tags,
//                isWebSession = false,
//                alphaPeakEnabled = isAlphaPeakEnabled(featureFlags, muse, MuseSessionType.SLEEP),
//                alphaPowerEnabled = isAlphaPowerEnabled(featureFlags, muse, MuseSessionType.SLEEP)
//            )
//        }
//
//        private fun toHeartUserSession(aggregatedData: Map<String, Int>): HeartUserSession {
//            return HeartUserSession(
//                historicalAbsMaxRate = aggregatedData[UserPreferencesRepository.HISTORICAL_HEART_BPM_MAX],
//                historicalAbsMinRate = aggregatedData[UserPreferencesRepository.HISTORICAL_HEART_BPM_MIN],
//                historicalAvgMaxRate = aggregatedData[UserPreferencesRepository.AVERAGE_HEART_BPM_MAX],
//                historicalAvgMinRate = aggregatedData[UserPreferencesRepository.AVERAGE_HEART_BPM_MIN],
//            )
//        }
//
//        fun createDataTrackingConfigForMeditationContent(
//            requestAudioFocus: Boolean,
//            groupTitle: String,
//            meditation: Meditation,
//            programId: String?,
//            programModuleId: String?,
//            defaultResultsMode: ResultsMode,
//            museDeviceSelector: MuseDeviceSelector,
//            meditationContentRepo: MeditationContentRepository,
//            aggregatedSessionData: Map<String, Int>,
//            tags: List<String>,
//            featureFlags: PlatformFeatureFlags
//        ): DataTrackingConfig {
//            val now = OffsetDateTime.now()
//            val utcTimestamp = now.toEpochSecond()
//            val resultsMode = ResultsMode.deserialize(meditation.resultsMode) ?: defaultResultsMode
//            val sessionType =
//                if (resultsMode == ResultsMode.SLEEP) {
//                    MuseSessionType.SLEEP
//                } else {
//                    MuseSessionType.GUIDED
//                }
//            val muse = if (!museDeviceSelector.isStreamingData) {
//                null
//            } else {
//                with(museDeviceSelector) {
//                    UserSessionMuse(
//                        macAddress,
//                        serialNumber,
//                        fabricBandSerialNumber,
//                        firmwareVersion
//                    )
//                }
//            }
//            return DataTrackingConfig(
//                getPresleepSessionInfo(meditation, meditationContentRepo),
//                requestAudioFocus,
//                audioControlsVisible = true,
//                playEndingBell = resultsMode != ResultsMode.SLEEP,
//                fadeAudio = false,
//                SessionIdUtils.createSessionId(),
//                now.toString(),
//                utcTimestamp = utcTimestamp,
//                sessionType,
//                resultsMode,
//                meditation.duration,
//                muse,
//                UserSessionContent(
//                    meditationUniqueId = meditation.id,
//                    legacyExerciseContentId = meditation.id,
//                    programUniqueId = programId,
//                    programModuleUniqueId = programModuleId,
//                    groupTitle = groupTitle,
//                    title = meditation.title,
//                    techniques = meditation.techniques,
//                    teacherImageUrl = meditation.teacher?.teacherImageURL ?: ""
//                ),
//                toHeartUserSession(aggregatedSessionData),
//                tags = tags,
//                isWebSession = false,
//                alphaPeakEnabled = isAlphaPeakEnabled(featureFlags, muse, sessionType),
//                alphaPowerEnabled = isAlphaPowerEnabled(featureFlags, muse, sessionType)
//            )
//        }
//
//        private fun getPresleepSessionInfo(
//            meditation: Meditation,
//            meditationContentRepo: MeditationContentRepository
//        ): SleepSessionInfo? {
//            val dir = meditationContentRepo.getDirectory(meditation) ?: return null
//            val filename = meditationContentRepo.getFilename(meditation) ?: return null
//
//            return SleepSessionInfo(
//                meditation.teacher?.teacherImageURL ?: "",
//                meditation.teacher?.name() ?: "",
//                meditation.duration,
//                GuidanceContent(
//                    null,
//                    null,
//                    meditation.id,
//                    dir.absolutePath,
//                    filename,
//                    ""
//                ),
//                null
//            )
//        }
//
//        fun createDataTrackingConfigForJourneyContent(
//            requestAudioFocus: Boolean,
//            groupTitle: String,
//            journey: Journey,
//            selectedSessionLengthSeconds: Int,
//            programId: String?,
//            programModuleId: String?,
//            defaultResultsMode: ResultsMode,
//            museDeviceSelector: MuseDeviceSelector,
//            journeyContentRepo: JourneyContentRepository,
//            aggregatedSessionData: Map<String, Int>,
//            tags: List<String>,
//            featureFlags: PlatformFeatureFlags
//        ): DataTrackingConfig {
//            val fmodConfig = FmodConfigUtils.parseConfig(journey.fmodJSON)
//            val resultsMode = ResultsMode.deserialize(journey.resultsMode) ?: defaultResultsMode
//            val sessionType =
//                FmodConfigUtils.parseSessionType(
//                    fmodConfig?.sessionType,
//                    if (resultsMode == ResultsMode.SLEEP) MuseSessionType.SLEEP else MuseSessionType.TIMED
//                )
//            val now = OffsetDateTime.now()
//            val utcTimestamp = now.toEpochSecond()
//            val muse =
//                if (MuseRequirement.toMuseRequirement(
//                        sessionType,
//                        requiresMuse = false
//                    ) == MuseRequirement.NO_MUSE
//                ) {
//                    null
//                } else {
//                    with(museDeviceSelector) {
//                        UserSessionMuse(
//                            macAddress,
//                            serialNumber,
//                            fabricBandSerialNumber,
//                            firmwareVersion
//                        )
//                    }
//                }
//            return DataTrackingConfig(
//                getPresleepSessionInfo(journey, selectedSessionLengthSeconds, journeyContentRepo),
//                requestAudioFocus,
//                audioControlsVisible = true,
//                playEndingBell = resultsMode != ResultsMode.SLEEP,
//                fadeAudio = true,
//                SessionIdUtils.createSessionId(),
//                now.toString(),
//                utcTimestamp = utcTimestamp,
//                sessionType,
//                resultsMode,
//                selectedSessionLengthSeconds,
//                muse,
//                UserSessionContent(
//                    journeyUniqueId = journey.id,
//                    programUniqueId = programId,
//                    programModuleUniqueId = programModuleId,
//                    legacySoundscapeContentId = journey.id,
//                    groupTitle = groupTitle,
//                    title = journey.name ?: "",
//                    techniques = journey.techniques,
//                    teacherImageUrl = journey.teacher?.teacherImageURL ?: ""
//                ),
//                toHeartUserSession(aggregatedSessionData),
//                tags = tags,
//                isWebSession = false,
//                alphaPeakEnabled = isAlphaPeakEnabled(featureFlags, muse, sessionType),
//                alphaPowerEnabled = isAlphaPowerEnabled(featureFlags, muse, sessionType)
//            )
//        }
//
//        private fun getPresleepSessionInfo(
//            journey: Journey,
//            selectedDurationSeconds: Int,
//            journeyContentRepo: JourneyContentRepository
//        ): SleepSessionInfo? {
//            val dir = journeyContentRepo.getDirectory(journey) ?: return null
//
//            var pureDataFilename: String? = journey.bankFileName?.replace(".bank", ".pd")
//            if (pureDataFilename != null && !dir.resolve(pureDataFilename).exists()) {
//                pureDataFilename = null
//            }
//
//            val fmodConfig = FmodConfigUtils.parseConfig(journey.fmodJSON) ?: return null
//            return SleepSessionInfo(
//                journey.teacher?.teacherImageURL ?: "",
//                journey.teacher?.name() ?: "",
//                if (selectedDurationSeconds > 0) {
//                    selectedDurationSeconds
//                } else {
//                    journey.duration!!
//                },
//                null,
//                FmodContent(
//                    null,
//                    journey.id,
//                    dir.path,
//                    journey.bankFileName,
//                    journey.stringsBankFileName,
//                    pureDataFilename,
//                    fmodConfig
//                )
//            )
//        }
//
//        fun createDataTrackingConfigForWebContent(
//            journey: Journey?,
//            fmodJson: String?,
//            programId: String?,
//            programModuleId: String?,
//            museDeviceSelector: MuseDeviceSelector,
//            journeyContentRepo: JourneyContentRepository,
//            aggregatedSessionData: Map<String, Int>,
//            tags: List<String>,
//            featureFlags: PlatformFeatureFlags
//        ): DataTrackingConfig {
//            val fmodConfig = if (journey != null) {
//                FmodConfigUtils.parseConfig(journey.fmodJSON)
//            } else {
//                FmodConfigUtils.parseConfig(fmodJson)
//            }
//            val sessionType =
//                FmodConfigUtils.parseSessionType(fmodConfig?.sessionType, MuseSessionType.TIMED)
//            val now = OffsetDateTime.now()
//            val utcTimestamp = now.toEpochSecond()
//            val muse =
//                if (MuseRequirement.toMuseRequirement(
//                        sessionType,
//                        requiresMuse = false
//                    ) == MuseRequirement.NO_MUSE
//                ) {
//                    null
//                } else {
//                    with(museDeviceSelector) {
//                        UserSessionMuse(
//                            macAddress,
//                            serialNumber,
//                            fabricBandSerialNumber,
//                            firmwareVersion
//                        )
//                    }
//                }
//            return DataTrackingConfig(
//                sessionInfo = if (journey == null) {
//                    SleepSessionInfo(
//                        /* teacherPhotoUrl = */ "",
//                        /* teacherName = */ "",
//                        /* sessionLengthSeconds = */ 0,
//                        /* guidanceContent = */ null,
//                        /* fmodContent = */ FmodContent(
//                            /* soundscapeUniqueId = */ null,
//                            /* journeyUniqueId = */ null,
//                            /* absoluteDirectoryPath = */ null,
//                            /* bankFilename = */ null,
//                            /* stringsBankFilename = */ null,
//                            /* pureDataFilename = */ null,
//                            /* config = */ fmodConfig
//                        )
//                    )
//                } else {
//                    getPresleepSessionInfo(journey, -1, journeyContentRepo)
//                },
//                requestAudioFocus = false,
//                audioControlsVisible = false,
//                playEndingBell = false,
//                fadeAudio = false,
//                sessionId = SessionIdUtils.createSessionId(),
//                rawStartDatetimeLocalWithTimezone = now.toString(),
//                utcTimestamp = utcTimestamp,
//                sessionType = sessionType,
//                resultsMode = if (sessionType == SessionType.SLEEP) ResultsMode.SLEEP else ResultsMode.MEDITATE,
//                selectedSessionLengthSeconds = null,
//                muse = muse,
//                content = UserSessionContent(
//                    journeyUniqueId = null,
//                    programUniqueId = programId,
//                    programModuleUniqueId = programModuleId,
//                    legacySoundscapeContentId = null,
//                    groupTitle = null,
//                    title = null,
//                    techniques = null,
//                    teacherImageUrl = null
//                ),
//                heart = toHeartUserSession(aggregatedSessionData),
//                tags = tags,
//                isWebSession = true,
//                alphaPeakEnabled = isAlphaPeakEnabled(featureFlags, muse, sessionType),
//                alphaPowerEnabled = isAlphaPowerEnabled(featureFlags, muse, sessionType)
//            )
//        }
//
//        private fun isAlphaPeakEnabled(
//            featureFlags: PlatformFeatureFlags,
//            muse: UserSessionMuse?,
//            sessionType: MuseSessionType
//        ): Boolean {
//            return featureFlags.isFeatureFlagEnabled(
//                FEATURE_FLAG_ALPHA_PEAK_ENABLED,
//                false
//            ) && muse != null
//                    && sessionType != MuseSessionType.FRONTAL_OXYGENATION
//        }
//
//        private fun isAlphaPowerEnabled(
//            featureFlags: PlatformFeatureFlags,
//            muse: UserSessionMuse?,
//            sessionType: MuseSessionType
//        ): Boolean {
//            return featureFlags.isFeatureFlagEnabled(
//                FEATURE_FLAG_ALPHA_POWER_ENABLED,
//                false
//            ) && muse != null
//                    && sessionType != MuseSessionType.FRONTAL_OXYGENATION
//        }
//    }
//}