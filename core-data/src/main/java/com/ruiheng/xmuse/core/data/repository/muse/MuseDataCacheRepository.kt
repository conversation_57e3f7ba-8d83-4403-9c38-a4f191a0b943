package com.ruiheng.xmuse.core.data.repository.muse

import android.os.Environment.DIRECTORY_DOCUMENTS
import android.os.Handler
import android.os.Looper
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.model.ResumableUploadRequest
import com.alibaba.sdk.android.oss.model.ResumableUploadResult
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.Utils
import com.blankj.utilcode.util.ZipUtils
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseFileFactory
import com.choosemuse.libmuse.MuseFileWriter
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.net.globalCatch
import com.ruiheng.xmuse.core.data.repository.net.requestCatch
import com.ruiheng.xmuse.core.data.util.AliyunOSSHelper
import com.ruiheng.xmuse.core.network.model.RequestDeviceBindResult
import com.ruiheng.xmuse.core.network.model.user.RequestUploadFile
import com.ruiheng.xmuse.core.network.user.FileService
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_AVATAR
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_COURSE_RECORD_PARENT
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_COURSE_RECORD_PATH
import com.ruiheng.xmuse.core.network.user.FileService.Companion.APP_OSS_PARENT
import com.ruiheng.xmuse.core.network.user.FileService.Companion.OSS_PARENT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.File
import java.util.concurrent.atomic.AtomicReference
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MuseDataCacheRepository @Inject constructor(private val fileService: FileService) {

    companion object {
        val CACHE_COURSE_PATH_KEY = "course"
    }

    /**
     * To save data to a file, you should use a MuseFileWriter.  The MuseFileWriter knows how to
     * serialize the data packets received from the headband into a compact binary format.
     * To read the file back, you would use a MuseFileReader.
     */
    private val fileWriter = AtomicReference<MuseFileWriter?>()

    /**
     * We don't want file operations to slow down the UI, so we will defer those file operations
     * to a handler on a separate thread.
     */
    private val fileHandler = AtomicReference<Handler?>()
    private var fileThread :Thread? = null

    private var parentDir: String? = null
    private var currentFileName: String? = null
    fun startMuseFileCache(parentDir: String, fileName: String) {
        val museFileWriter = fileWriter.get()
        if (museFileWriter?.isOpen == true) {
            museFileWriter.addAnnotationString(0, "Disconnected")
            museFileWriter.flush()
            museFileWriter.close()
        }
        this.parentDir = parentDir
        currentFileName = "${fileName}.xmuse"
        fileThread = Thread {
            Looper.prepare()
            val looper = Looper.myLooper()
            if (looper != null) {
                fileHandler.set(Handler(looper))
            } else {
                fileHandler.set(Handler())
            }
            val rootDir = Utils.getApp().getExternalFilesDir(DIRECTORY_DOCUMENTS)
            val dir = File(rootDir, parentDir ?: "other")
            try {
                if (!dir.exists()) {
                    dir.mkdirs()
                }
            } catch (e: Exception) {
            }

            val file = File(dir, "${currentFileName}")
            if (file.exists() && !file.delete()) {

            }

            fileWriter.set(MuseFileFactory.getMuseFileWriter(file))
            Looper.loop()
        }
        fileThread?.start()
    }

    fun saveFileCache() {
        val handler = fileHandler.get() ?: return
        handler.post {
            val museFileWriter = fileWriter.get() ?: return@post
            museFileWriter.addAnnotationString(0, "Disconnected")
            museFileWriter.flush()
            museFileWriter.close()
        }
    }

    fun startUploadMuseFile(museFileName: String, parentDir: String) = flow<Result<RequestUploadFile>> {
        emit(Result.Loading())
        delay(500)
        val ossClient = AliyunOSSHelper.getOSSClient(Utils.getApp())
        if (ossClient == null) {
            emit(Result.Error(PetivityThrowable()))
            return@flow
        }
        val rootDir = Utils.getApp().getExternalFilesDir(DIRECTORY_DOCUMENTS)
        val museFile = File(rootDir, "${parentDir}/${museFileName}.xmuse")

        if (!museFile.exists()) {
            emit(Result.Error(PetivityThrowable()))
            return@flow
        }
        val zipFile = File(rootDir, "${parentDir}/${museFileName}.zip")
        val success = ZipUtils.zipFile(museFile,zipFile)
        val uploadFile = if (zipFile.exists() && success) zipFile else museFile

        val requestFile = uploadFile.asRequestBody("multipart/form-data".toMediaTypeOrNull())
        val body = MultipartBody.Part.createFormData("file", uploadFile.name, requestFile)
        val desPath =
            "$OSS_PARENT/$APP_OSS_PARENT/$APP_COURSE_RECORD_PARENT/${APP_COURSE_RECORD_PATH}/${uploadFile.name}"

        emit(Result.Loading(RequestUploadFile(fileUrl = desPath)))
        val response = fileService.uploadFile(
            body,
            desPath.toRequestBody()
        )
        requestCatch(response, successCallback = { result ->
            try {
                FileUtils.delete(museFile)
                FileUtils.delete(zipFile)
            }catch (e:Exception){
                e.printStackTrace()
            }
            emit(Result.Success(result))
        }, onErrorCallback = {
            try {
                FileUtils.delete(museFile)
                FileUtils.delete(zipFile)
            }catch (e:Exception){
                e.printStackTrace()
            }
        })
//        val bucketName = "xmuse"
//        val objectName = "eeg-data/xmuse-app/course/originxmusefile/${museFileName}"
//        val recordDirectory =
//            File(Utils.getApp().getExternalFilesDir(DIRECTORY_DOCUMENTS), "oss_record")
//        if (!recordDirectory.exists()) {
//            recordDirectory.mkdirs()
//        }
//        val request = ResumableUploadRequest(
//            bucketName,
//            objectName,
//            museFile.absolutePath,
//            recordDirectory.absolutePath
//        )
//        request.setDeleteUploadOnCancelling(false)
//        request.setProgressCallback { request, currentSize, totalSize ->
//        }
//
//        val resumableTask = ossClient.asyncResumableUpload(
//            request,
//            object : OSSCompletedCallback<ResumableUploadRequest, ResumableUploadResult> {
//                override fun onSuccess(
//                    request: ResumableUploadRequest?,
//                    result: ResumableUploadResult?
//                ) {
//
//                }
//
//                override fun onFailure(
//                    request: ResumableUploadRequest?,
//                    clientException: ClientException?,
//                    serviceException: ServiceException?
//                ) {
//                }
//
//            })

//        resumableTask.waitUntilFinished()
    }.globalCatch().flowOn(Dispatchers.IO)

    fun onMuseDataPacketReceive(museDataPacket: MuseDataPacket?) {
        if (museDataPacket == null) return
        val museFileWriter = fileWriter.get() ?: return
        val handler = fileHandler.get() ?: return
        handler.post {
            fileWriter.get()?.addDataPacket(0, museDataPacket)
        }
    }
}