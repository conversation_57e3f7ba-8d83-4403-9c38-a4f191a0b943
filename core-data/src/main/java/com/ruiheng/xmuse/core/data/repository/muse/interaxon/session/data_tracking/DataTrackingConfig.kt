package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking

import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.internal.MuseSessionType
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.ResultsMode
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.SessionUpdate
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo.SleepSessionInfo

data class DataTrackingConfig(
    val sessionInfo: SleepSessionInfo?,
    val requestAudioFocus: Boolean,
    val audioControlsVisible: Boolean,
    val playEndingBell: Boolean,
    val fadeAudio: <PERSON>olean,
    var sessionId: String,
    val rawStartDatetimeLocalWithTimezone: String,
    val utcTimestamp: Long,
    var utcTimestampS: String,
    val sessionType: MuseSessionType,
    val resultsMode: ResultsMode,
    val selectedSessionLengthSeconds: Int?,
    var muse: Muse?,
//    val content: UserSessionContent,
//    val heart: HeartUserSession,
    val tags: List<String>,
    val isWebSession: Boolean,
    val alphaPeakEnabled: Boolean,
    val alphaPowerEnabled: Boolean,

    var savedSessionUpdate: SessionUpdate? = null
) {
    companion object {
        fun createSleepSessionDefault() = DataTrackingConfig(
            null, true,
            false, true,
            true,
            "", "", 0L, "",
            MuseSessionType.GUIDED, ResultsMode.SLEEP, 2000,
            null, listOf(), false, true, true
        )
    }
//    fun toUserSession(): UserSession {
//        val converter = TypeConverter()
//        return UserSession(
//            id = sessionId,
//            rawStartDatetimeLocalWithTimezone = rawStartDatetimeLocalWithTimezone,
//            utcTimestamp = utcTimestamp,
//            rawSessionType = converter.sessionTypeToString(sessionType),
//            rawResultsMode = converter.resultsModeToString(resultsMode),
//            selectedSessionLengthSeconds = selectedSessionLengthSeconds,
//            muse = muse,
//            content = content,
//            heart = heart,
//            tags = RealmList(*tags.toTypedArray())
//        )
//    }
}

