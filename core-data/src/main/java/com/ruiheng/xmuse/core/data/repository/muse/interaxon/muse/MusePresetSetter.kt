package com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse

import android.util.Log
import com.choosemuse.libmuse.ConnectionState
import com.choosemuse.libmuse.Muse
import com.choosemuse.libmuse.MuseModel
import com.choosemuse.libmuse.MusePreset
import com.choosemuse.libmuse.internal.MuseSessionType
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.BehaviorSubject
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Modify by Austin
 */
@Singleton
class MusePresetSetter
@Inject constructor(
    private val museConnector: MuseConnector,
//    @SharedPrefName(PreferenceType.LEGACY)
//    private val sharedPreferences: RxSharedPreferences
) {

    companion object {
        private const val MANUALLY_SELECTED_PRESET_KEY = "MusePresetSetter::manuallySelectedPreset"
        private const val IS_MANUALLY_SELECTING_PRESET_KEY =
            "MusePresetSetter::isManuallySelectingPreset"

        val ATHENA_DEFAULT_PRESET = MusePreset.PRESET_1034
        val ATHENA_SLEEP_PRESET = MusePreset.PRESET_1035
    }

    var manuallySelectedPreset: MusePreset? = null
//        set(value) {
//            val pref = sharedPreferences.getString(MANUALLY_SELECTED_PRESET_KEY)
//            if (value == null) {
//                pref.delete()
//            } else {
//                pref.set(value.name)
//            }
//        }
//        get() {
//            val pref = sharedPreferences.getString(MANUALLY_SELECTED_PRESET_KEY)
//            return if (pref.isSet) {
//                MusePreset.valueOf(pref.get())
//            } else {
//                null
//            }
//        }

    var isManuallySelectingPreset: Boolean = false
//        set(value) {
//            sharedPreferences.getBoolean(IS_MANUALLY_SELECTING_PRESET_KEY).set(value)
//        }
//        get() {
//            return sharedPreferences.getBoolean(IS_MANUALLY_SELECTING_PRESET_KEY, false).get()
//        }

    private var presetMonitorDisposable: Disposable? = null

    private var presetModeObs = BehaviorSubject.createDefault(PresetMode.MEDITATE)

    enum class PresetMode {
        MEDITATE, SLEEP;

        companion object {
            fun toPresetMode(sessionType: MuseSessionType?): PresetMode {
                return when (sessionType) {
                    null,
                    MuseSessionType.MIND,
                    MuseSessionType.TIMED,
                    MuseSessionType.BODY,
                    MuseSessionType.HEART,
                    MuseSessionType.BREATH,
                    MuseSessionType.GUIDED,
                    MuseSessionType.FRONTAL_OXYGENATION -> MEDITATE
                    MuseSessionType.SLEEP -> SLEEP
                }
            }
        }
    }

    fun setPresetMode(presetMode: PresetMode) {
        presetModeObs.onNext(presetMode)
    }

    fun startMusePresetMonitoring() {
        Observable.combineLatest(
            museConnector.muse,
            museConnector.museConnectionState,
            presetModeObs
        ) { muse, connectionState, presetMode ->
            Triple(muse, connectionState, presetMode)
        }.filter { (_, connectionState, _) ->
            connectionState == ConnectionState.CONNECTED
        }.subscribe { (muse, _, presetMode) ->
            updatePreset(muse, presetMode)
        }.let { presetMonitorDisposable = it }
    }

    private fun updatePreset(muse: Muse, presetMode: PresetMode) {
        val preset = getSelectedPreset(muse.model, presetMode)

        Log.d("TEST", "Muse Preset: ${muse.museConfiguration.preset}")
        if (preset != null && muse.museConfiguration.preset != preset) {
            muse.setPreset(preset)
        }
    }

    fun getSelectedPreset(museModel: MuseModel, presetMode: PresetMode): MusePreset? {
        return if (isManuallySelectingPreset) {
            manuallySelectedPreset
        } else {
            getDefaultPreset(museModel, presetMode)
        }
    }

    private val defaultPresetsPerModel = mapOf(
        MuseModel.MU_03 to MusePreset.PRESET_51,
        MuseModel.MU_06 to MusePreset.PRESET_51,
        MuseModel.MU_04 to MusePreset.PRESET_51,
        MuseModel.MU_05 to MusePreset.PRESET_51,
    )

    private fun getDefaultPreset(museModel: MuseModel, presetMode: PresetMode): MusePreset? {
        return if (museModel == MuseModel.MS_03) {
            when (presetMode) {
                PresetMode.SLEEP -> ATHENA_SLEEP_PRESET
                PresetMode.MEDITATE -> ATHENA_DEFAULT_PRESET
            }
        } else {
            defaultPresetsPerModel[museModel]
        }
    }

    fun sendSetPresetCommand() {
        museConnector.getCurrentMuse()?.let { updatePreset(it, presetModeObs.value!!) }
    }

    fun stopMusePresetMonitoring() {
        presetMonitorDisposable?.dispose()
        presetMonitorDisposable = null
    }
}