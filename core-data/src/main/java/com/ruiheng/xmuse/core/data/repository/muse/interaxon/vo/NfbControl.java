// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

public final class NfbControl {


    /*package*/ final String type;

    /*package*/ final String id;

    /*package*/ final String name;

    /*package*/ final String descriptionText;

    /*package*/ final Integer fmodParameter;

    /*package*/ final Integer busymindParameter;

    /*package*/ final Boolean visible;

    /*package*/ final SliderSettings sliderSettings;

    /*package*/ final SwitchSettings switchSettings;

    public NfbControl(
            String type,
            String id,
            String name,
            String descriptionText,
            Integer fmodParameter,
            Integer busymindParameter,
            Boolean visible,
            SliderSettings sliderSettings,
            SwitchSettings switchSettings) {
        this.type = type;
        this.id = id;
        this.name = name;
        this.descriptionText = descriptionText;
        this.fmodParameter = fmodParameter;
        this.busymindParameter = busymindParameter;
        this.visible = visible;
        this.sliderSettings = sliderSettings;
        this.switchSettings = switchSettings;
    }

    public String getType() {
        return type;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescriptionText() {
        return descriptionText;
    }

    public Integer getFmodParameter() {
        return fmodParameter;
    }

    public Integer getBusymindParameter() {
        return busymindParameter;
    }

    public Boolean getVisible() {
        return visible;
    }

    public SliderSettings getSliderSettings() {
        return sliderSettings;
    }

    public SwitchSettings getSwitchSettings() {
        return switchSettings;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "NfbControl{" +
                "type=" + type +
                "," + "id=" + id +
                "," + "name=" + name +
                "," + "descriptionText=" + descriptionText +
                "," + "fmodParameter=" + fmodParameter +
                "," + "busymindParameter=" + busymindParameter +
                "," + "visible=" + visible +
                "," + "sliderSettings=" + sliderSettings +
                "," + "switchSettings=" + switchSettings +
        "}";
    }

}
