package com.ruiheng.xmuse.core.data.request

import android.content.ComponentName
import android.content.Intent
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ThreadUtils
import com.ruiheng.xmuse.core.database.dao.UserDao
import com.ruiheng.xmuse.core.network.RequestResultInterceptor
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber

class AccountExpiredInterceptor(val userDao: UserDao) : RequestResultInterceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        val result = response.body?.source()?.buffer?.toString()
        if (result?.contains("401") == true) {
            userDao.deleteCurrentUserSync()
            ThreadUtils.runOnUiThread {
                val mainActivity = ActivityUtils.getMainActivities()[0]
                val topActivity = ActivityUtils.getTopActivity()
                val packageName = AppUtils.getAppPackageName()
                val componentName = ComponentName(packageName, mainActivity)
                val intent = Intent()
                intent.putExtra("isUserExpired", true)
                intent.setComponent(componentName)
                if (intent.resolveActivity(topActivity.packageManager) != null) {
                    topActivity.startActivity(intent)
                }
            }
        }
        Timber.d("AccountExpiredInterceptor:${response}")
        return response
    }

}