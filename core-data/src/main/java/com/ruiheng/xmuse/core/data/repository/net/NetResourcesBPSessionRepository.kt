package com.ruiheng.xmuse.core.data.repository.net

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.time.getUTCNowTimeString
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.reports.SessionUpdate
import com.ruiheng.xmuse.core.network.bp.ApiRequestBPSessionInfo
import com.ruiheng.xmuse.core.network.bp.BPRxService
import com.ruiheng.xmuse.core.network.bp.BPService
import com.ruiheng.xmuse.core.network.model.ApiRequestResult
import com.ruiheng.xmuse.core.network.model.ApiRequestSleepInfo
import io.reactivex.rxjava3.core.Observable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NetResourcesBPSessionRepository @Inject constructor(
    private val bpService: BPService,
    private val bpRxService: BPRxService
) {

    fun startSession() =
        flow<Result<ApiRequestBPSessionInfo>> {
            emit(Result.Loading())
            val requestBody = generateRequestDataJson()
            val response = bpService.startSession(requestBody)
            requestCatch(
                response,
                successCallback = { result ->
                    emit(Result.Success(result))
                })
        }.globalCatch().flowOn(Dispatchers.IO)

    fun startRxPushSessionProgress(
        sessionId: String,
        startTime: String,
        busymindMapData: Map<BusymindData, MutableList<Double>>,
    ): Observable<Result<Any?>> {
        val contentJson = JsonObject()
        contentJson.addProperty("reportId", sessionId)
        contentJson.addProperty("startTime", startTime)
        contentJson.addProperty("endTime", getUTCNowTimeString())
        busymindMapData.forEach { (key, valueList) ->
            val array = JsonArray()
            valueList.forEach { array.add(it) }
            contentJson.add(key.requestParam, array)
        }
        Timber.d("startRxPushSessionProgress:${contentJson.toString()}")
        val data = JsonObject()
        data.add("data", contentJson)
        val requestBody =
            data.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
        return bpRxService.pushSleepDataRx(requestBody).map {
            if (it.code != 200) {
                val exception = PetivityThrowable(message = it.msg)
                Result.Error(exception)
            } else {
                Result.Success(true)
            }
        }
    }

    fun rxEndSession(sessionId: String): Observable<Result<ApiRequestBPSessionInfo?>> {
        val requestBody = generateRequestDataJson(
            Pair("reportId", sessionId),
        )
        return bpRxService.endSleepSessionRx(requestBody).map { it.toResult() }
    }
}


