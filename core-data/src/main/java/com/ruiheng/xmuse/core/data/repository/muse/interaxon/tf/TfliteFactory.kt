package com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf

import android.content.Context
import android.content.res.AssetManager
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.internal.Classifier
import com.choosemuse.libmuse.internal.DataWindowBuffer
import com.choosemuse.libmuse.internal.Presenter
import com.choosemuse.libmuse.internal.TfliteModelConsts
import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel
import dagger.hilt.android.scopes.ActivityRetainedScoped
import java.nio.ByteBuffer
import java.nio.ByteOrder
import javax.inject.Inject
import javax.inject.Singleton


@ActivityRetainedScoped
class TfliteFactory
@Inject constructor() {
    companion object {
        private const val TAG = "TfliteFactory"

        fun getData(assets: AssetManager, filename: String): ByteArray {
            val model = assets.open(filename).readBytes()
            val buffer = ByteBuffer.allocateDirect(model.size).order(ByteOrder.nativeOrder())
            val data = ByteArray(model.size)
            buffer.put(model).position(0)
            buffer.get(data)
            return data
        }
    }

    fun createBuffer(filename: String, hopSize: Long, hopInputType: String): DataWindowBuffer? {
        return try {
            DataWindowBuffer.createFromTfliteModelData(
                getData(Utils.getApp().assets, "$filename.tflite"),
                hopSize,
                hopInputType
            )
        } catch (e: Exception) {
//            logError(TAG, "", e)
            null
        }
    }

    fun createClassifier(filename: String): Classifier? {
        return try {

            Classifier.createWithData(getData(Utils.getApp().assets, "$filename.tflite"))
        } catch (e: Exception) {
//            logError(TAG, "", e)
            null
        }
    }

    fun createDeepSleepIntensityEegBuffer(useLegacy: Boolean = false) =
        DataWindowBuffer.createFromTfliteModelData(
            getData(
                Utils.getApp().assets,
                if (useLegacy) "legacy_tf_deep_sleep_intensity.tflite" else "tf_deep_sleep_intensity.tflite"
            ),
            TfliteModelConsts.EEG_SAMPLING_RATE * TfliteModelConsts.DEEP_SLEEP_INTENSITY_EEG_HOP_SECS,
            TfliteModelConsts.EEG_LABEL
        )!!

    fun createSleepStageEegBuffer(classifier: TfliteSleepStageClassifierModel): DataWindowBuffer {
        return DataWindowBuffer.createFromTfliteModelData(
            getData(Utils.getApp().assets, classifier.filename),
            TfliteModelConsts.EEG_SAMPLING_RATE * TfliteModelConsts.SLEEP_STAGES_EEG_HOP_SECS,
            TfliteModelConsts.EEG_LABEL
        )!!
    }

    fun createAccBuffer() = DataWindowBuffer.createFromTfliteModelData(
        getData(
            Utils.getApp().assets,
            "tf_sleep_position_classifier.tflite"
        ),
        TfliteModelConsts.ACC_SAMPLING_RATE * 10,
        TfliteModelConsts.ACC_LABEL
    )!!

    fun createSleepStagesClassifier(classifier: TfliteSleepStageClassifierModel): Classifier {
        return Classifier.createWithData(
            getData(
                Utils.getApp().assets,
                classifier.filename
            )
        )!!
    }

    fun createSleepPositionsClassifier() = Classifier.createWithData(
        getData(
            Utils.getApp().assets,
            "tf_sleep_position_classifier.tflite"
        )
    )!!

    fun createDeepSleepIntensityClassifier(useLegacy: Boolean = false) = Classifier.createWithData(
        getData(
            Utils.getApp().assets,
            if (useLegacy) {
                "legacy_tf_deep_sleep_intensity.tflite"
            } else {
                "tf_deep_sleep_intensity.tflite"
            }
        )
    )!!

    fun createPresenter(presenter: TflitePresenterModel): Presenter {
        return Presenter.createWithData(getData(Utils.getApp().assets, presenter.tfliteFilename))!!
    }
}