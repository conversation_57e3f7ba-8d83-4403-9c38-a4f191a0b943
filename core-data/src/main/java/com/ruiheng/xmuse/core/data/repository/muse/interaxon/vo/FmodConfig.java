// AUTOGENERATED FILE - DO NOT MODIFY!
// This file generated by <PERSON><PERSON><PERSON> from muse.djinni

package com.ruiheng.xmuse.core.data.repository.muse.interaxon.vo;

import com.choosemuse.libmuse.internal.RealtimeModel;

import java.util.ArrayList;
public final class FmodConfig {


    /*package*/ final String fmodEvent;

    /*package*/ final String soundscapeContentId;

    /*package*/ final ArrayList<Integer> fmodMap;

    /*package*/ final ArrayList<RealtimeModel> realTimeModels;

    /*package*/ final ArrayList<NfbControl> controls;

    /*package*/ final String sessionType;

    /*package*/ final String initialBusymindMode;

    /*package*/ final Boolean requiresConnectedMuseOnModuleStart;

    public FmodConfig(
            String fmodEvent,
            String soundscapeContentId,
            ArrayList<Integer> fmodMap,
            ArrayList<RealtimeModel> realTimeModels,
            ArrayList<NfbControl> controls,
            String sessionType,
            String initialBusymindMode,
            Boolean requiresConnectedMuseOnModuleStart) {
        this.fmodEvent = fmodEvent;
        this.soundscapeContentId = soundscapeContentId;
        this.fmodMap = fmodMap;
        this.realTimeModels = realTimeModels;
        this.controls = controls;
        this.sessionType = sessionType;
        this.initialBusymindMode = initialBusymindMode;
        this.requiresConnectedMuseOnModuleStart = requiresConnectedMuseOnModuleStart;
    }

    public String getFmodEvent() {
        return fmodEvent;
    }

    public String getSoundscapeContentId() {
        return soundscapeContentId;
    }

    public ArrayList<Integer> getFmodMap() {
        return fmodMap;
    }

    public ArrayList<RealtimeModel> getRealTimeModels() {
        return realTimeModels;
    }

    public ArrayList<NfbControl> getControls() {
        return controls;
    }

    public String getSessionType() {
        return sessionType;
    }

    public String getInitialBusymindMode() {
        return initialBusymindMode;
    }

    public Boolean getRequiresConnectedMuseOnModuleStart() {
        return requiresConnectedMuseOnModuleStart;
    }

    /**
     * Returns a string representation of this object.
     *
     * @return a string representation of this object.
     */
    @Override
    public String toString() {
        return "FmodConfig{" +
                "fmodEvent=" + fmodEvent +
                "," + "soundscapeContentId=" + soundscapeContentId +
                "," + "fmodMap=" + fmodMap +
                "," + "realTimeModels=" + realTimeModels +
                "," + "controls=" + controls +
                "," + "sessionType=" + sessionType +
                "," + "initialBusymindMode=" + initialBusymindMode +
                "," + "requiresConnectedMuseOnModuleStart=" + requiresConnectedMuseOnModuleStart +
        "}";
    }

}
