package com.ruiheng.xmuse.core.data.repository.muse.interaxon.tf

enum class TfliteSleepStageClassifierModel {
    DSN,
    VALIDATED,
    MNET,
    MNET_V2B,
    COMBINED_FFT_SQC,
    TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_888_1059_DSN_891_1092,
    TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_1179_1314_DSN_1157_1233_EPOCH_10S_STEP_10S;

    val filename: String
        get() {
            return when (this) {
                DSN -> "tf_sleep_stage_classifier.tflite"
                VALIDATED -> "tf_validated_sleep_stage_classifier.tflite"
                MNET -> "mnet_tf_sleep_stage_classifier.tflite"
                MNET_V2B -> "mnet_v2b_basic_tf_sleep_stage_classifier.tflite"
                COMBINED_FFT_SQC -> "tf_sleep_stage_classifier_combined_fft_sqc.tflite"
                TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_888_1059_DSN_891_1092 ->
                    "trained_models_sleep_tflite_models_ss_dsp_mnet_888_1059_dsn_891_1092_tf_sleep_stage_classifier_dsp.tflite"
                TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_1179_1314_DSN_1157_1233_EPOCH_10S_STEP_10S ->
                    "trained_models_sleep_tflite_models_ss_dsp_mnet_1179_1314_dsn_1157_1233_epoch_10s_step_10s_tf_sleep_stage_classifier_dsp.tflite"
            }
        }

    val requiresPreprocessedFft: Boolean
        get() {
            return when (this) {
                DSN,
                VALIDATED,
                MNET,
                MNET_V2B,
                TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_888_1059_DSN_891_1092,
                TRAINED_MODELS_SLEEP_TFLITE_MODELS_SS_DSP_MNET_1179_1314_DSN_1157_1233_EPOCH_10S_STEP_10S -> false
                COMBINED_FFT_SQC -> true
            }
        }
}