package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

/**
 * 计算各个分段的平局占比，计算方法 传入当前完成的总时长(秒)completedSeconds，结合timeseries的大小可知每段时长，因此可以计算
 * 传入的ragnges为分段区间，可通过比对数据进行分段计算
 */
object MuseDataUtils {
    fun calcSecondsPerRange(
        timeseries: List<Double>,
        completedSeconds: Long,
        ranges: List<Double>
    ): List<Long> {
        val period = completedSeconds / timeseries.size.toDouble()
        val secs = MutableList(ranges.size - 1) { 0.0 }

        for (value in timeseries) {
            for (i in (ranges.size - 2) downTo 0) {
                if (value >= ranges[i] && value <= ranges[i + 1]) {
                    secs[i] += period
                    break
                }
            }
        }

        return round(secs)
    }

    private fun round(values: List<Double>): List<Long> {
        var roundUp = true
        return values.map { value ->
            val rounded = kotlin.math.round(value)
            if (rounded - value == 0.5) {
                val result = if (roundUp) rounded else rounded - 1
                roundUp = !roundUp
                result.toLong()
            } else {
                rounded.toLong()
            }
        }
    }

    fun calcPercentages(seconds: List<Long>): List<Long> {
        val sum = seconds.sum()
        if (sum <= 0) {
            return List(seconds.size) { 0L }
        }
        val percentages = seconds.map { 100.0 * it / sum }
        return round(percentages)
    }
}