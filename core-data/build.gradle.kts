/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@Suppress("DSL_SCOPE_VIOLATION") // Remove when fixed https://youtrack.jetbrains.com/issue/KTIJ-19369
plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)

}

android {
    namespace = "${rootProject.ext.get("namespacePrefix")}.core.data"
    compileSdk = 34

    defaultConfig {
        minSdk = 26

        testInstrumentationRunner = "${rootProject.ext.get("namespacePrefix")}.core.testing.HiltTestRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("libs")
        }
    }

    buildFeatures {
        aidl = false
        buildConfig = false
        renderScript = false
        shaders = false
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

//    packagingOptions {
//        resources {
//            pickFirsts.add("lib/arm64-v8a/libtensorflowlite_jni.so")
//            pickFirsts.add("lib/armeabi-v7a/libtensorflowlite_jni.so")
//            pickFirsts.add("lib/x86/libtensorflowlite_jni.so")
//            pickFirsts.add("lib/x86_64/libtensorflowlite_jni.so")
//            // 添加其他可能冲突的文件
//        }
//    }
}

dependencies {
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar","*.jar"))))

    api(project(":core-database"))
    api(project(":core-network"))
    implementation(project(":core-common"))
    implementation(libs.other.threetenabp)
    api(libs.other.wechat.core)

    // Arch Components
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    implementation(libs.other.tensorflow.lite)

    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.other.rx.android)
    implementation(libs.other.rx.java)
    implementation(libs.other.aliyun.oss)

    // Local tests: jUnit, coroutines, Android runner
    testImplementation(libs.junit)
    testImplementation(libs.kotlinx.coroutines.test)

}
