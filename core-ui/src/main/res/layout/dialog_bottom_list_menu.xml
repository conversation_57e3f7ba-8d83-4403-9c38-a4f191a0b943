<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/half_normal_margin"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            android:textColor="?colorOnPrimaryContainer"
            android:textSize="@dimen/font_title_large"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/choose_photo_type"
            tools:visibility="visible" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_menu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:itemCount="2"
            tools:listitem="@layout/item_public_menu_row" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_bottom_cancel"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:text="@string/Cancel"
            android:textColor="?colorOnSurface"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@android:color/transparent"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:background="?colorSurfaceContainer"
            app:layout_constraintTop_toBottomOf="@id/rv_menu" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>