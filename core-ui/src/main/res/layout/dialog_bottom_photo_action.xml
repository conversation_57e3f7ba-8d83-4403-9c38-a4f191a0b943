<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/half_normal_margin"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/space24"
        android:paddingEnd="@dimen/space24"
        android:paddingBottom="@dimen/activity_vertical_margin"
        app:layout_constraintTop_toBottomOf="@id/drag_handle">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?colorOnPrimaryContainer"
            android:textSize="@dimen/font_title"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/choose_photo_type" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_bottom_take_photo_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:text="@string/take_a_photo"
            android:textColor="?colorOnPrimary"
            android:textSize="@dimen/font_medium"
            app:backgroundTint="?colorPrimary"
            app:cornerRadius="12dp"
            app:layout_constraintDimensionRatio="6.2:1"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/bt_bottom_choose_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/half_normal_margin"
            android:text="@string/upload_from_phone"
            android:textColor="?colorPrimary"
            android:textSize="@dimen/font_medium"
            app:backgroundTint="@android:color/transparent"
            app:cornerRadius="12dp"
            app:layout_constraintDimensionRatio="6.2:1"
            app:layout_constraintTop_toBottomOf="@id/bt_bottom_take_photo_action"
            app:strokeColor="?colorAccent"
            app:strokeWidth="1dp" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>