<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black26">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_public_error_dialog_bg"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingTop="28dp"
        android:layout_marginStart="@dimen/space48"
        android:layout_marginEnd="@dimen/space48"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingBottom="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_dialog_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?colorOnPrimaryContainer"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/tv_dialog_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Delete schedule?" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_dialog_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/half_normal_margin"
            android:lineSpacingExtra="4dp"
            android:textColor="?colorOnSurface"
            app:layout_constraintTop_toBottomOf="@id/tv_dialog_title"
            tools:text="meals will be canceled." />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_dialog_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:backgroundTint="@color/red500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_dialog_content"
            tools:text="Leave now" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_dialog_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?colorOnSecondaryContainer"
            app:backgroundTint="@android:color/transparent"
            app:layout_constraintEnd_toStartOf="@id/btn_dialog_sure"
            app:layout_constraintTop_toTopOf="@id/btn_dialog_sure"
            tools:text="Cancel" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>