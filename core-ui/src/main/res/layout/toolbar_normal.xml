<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <View
        android:id="@+id/view_toolbar_gradient"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_cl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="68dp"
        app:layout_constraintTop_toBottomOf="@id/view_toolbar_gradient">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_toolbar_back"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/half_normal_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_toolbar_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_public_left_back"
            app:tint="?colorOnSurface" />

        <TextView
            android:id="@+id/tv_toolbar_title"
            style="?TextTitleStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:lineSpacingExtra="2dp"
            app:layout_constraintBottom_toBottomOf="@+id/btn_toolbar_back"
            app:layout_constraintEnd_toStartOf="@+id/btn_toolbar_more"
            app:layout_constraintStart_toEndOf="@+id/btn_toolbar_back"
            app:layout_constraintTop_toTopOf="@+id/btn_toolbar_back"
            tools:text="妙诗App"
            tools:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_toolbar_more"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:padding="6dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="@+id/tv_toolbar_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_toolbar_title"
            app:layout_constraintTop_toTopOf="@+id/tv_toolbar_title"
            app:tint="?colorOnSurface" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_toolbar_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true"
            android:textColor="?colorOnPrimaryContainer"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tv_toolbar_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_toolbar_title"
            app:layout_constraintTop_toTopOf="@+id/tv_toolbar_title"
            tools:text="Skip" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.divider.MaterialDivider
        android:id="@+id/divider_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>