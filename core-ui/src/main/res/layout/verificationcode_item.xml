<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:paddingEnd="8dp">

    <com.ruiheng.xmuse.core.ui.widget.verify.VerificationCodeEditText
        android:id="@+id/tv_code"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/shape_verification_code_text_complete"
        android:gravity="center"
        android:textSize="24sp"
        android:textCursorDrawable="@android:color/transparent"
        android:inputType="number"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>