<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="option"
            type="com.ruiheng.xmuse.core.ui.custom.PublicOptionImp" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_corner_surface_bg"
            android:padding="16dp"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_public_option"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_public_option"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@android:drawable/presence_away" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="?colorOnPrimaryContainer"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tv_public_option_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/img_public_option"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Less Active" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_public_option"
                app:layout_constraintTop_toBottomOf="@id/tv_public_option"
                tools:text="No climbing or jumping; only napping" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
