<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?colorSurfaceContainer"
    android:paddingBottom="24dp">


    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_bottom_action"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:textSize="16sp"
        app:iconTint="@color/white"
        app:layout_constraintBottom_toTopOf="@id/bt_bottom_sub_action"
        app:layout_constraintDimensionRatio="6.5:1"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="I’m ready" />

    <TextView
        android:id="@+id/space_tv_bottom_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/transparent"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/bt_bottom_action"
        app:layout_constraintEnd_toEndOf="@id/bt_bottom_action"
        app:layout_constraintStart_toStartOf="@id/bt_bottom_action"
        app:layout_constraintTop_toTopOf="@id/bt_bottom_action"
        tools:text="I’m ready" />

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/loading_bottom_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/half_normal_margin"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="@color/white"
        app:indicatorInset="2dp"
        app:indicatorSize="20dp"
        app:layout_constraintBottom_toBottomOf="@id/bt_bottom_action"
        app:layout_constraintEnd_toStartOf="@id/space_tv_bottom_action"
        app:layout_constraintTop_toTopOf="@id/bt_bottom_action"
        app:trackThickness="2dp"
        tools:indeterminate="false"
        tools:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_bottom_sub_action"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintDimensionRatio="6.5:1"
        app:layout_constraintTop_toBottomOf="@id/bt_bottom_action"
        tools:text="I’m ready" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_bottom_sub_action3"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintDimensionRatio="6.5:1"
        app:layout_constraintTop_toBottomOf="@id/bt_bottom_sub_action"
        tools:text="I’m ready" />

    <com.google.android.material.divider.MaterialDivider
        android:id="@+id/divider_bottom_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>