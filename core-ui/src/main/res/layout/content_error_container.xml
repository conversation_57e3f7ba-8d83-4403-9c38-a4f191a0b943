<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?colorSurface">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_error_container"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:scaleType="fitXY"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/img_connection_fail" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_error_container_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_medium"
            app:layout_constraintTop_toBottomOf="@id/img_error_container"
            tools:text="No schedules... yet!" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_error_container_sub_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:textColor="?colorOnSurfaceVariant"
            app:layout_constraintTop_toBottomOf="@id/tv_error_container_content"
            tools:text="All kitten aside, let’s get your cat set up with some meals." />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>