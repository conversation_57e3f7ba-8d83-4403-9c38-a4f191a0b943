<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/toolbar_space_view"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/app_bar" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/view_navigation_space"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

    </LinearLayout>

    <include
        android:id="@+id/app_bar"
        layout="@layout/toolbar_normal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:id="@+id/view_navigation_space"
        android:layout_width="match_parent"
        android:layout_height="1px"

        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>