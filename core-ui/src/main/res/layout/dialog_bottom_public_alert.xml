<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/activity_horizontal_margin">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_result"
            android:layout_width="32dp"
            android:layout_height="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_result_alert" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/activity_horizontal_margin"
            android:textColor="?colorOnPrimaryContainer"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/img_result"
            tools:text="The bowl is full.Let’s wait until they eat some. " />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_bottom_action"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/half_normal_margin"
        android:text="@string/Cancel"
        android:textColor="?colorOnSecondaryContainer"
        android:textSize="16sp"
        app:backgroundTint="@android:color/transparent"
        app:layout_constraintDimensionRatio="6.5:1"
        app:layout_constraintTop_toBottomOf="@id/layout_container" />
</androidx.constraintlayout.widget.ConstraintLayout>