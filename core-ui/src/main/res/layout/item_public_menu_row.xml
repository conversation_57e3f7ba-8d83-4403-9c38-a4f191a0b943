<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="option"
            type="com.ruiheng.xmuse.core.ui.custom.PublicMenuImp" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_corner_surface_bg"
            android:padding="16dp"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_public_option"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginEnd="8dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@android:drawable/presence_away" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_medium"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/img_public_option"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Less Active" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:gravity="end"
                android:textColor="?colorOnSurfaceVariant"
                android:textSize="@dimen/font_normal"
                app:layout_constraintBottom_toBottomOf="@id/tv_public_option"
                app:layout_constraintEnd_toStartOf="@id/img_public_menu_end"
                app:layout_constraintStart_toEndOf="@id/tv_public_option"
                app:layout_constraintTop_toTopOf="@id/tv_public_option"
                tools:text="NNNNNNNN" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_public_option_content"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tv_public_option"
                app:layout_constraintEnd_toStartOf="@id/img_public_menu_end"
                app:layout_constraintTop_toTopOf="@id/tv_public_option"
                tools:src="@drawable/ico_avatar_default" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_public_menu_end"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:padding="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_public_action_arrow_hor" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
