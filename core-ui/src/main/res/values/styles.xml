<resources>
    <style name="in_LoadingIndicatorView">
        <item name="in_minWidth">48dp</item>
        <item name="in_maxWidth">48dp</item>
        <item name="in_minHeight">48dp</item>
        <item name="in_maxHeight">48dp</item>
        <item name="in_indicatorName">InLineScalePulseOutRapidIndicator</item>
    </style>

    <style name="TextTitleStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?colorOnPrimaryContainer</item>
        <item name="android:gravity">center</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="PetivityToggleButtonGroup" parent="Widget.Material3.MaterialButtonToggleGroup">
        <item name="android:spacing">12dp</item>
    </style>

    <style name="PetivityToggleButton" parent="Widget.Material3.MaterialButtonToggleGroup">
        <item name="android:spacing">12dp</item>
    </style>

    <style name="Widget.Petivity.Switch" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Petivity.Switch</item>
    </style>

    <style name="ThemeOverlay.Petivity.Switch" parent="">
        <item name="colorOutline">@color/white</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSurfaceContainerHighest">@color/white</item>
        <item name="colorPrimary">@color/colorAccent</item>

    </style>

    <style name="CommomDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--<item name="android:windowContentOverlay">@null</item>-->
        <!--<item name="android:windowTitleStyle">@null</item>-->
        <!--<item name="android:colorBackgroundCacheHint">@null</item>-->
        <!--<item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>-->
        <!--<item name="android:backgroundDimEnabled">true</item>-->
        <!--<item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>-->
        <item name="android:gravity">top</item>
        <!--<item name="android:backgroundDimAmount">0.6</item>-->
    </style>

    <style name="ThemeOverlay.App.BottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="colorSurfaceContainerLow">#FFFFFE</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorSurfaceDim">@color/grey50</item>
    </style>


    <style name="PetivityPickerView">
        <item name="np_wheelItemCount">9</item>
        <item name="np_itemSpacing">4dp</item>
        <item name="np_dividerColor">@color/grey100</item>
        <item name="np_dividerDistance">28sp</item>
        <item name="np_selectedTextColor">@color/grey900</item>
        <item name="np_selectedTextSize">24sp</item>
        <item name="np_textColor">?colorOnSurfaceVariant</item>
        <item name="np_textSize">18sp</item>
        <item name="np_wrapSelectorWheel">false</item>

    </style>

    <!-- Global TextInputStyle-->
    <style name="Widget.Petivity.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxBackgroundColor">@color/white</item>
        <item name="boxCornerRadiusBottomEnd">16dp</item>
        <item name="boxCornerRadiusBottomStart">16dp</item>
        <item name="boxCornerRadiusTopEnd">16dp</item>/
        <item name="boxCornerRadiusTopStart">16dp</item>

    </style>

    <style name="Widget.Petivity.CardView.Filled" parent="Widget.Material3.CardView.Filled">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Card</item>
        <item name="colorSurfaceDim">@color/grey50</item>
    </style>

    <style name="Widget.Petivity.CardView.Outlined" parent="Widget.Material3.CardView.Outlined">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Card</item>
    </style>

    <style name="Widget.Petivity.CardView.Elevated" parent="Widget.Material3.CardView.Elevated">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Card.Elevated</item>
    </style>

    <style name="ThemeOverlay.App.Card" parent="Base.Theme.Petivity">
        <!--CardView Elevated 模式下背景色由colorSurface控制，边框为colorOutline -->
        <item name="colorSecondary">@color/white</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorSurfaceContainerLow">@color/white</item>
    </style>

    <style name="ThemeOverlay.App.Card.Elevated" parent="Base.Theme.Petivity">
        <!--CardView Elevated 模式下背景色由colorSurfaceContainerLow控制-->
        <item name="colorSurfaceContainerLow">@color/white</item>
    </style>

    <style name="ThemeOverlay.App.Progress" parent="Base.Theme.Petivity">
        <!--CardView Elevated 模式下背景色由colorSurfaceContainerLow控制-->
        <item name="colorAccent">@color/white</item>
    </style>

    <style name="Widget.App.Button" parent="Widget.Material3.Button">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Button</item>
        <item name="android:textAppearance">@style/TextAppearance.App.Button</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.Button</item>
    </style>

    <style name="Widget.App.Button.TextButton" parent="Widget.Material3.Button.TextButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.App.Button.TextButton</item>
        <item name="android:textAppearance">@style/TextAppearance.App.Button</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.Button</item>
    </style>

    <style name="ShapeAppearance.App.Button" parent="">
        <item name="cornerSize">12dp</item>
    </style>

    <style name="ThemeOverlay.App.Button" parent="ThemeOverlay.Material3.Button">
        <item name="colorContainer">@color/colorPrimary</item>
        <item name="colorOnContainer">@color/white</item>
    </style>

    <style name="ThemeOverlay.App.Button.TextButton" parent="ThemeOverlay.Material3.Button.TextButton">
        <item name="colorOnSurface">@color/grey900</item>
        <item name="colorOnContainer">@color/grey900</item>
    </style>

    <style name="TextAppearance.App.Button" parent="TextAppearance.Material3.LabelLarge">

    </style>

    <style name="SleepPickerView">
        <item name="np_wheelItemCount">3</item>
        <item name="np_itemSpacing">4dp</item>
        <item name="np_dividerColor">@android:color/transparent</item>
        <item name="np_selectedTextColor">?colorOnSurface</item>
        <item name="np_selectedTextSize">30sp</item>
        <item name="np_textColor">?colorOnSurfaceVariant</item>
        <item name="np_textSize">28sp</item>
        <item name="np_wrapSelectorWheel">false</item>
        <item name="np_typeface">bold</item>
    </style>

    <style name="Widget.Petivity.Switch.Dark" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Petivity.Switch.Dark</item>
    </style>

    <style name="ThemeOverlay.Petivity.Switch.Dark" parent="">
        <item name="colorOutline">@android:color/transparent</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSurfaceContainerHighest">@color/grey800</item>
        <item name="colorPrimary">@color/colorAccent</item>

    </style>

    <style name="TabLayout.Selected.Text" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">20sp</item>
    </style>


    <style name="PopDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/pop_enter</item>
        <item name="android:windowExitAnimation">@anim/pop_exit</item>
    </style>

    <style name="PopDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
</resources>
