<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="TextSizeBody" format="dimension" />
    <attr name="TextSizeBodyLarge" format="dimension" />
    <attr name="TextSizeHeadline" format="dimension" />

    <attr name="TextTitleStyle" format="dimension" />
    <attr name="TextNormalStyle" format="dimension" />

    <attr name="color_background_secondary" format="reference|color" />
    <attr name="color_background_primary" format="reference|color" />
    <attr name="color_background_blank" format="reference|color" />

    <attr name="colorAccentBorder" format="reference|color" />

    <attr name="color_text_primary" format="reference|color" />
    <attr name="color_text_secondary" format="reference|color" />

    <declare-styleable name="VerificationCodeLayout">
        <attr name="code_text_color" format="color" />//验证码字体颜色
        <attr name="code_text_size" format="dimension" />//验证码字体大小
        <attr name="code_number" format="integer" />//验证码数量 4位 6位
        <attr name="line_color_default" format="color" />//验证码下面线的默认颜色
        <attr name="line_color_focus" format="color" />//验证码下面线选中后的颜色
    </declare-styleable>

    <declare-styleable name="In_LoadingIndicatorView">
        <attr name="in_minWidth" format="dimension" />
        <attr name="in_maxWidth" format="dimension" />
        <attr name="in_minHeight" format="dimension" />
        <attr name="in_maxHeight" format="dimension" />
        <attr name="in_indicatorName" format="string" />
        <attr name="in_indicatorColor" format="color" />
    </declare-styleable>
    <declare-styleable name="voiceView">
        <!--中间线的颜色，就是波形的时候，大家可以看到，中间有一条直线，就是那个-->
        <attr name="middleLine" format="color" />
        <!--中间线的高度，因为宽度是充满的-->
        <attr name="middleLineHeight" format="dimension" />
        <!--波动的线的颜色，如果是距形样式的话，刚是距形的颜色-->
        <attr name="voiceLine" format="color" />
        <!--波动线的横向移动速度，线的速度的反比，即这个值越小，线横向移动越快，越大线移动越慢，默认90-->
        <attr name="lineSpeed" format="integer" />
        <!--矩形的宽度-->
        <attr name="rectWidth" format="dimension" />
        <!--矩形之间的间隔-->
        <attr name="rectSpace" format="dimension" />
        <!--矩形的初始高度，就是没有声音的时候，矩形的高度-->
        <attr name="rectInitHeight" format="dimension" />
        <!--所输入音量的最大值-->
        <attr name="maxVolume" format="float" />
        <!--控件的样式，一共有两种，波形或者矩形-->
        <attr name="viewMode">
            <enum name="line" value="0" />
            <enum name="rect" value="1" />
        </attr>
        <!--灵敏度，默认值是4-->
        <attr name="sensibility">
            <enum name="one" value="1" />
            <enum name="two" value="2" />
            <enum name="three" value="3" />
            <enum name="four" value="4" />
            <enum name="five" value="5" />
        </attr>
        <!--精细度，绘制曲线的时候，每几个像素绘制一次，默认是1，一般，这个值越小，曲线越顺滑，
            但在一些旧手机上，会出现帧率过低的情况，可以把这个值调大一点，在图片的顺滑度与帧率之间做一个取舍-->
        <attr name="fineness">
            <enum name="one" value="1" />
            <enum name="two" value="2" />
            <enum name="three" value="3" />
        </attr>
    </declare-styleable>

    <declare-styleable name="StarTwinklingView">
        <attr name="starCount" format="integer" />
        <attr name="meteorCount" format="integer" />
        <attr name="meteorProbability" format="float" />
    </declare-styleable>

    <declare-styleable name="LongPressButton">
        <attr name="cornerRadius" format="dimension" />
        <attr name="longPressDuration" format="integer" />
    </declare-styleable>
</resources>