<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <set>
            <objectAnimator android:duration="100"
                android:propertyName="scaleX" android:valueTo="0.9"
                android:valueType="floatType"/>
            <objectAnimator android:duration="100"
                android:propertyName="scaleY" android:valueTo="0.9"
                android:valueType="floatType"/>
            <objectAnimator android:duration="100"
                android:propertyName="translationZ" android:valueTo="16dp"
                android:valueType="floatType"/>

        </set>
    </item>
    <item>
        <set>
            <objectAnimator android:duration="@android:integer/config_shortAnimTime"
                android:propertyName="scaleX" android:valueTo="1.0"
                android:valueType="floatType"/>
            <objectAnimator android:duration="@android:integer/config_shortAnimTime"
                android:propertyName="scaleY" android:valueTo="1.0"
                android:valueType="floatType"/>
            <objectAnimator android:duration="@android:integer/config_shortAnimTime"
                android:propertyName="translationZ" android:valueTo="0dp"
                android:valueType="floatType"/>
        </set>
    </item>
</selector>
