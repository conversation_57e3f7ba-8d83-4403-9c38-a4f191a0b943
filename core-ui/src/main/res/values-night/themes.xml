<resources xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Petivity" parent="Theme.Material3.Dark.NoActionBar">
        <!-- Customize your light theme here. -->
        <item name="android:windowBackground">@color/grey900</item>
        <item name="android:colorBackground">@color/grey900</item>
        <item name="colorSurface">@color/grey900</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOnSurfaceVariant">@color/white70</item>

        <item name="colorSurfaceDim">@color/grey800</item>
        <!--        与常规反色-->
        <item name="colorSurfaceInverse">@color/white</item>
        <item name="colorOnSurfaceInverse">@color/grey900</item>

        <item name="colorSurfaceContainerHighest">@color/grey900</item>
        <item name="colorSurfaceContainer">@color/grey800</item>

        <item name="colorSurfaceContainerLow">@color/grey800</item>
        <!-- Toolbar\ActionBar\FloatingActionButton\Button  的背景颜色-->
        <!-- TabLayout选中标签颜色-->
        <item name="colorPrimary">@color/colorPrimary</item>

        <!-- 显示在colorPrimary上的文字等-->
        <item name="colorOnPrimary">@color/white</item>

        <item name="colorPrimaryContainer">@color/grey900</item>
        <item name="colorOnPrimaryContainer">@color/white</item>

        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        <item name="colorSecondaryContainer">@color/colorSecondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/colorOnSecondaryContainer</item>


        <item name="colorOutline">@color/grey600</item>
        <item name="colorOutlineVariant">@color/grey800</item>

        <item name="colorAccent">@color/colorAccent</item>

        <item name="colorErrorContainer">@color/red100</item>
        <item name="colorOnErrorContainer">@color/red800</item>

        <item name="TextTitleStyle">@style/TextTitleStyle</item>
        <item name="android:textSize">16sp</item>

        <item name="materialButtonToggleGroupStyle">@style/PetivityToggleButtonGroup</item>
        <item name="materialSwitchStyle">@style/Widget.Petivity.Switch</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.App.BottomSheetDialog</item>
        <item name="textInputStyle">@style/Widget.Petivity.TextInputLayout</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.Petivity.CardView.Outlined</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.Petivity.CardView.Elevated</item>
        <item name="materialCardViewFilledStyle">@style/Widget.Petivity.CardView.Filled</item>
        <item name="progressBarStyle">@style/ThemeOverlay.App.Progress</item>

        <item name="borderlessButtonStyle">@style/Widget.App.Button.TextButton</item>
        <item name="materialButtonStyle">@style/Widget.App.Button</item>
    </style>

    <style name="Theme.Petivity" parent="Base.Theme.Petivity">

    </style>

    <style name="Theme.TransC" parent="Base.Theme.Petivity.Dark">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="Theme.Petivity.Translucent" parent="Base.Theme.Petivity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="colorSurface">@android:color/transparent</item>

    </style>

</resources>