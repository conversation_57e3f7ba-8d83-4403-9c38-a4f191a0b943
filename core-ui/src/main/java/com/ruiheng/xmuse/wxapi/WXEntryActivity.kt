///*
// * Copyright (C) 2022 The Android Open Source Project
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *      http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//
//package com.ruiheng.xmuse.wxapi
//
//import android.app.Activity
//import android.content.Context
//import android.content.Intent
//import android.os.Bundle
//import android.view.View
//import androidx.activity.viewModels
//import com.blankj.utilcode.util.ToastUtils
//import com.polidea.rxandroidble3.ClientComponent.PlatformConstants
//import com.ruiheng.xmuse.core.common.result.isLoading
//import com.ruiheng.xmuse.core.data.WECHAT_APP_ID
//import com.ruiheng.xmuse.core.data.repository.UserDataRepository
//import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
//import com.ruiheng.xmuse.core.ui.helper.controlLoading
//import com.ruiheng.xmuse.core.ui.ui.BaseActivity
//import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
//import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
//import com.ruiheng.xmuse.feature.login.LoginViewModel
//import com.ruiheng.xmuse.feature.login.R
//import com.ruiheng.xmuse.feature.login.databinding.ActivityWxentryBinding
//import com.tencent.mm.opensdk.constants.Build
//import com.tencent.mm.opensdk.constants.ConstantsAPI
//import com.tencent.mm.opensdk.diffdev.DiffDevOAuthFactory
//import com.tencent.mm.opensdk.diffdev.IDiffDevOAuth
//import com.tencent.mm.opensdk.modelbase.BaseReq
//import com.tencent.mm.opensdk.modelbase.BaseResp
//import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat
//import com.tencent.mm.opensdk.modelmsg.SendAuth
//import com.tencent.mm.opensdk.openapi.IWXAPI
//import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
//import com.tencent.mm.opensdk.openapi.WXAPIFactory
//import dagger.hilt.android.AndroidEntryPoint
//import timber.log.Timber
//
//@AndroidEntryPoint
//class WXEntryActivity : BaseBindingActivity<ActivityWxentryBinding>(), IWXAPIEventHandler {
//    override fun bindDataBindingView() = ActivityWxentryBinding.inflate(layoutInflater)
//
//    private var isCodeLogin: Boolean = false
//    private var isCodeBtn: Boolean = false
//    private var isShare: Boolean = false
//    private var jumpToMiniProgram: Boolean = false
//    private var jumpWXService: Boolean = false
//    private var jumpToMiniProgramType: String? = null
//    private var jumpToMiniProgramPath: String? = null
//    private lateinit var iwxApi: IWXAPI
//    private lateinit var wxoauth: IDiffDevOAuth
//    private val loginViewModel: LoginViewModel by viewModels()
//
//    companion object {
//
//        var onWxRespCallback: ((String) -> Unit)? = null
//
//        const val BUNDLE_WX_LOGIN_CODE = "wxCodeLogin"
//        const val BUNDLE_WX_SHARE_CODE = "wxCodeShare"
//        const val BUNDLE_WX_CODE_BTN = "wxCodeBtn"
//        const val BUNDLE_WX_JUMP_MINI_PROGRAM = "jumpToMiniProgram"
//        const val BUNDLE_WX_JUMP_SERVICE = "jumpService"
//        const val BUNDLE_WX_JUMP_MINI_PROGRAM_TYPE = "jumpToMiniProgramType"
//
//        const val KEY_JUMP_TO_CHARGROUP = "2"
//        const val KEY_JUMP_TO_OFFICIAL = "1"
//        const val BUNDLE_WX_JUMP_MINI_PROGRAM_PATH = "jumpToMiniProgramPath"
//
//        const val PATH_MACHINE_GROUP = "chatgroup/chatgroup"
//        const val PATH_MACHINE_OFFICIAL = "wxofficial/wxofficial"
//
//
//        fun startActivity(
//            context: BaseActivity,
//            codeLogin: Boolean = false,
//            isShare: Boolean = false,
//            isCodeBtn: Boolean = false
//        ): Intent {
//            val starter = Intent(context, WXEntryActivity::class.java)
//            starter.putExtra(BUNDLE_WX_LOGIN_CODE, codeLogin)
//            starter.putExtra(BUNDLE_WX_SHARE_CODE, isShare)
//            starter.putExtra(BUNDLE_WX_CODE_BTN, isCodeBtn)
//            return starter
////            context.startActivity(starter)
////            context.delayStartActivity(starter)
//        }
//
////        fun jumpMiniProgram(context: Context, param: String = KEY_JUMP_TO_OFFICIAL) {
////            val starter = Intent(context, WXEntryActivity::class.java)
////            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM, true)
////            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM_TYPE, param)
////            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM_PATH, PATH_MACHINE_OFFICIAL)
////            context.startActivity(starter)
////        }
//
//        //跳转至小程序-公众号二维码
//        fun jumpMiniOfficial(context: Context) {
//            val starter = Intent(context, WXEntryActivity::class.java)
//            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM, true)
//            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM_TYPE, KEY_JUMP_TO_OFFICIAL)
//            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM_PATH, PATH_MACHINE_OFFICIAL)
//            context.startActivity(starter)
//        }
//
//        //跳转至小程序-粉丝群二维码
//        fun jumpMachineGroup(context: Context, machineType: Int) {
//            val starter = Intent(context, WXEntryActivity::class.java)
//            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM, true)
//            starter.putExtra(BUNDLE_WX_JUMP_MINI_PROGRAM_TYPE, KEY_JUMP_TO_CHARGROUP)
//            starter.putExtra(
//                BUNDLE_WX_JUMP_MINI_PROGRAM_PATH,
//                "${PATH_MACHINE_GROUP}?machineType=${machineType}"
//            )
//            context.startActivity(starter)
//        }
//
//        //跳转至微信客服
//        fun jumpWXService(context: BaseActivity) {
//            val starter = Intent(context, WXEntryActivity::class.java)
//            starter.putExtra(BUNDLE_WX_JUMP_SERVICE, true)
//            context.startActivity(starter)
//        }
//    }
//
//    override fun initData(bundle: Bundle?) {
//        super.initData(bundle)
//        isCodeLogin = bundle?.getBoolean(BUNDLE_WX_LOGIN_CODE, false) ?: false
//        isShare = bundle?.getBoolean(BUNDLE_WX_SHARE_CODE, false) ?: false
//        isCodeBtn = bundle?.getBoolean(BUNDLE_WX_CODE_BTN, false) ?: false
//        jumpToMiniProgram = bundle?.getBoolean(BUNDLE_WX_JUMP_MINI_PROGRAM, false) ?: false
//        jumpToMiniProgramType =
//            bundle?.getString(BUNDLE_WX_JUMP_MINI_PROGRAM_TYPE, KEY_JUMP_TO_OFFICIAL)
//        jumpToMiniProgramPath =
//            bundle?.getString(BUNDLE_WX_JUMP_MINI_PROGRAM_PATH, PATH_MACHINE_OFFICIAL)
//        jumpWXService = bundle?.getBoolean(BUNDLE_WX_JUMP_SERVICE, false) ?: false
//
//    }
//
//    override fun initView(saveInsBundle: Bundle?, contentView: View) {
//        super.initView(saveInsBundle, contentView)
//        iwxApi = WXAPIFactory.createWXAPI(this, WECHAT_APP_ID, false)
//        iwxApi.handleIntent(intent, this)
//        wxoauth = DiffDevOAuthFactory.getDiffDevOAuth()
//
//        if (jumpWXService) {
//            //TODO 跳转微信客服
////            if (!wxReViewModel.isWXAppInstalled() || iwxApi.wxAppSupportAPI < Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {
////                layout_root.visibility = View.GONE
////                getWeChatCodeDialog(
////                    supportFragmentManager,
////                    getString(R.string.contact_the_official_customer_service_of_quxueban),
////                    weixinServiceUrl,
////                    { finish() },
////                    saveCallback = { container ->
////                        saveImage(container)
////                    }
////                )
////                return
////            }
//            val sereviceReqs = WXOpenCustomerServiceChat.Req()
//            sereviceReqs.corpId = "ww723fae41f6b652be"
//            sereviceReqs.url = "https://work.weixin.qq.com/kfid/kfc213d406e4b5392c6"
//            iwxApi.sendReq(sereviceReqs)
//            return
//        }
//
//        if (jumpToMiniProgram) {
//            //TODO 跳转小程序
////            if (!wxReViewModel.isWXAppInstalled()) {
////                val title = hashMapOf(
////                    KEY_JUMP_TO_OFFICIAL to R.string.pay_attention_to_quxueban_official_account,
////                    KEY_JUMP_TO_CHARGROUP to R.string.join_quxueban_weixin_group
////                )
////                val url = if (jumpToMiniProgramType == KEY_JUMP_TO_OFFICIAL) weixinAccountUrl else weixinGroupUrl
////                layout_root.visibility = View.GONE
////                getWeChatCodeDialog(
////                    supportFragmentManager,
////                    getString(title[jumpToMiniProgramType]!!),
////                    url,
////                    { finish() },
////                    saveCallback = { container ->
////                        saveImage(container)
////                    }
////                )
////                return
////            }
////            val miniProgramLaunch = WXLaunchMiniProgram.Req()
////            miniProgramLaunch.userName = "gh_cf2078115105"
////            miniProgramLaunch.path = "pages/${jumpToMiniProgramPath}"
////            miniProgramLaunch.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE
////            iwxApi.sendReq(miniProgramLaunch)
////            delayFinishActivity()
//            return
//        }
//
////        if (!loginViewModel.isWXAppInstalled() && !isCodeLogin) {
////            loading_register.visibility = View.GONE
////            ToastUtils.showShort(R.string.install_wx_first)
////            delayFinishActivity()
////        }
//
////        tv_wx_login_cancel.setOnClickListener(this)
////        layout_register_identify.setOnClickListener(this)
//
//        //TODO 获取微信登录图片
////        if (isCodeLogin) {
////            loginViewModel.getCodeAccessToken().observe(this, { accessTokenResult ->
////                if (accessTokenResult.hasData() && !accessTokenResult.data!!.access_token.isNullOrEmpty()) {
////                    Timber.d("$accessTokenResult.data.access_token")
////                    getTicket(accessTokenResult.data.access_token!!)
////                }
////                if (accessTokenResult.isError() ||
////                    (accessTokenResult.hasData() &&
////                            !accessTokenResult.data!!.errmsg.isNullOrEmpty())
////                ) {
////                    Resource.showError(accessTokenResult, R.string.login_failure)
////                    finish()
////                }
////            })
////        }
//    }
//
//    private var shouldFinishSelfHandler: Boolean = false
//    private var shareSuccessToast: Boolean = true
//    override fun onResp(resp: BaseResp) {
//        Timber.d("WxOnResp$resp")
//        shouldFinishSelfHandler = false
//        when (resp.type) {
//            ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX -> {
//                if (resp.errCode == BaseResp.ErrCode.ERR_OK && shareSuccessToast) {
//                    shareSuccessToast = false
////                    ToastUtils.showShort(R.string.success_share)
//                }
//                finish()
//            }
//
//            ConstantsAPI.COMMAND_SENDAUTH -> {
//                when (resp.errCode) {
//                    BaseResp.ErrCode.ERR_OK -> {
//                        onWxRespOk((resp as SendAuth.Resp).code)
//                    }
//
//                    else -> {
//                        ToastUtils.showShort(com.ruiheng.xmuse.core.ui.R.string.authorize_reject)
//                        finish()
//                    }
//                }
//            }
//
//            ConstantsAPI.COMMAND_OPEN_CUSTOMER_SERVICE_CHAT -> {
//                finish()
//            }
//
//            ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM -> {
//                finish()
//            }
//        }
//    }
//
//    override fun onReq(p0: BaseReq) {
//    }
//
//    /**
//     * Got WeChat Authorization And the start getting accessToken from WeChat Server
//     *
//     * @see
//     */
//    private fun onWxRespOk(code: String) {
//        runOnUiThread {
//            val resultIntent = Intent()
//            resultIntent.putExtra(BUNDLE_WX_LOGIN_CODE, code)
//            setResult(Activity.RESULT_OK, resultIntent)
//            onWxRespCallback?.invoke(code)
//            onWxRespCallback = null
//            finish()
////            if (!userToken.isNullOrEmpty()) {
////                autoRemoveFlow(
////                    loginViewModel.bindThirdParty(
////                        code,
////                        UserDataRepository.ThirdPartyLoginType.WeChat.requestType
////                    )
////                ) { result ->
////                    result.controlLoading(this)
////                    if (!result.isLoading()) {
////                        finish()
////                    }
////                }
////            } else {
////                autoRemoveFlow(loginViewModel.loginWithWechat(code)) { result ->
////                    if (result.isSuccessWithData()) {
////                        finish()
////                    }
////                    if (result.isError()) {
////                        result.showResult()
////                        finish()
////                    }
////                }
////            }
//        }
//
////        checkAndRegister(code)
////        val user = personXViewModel.currentUserLiveData.value
////        if (user != null && !user.token.isNullOrEmpty()) {
////            userBindWechat(code, user.token!!)
////        } else {
////            checkAndRegister(code)
////        }
//
//    }
//
//    private fun checkAndRegister(code: String) {
//
////        loginViewModel.checkWxRegister(code).observe(this, Observer { result ->
////            if (result.isSuccess()) {
////                finish()
////            }
////            if (result.isError()) {
////                Resource.showError(result, R.string.login_failure)
////                finish()
////            }
////        })
//    }
//
//
//    override fun onWidgetClick(view: View) {
//        when (view) {
//
//            else -> {
//                super.onWidgetClick(view)
//            }
//        }
//    }
//}
