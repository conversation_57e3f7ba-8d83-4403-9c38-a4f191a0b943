//package com.ruiheng.xmuse.wxapi
//
//data class WxAccessToken(
//    val access_token: String?,
//    val expires_in: String?,
//    val errcode: Int?,
//    val errmsg: String?
//)
//
//data class WxTicket(
//    val ticket: String?,
//    val expires_in: String?,
//    val errcode: Int?,
//    val errmsg: String?
//)
//
//data class WxUserInfo(
//    var openid: String,
//    var nickname: String,
//    var sex: Int,
//    var province: String,
//    var city: String,
//    var country: String,
//    var headimgurl: String?,
//    var privilege: List<String>,
//    var unionid: String,
//    var errcode: String,
//    var errmsg: String
//) {
//    constructor() : this("", "",
//        0, "", "",
//        "", "", emptyList(),
//        "", "", "")
//}