package com.ruiheng.xmuse.core.ui.helper

import kotlin.math.floor

public fun Double.toDisplayText(): String {
    val integerPart = floor(this).toInt()
    val fractionalPart = this - integerPart
    if (integerPart == 0 && fractionalPart == 0.0) return ""
    if (integerPart == 0) return fractionalPart.decimalToFraction()
    if (fractionalPart == 0.0) return "$integerPart"
    return "${integerPart}${fractionalPart.decimalToFraction()}"
}

fun Double?.decimalToFraction(): String {
    fun gcd(a: Int, b: Int): Int {
        return if (b == 0) a else gcd(b, a % b)
    }
    if (this == null) return ""
    val tolerance = 1.0E-6
    var h1 = 1
    var h2 = 0
    var k1 = 0
    var k2 = 1
    var b = this
    do {
        val a = Math.floor(b!!).toInt()
        val aux = h1
        h1 = a * h1 + h2
        h2 = aux
        val aux2 = k1
        k1 = a * k1 + k2
        k2 = aux2
        b = 1 / (b - a)
    } while (Math.abs(this - h1.toDouble() / k1) > this * tolerance)

    val gcd = gcd(h1, k1)
    return "${h1 / gcd}/${k1 / gcd}"
}