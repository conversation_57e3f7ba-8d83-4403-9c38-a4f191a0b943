package com.ruiheng.xmuse.core.ui.ui

import androidx.viewbinding.ViewBinding

abstract class BaseLazyFragment<T : ViewBinding> : BaseBindingFragment<T>() {

    private var isDataLoaded: Boolean = false
    abstract fun doLazyBusiness()
    override fun onResume() {
        super.onResume()
        tryLoadData()
    }

    private fun tryLoadData() {
        if (!isDataLoaded) {
            doLazyBusiness()
            isDataLoaded = true
        }
    }


}