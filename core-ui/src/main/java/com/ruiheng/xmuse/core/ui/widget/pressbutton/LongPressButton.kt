package com.ruiheng.xmuse.core.ui.widget.pressbutton

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.ruiheng.xmuse.core.ui.R
import kotlin.math.min
import androidx.core.graphics.toColorInt

class LongPressButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    val stringToColor = "#FFFFFF".toColorInt()

    // 画笔
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = stringToColor // 默认边框颜色
        style = Paint.Style.STROKE
        strokeWidth = 2f // 默认边框宽度
    }

    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = stringToColor // 默认填充色
        style = Paint.Style.FILL
    }

    // 圆角矩形区域
    private val borderRect = RectF()        // 边框的圆角矩形
    private val progressRect = RectF()      // 进度填充的矩形（用于裁剪）

    // 填充进度（0-1）
    private var progress = 0f

    // 动画控制器
    private var animator: ValueAnimator? = null

    // 长按总时长（毫秒）
    private var longPressDuration = 1500L

    // 圆角大小（dp）
    private var cornerRadius = 12f

    // 长按完成监听器
    private var onLongPressCompleteListener: ((Float, Boolean) -> Unit)? = null

    // 标记长按是否已完成（避免重复触发）
    private var isLongPressCompleted = false

    init {
        // 解析自定义属性
        context.obtainStyledAttributes(attrs, R.styleable.LongPressButton).apply {
            cornerRadius = getDimension(R.styleable.LongPressButton_cornerRadius, cornerRadius)
            longPressDuration = getInteger(
                R.styleable.LongPressButton_longPressDuration,
                longPressDuration.toInt()
            ).toLong()
            recycle()
        }
    }

    private val rectF = RectF()

    private val xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //1.计算边框内缩后的区域（解决边框显示不全问题）
        val strokeWidth = borderPaint.strokeWidth
        borderRect.set(
            strokeWidth / 2,
            strokeWidth / 2,
            width - strokeWidth / 2,
            height - strokeWidth / 2
        )
        borderPaint.color = Color.WHITE
        borderPaint.alpha = 80
        //2.绘制边框
        canvas.drawRoundRect(borderRect, cornerRadius, cornerRadius, borderPaint)
        val saveCount = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)
        rectF.set(0f, 0f, width.toFloat(), height.toFloat())

        // 3. 设置 PorterDuff 模式为 SRC_IN
        progressPaint.color = Color.WHITE
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, progressPaint)
        progressPaint.xfermode = xfermode

        // 4. 绘制源图像（SRC）：一个覆盖整个 View 的矩形
        //    注意：只有与圆角矩形重叠的部分会被显示
        progressPaint.color = stringToColor
        progressPaint.alpha = 80
        canvas.drawRect(0f, 0f, (width) * progress, height.toFloat(), progressPaint)
        progressPaint.alpha = 0
        canvas.drawRect(
            (width) * progress,
            0f,
            width.toFloat(),
            height.toFloat(),
            progressPaint
        )

        // 5. 重置 Xfermode
        progressPaint.xfermode = null

        // 6. 恢复画布状态
        canvas.restoreToCount(saveCount)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isLongPressCompleted = false
                startProgressAnimation()
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                cancelProgressAnimation()
                // 如果长按已完成，则不触发失败回调
                if (!isLongPressCompleted) {
                    onLongPressCompleteListener?.invoke(progress, false)
                }
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    private fun startProgressAnimation() {
        animator?.cancel()
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = longPressDuration
            addUpdateListener {
                progress = it.animatedValue as Float
                // 进度更新回调（无论是否完成）
                onLongPressCompleteListener?.invoke(progress, false)

                // 检查进度是否达到或超过阈值，且未触发过完成回调
                if (progress >= 0.99f && !isLongPressCompleted) {
                    isLongPressCompleted = true
                    onLongPressCompleteListener?.invoke(progress, true)
                    // 触发完成后，可以选择停止动画
                    cancel()
                }

                invalidate()
            }
            start()
        }
    }

    private fun cancelProgressAnimation() {
        animator?.cancel()
        progress = 0f // 取消时长按时重置进度
        invalidate()
    }

    // 设置长按完成监听器
    fun setOnLongPressCompleteListener(listener: (Float, Boolean) -> Unit) {
        onLongPressCompleteListener = listener
    }

    // 设置边框颜色
    fun setBorderColor(color: Int) {
        borderPaint.color = color
        invalidate()
    }

    // 设置填充颜色
    fun setProgressColor(color: Int) {
        progressPaint.color = color
        invalidate()
    }

    // 设置边框宽度
    fun setBorderWidth(width: Float) {
        borderPaint.strokeWidth = width
        invalidate()
    }

    // 设置圆角大小
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }

    // 设置长按持续时间
    fun setLongPressDuration(duration: Long) {
        longPressDuration = duration
    }
}