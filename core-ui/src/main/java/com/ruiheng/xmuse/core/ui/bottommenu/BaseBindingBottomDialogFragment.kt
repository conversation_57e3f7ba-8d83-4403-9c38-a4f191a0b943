package com.ruiheng.xmuse.core.ui.bottommenu

import android.os.Bundle
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.blankj.utilcode.util.ScreenUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.ruiheng.xmuse.core.ui.ui.FullScreenPage
import com.ruiheng.xmuse.core.ui.ui.IBaseView
import timber.log.Timber

abstract class BaseBindingBottomDialogFragment<T : ViewBinding> : BottomSheetDialogFragment(),
    IBaseView {

    private var _binding: T? = null
    override var lastClick: Long? = null
    private var tvTitle: AppCompatTextView? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    protected val binding get() = _binding!!
    private var reload: Boolean = false
    private var isNavigationViewInit = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        if (_binding == null) {
            _binding = bindDataBindingView(inflater, container)
        }
//        val parent = _binding?.root?.parent as ViewGroup?
//        if (parent != null) {
//            reload = true
//            parent.removeAllViewsInLayout()
//            _binding = bindDataBindingView(inflater, container)
//        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (this is FullScreenPage) {
            val param = view.layoutParams
            param.height = ScreenUtils.getAppScreenHeight()
            view.layoutParams = param
            val dialog = dialog
            if (dialog is BottomSheetDialog) {
                dialog.behavior.hideFriction = 0.8f
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }
        super.onViewCreated(view, savedInstanceState)
        initView(savedInstanceState, view)
    }

    abstract fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): T

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
    }

    override fun initData(bundle: Bundle?) {

    }

    override fun bindTitle(): String? = null

    override fun bindTitleRes(): Int? = null

    protected fun setToolbarTitle(updateTitle: String? = null) {
        if (!updateTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = updateTitle
            return
        }
        val bindResTitle = if (bindTitleRes() != null) getString(bindTitleRes()!!) else null
        val mTitle = if (bindTitle() != null) bindTitle() else bindResTitle
        if (!mTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = mTitle
        } else if (!TextUtils.isEmpty(bindTitle())) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = bindTitle()
        } else {
            tvTitle?.visibility = View.INVISIBLE
        }
    }

    override fun onWidgetClick(view: View) {

    }

    fun showDialog(supportFragmentManager: FragmentManager) {
        show(supportFragmentManager, this.javaClass.simpleName)
    }

    private val theme by lazy {
        requireContext().theme
    }

    protected fun getThemeColor(colorAttr: Int?): Int? {
        if (colorAttr != null && theme != null) {
            val typedValue = TypedValue()
            theme!!.resolveAttribute(colorAttr, typedValue, true)
            return typedValue.data
        }
        return null
    }
}
