package com.ruiheng.xmuse.core.ui.rv

import android.content.Context
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ruiheng.xmuse.core.ui.R

class RVLineSpaceItemDecoration(
    context: Context,
    private val leftPadding: Int,
    private val rightPadding: Int
) : RecyclerView.ItemDecoration() {

    private val divider: Drawable? = ContextCompat.getDrawable(context, R.drawable.shape_divider)

    override fun onDraw(c: Can<PERSON>, parent: RecyclerView, state: RecyclerView.State) {
        val left = parent.paddingLeft + leftPadding
        val right = parent.width - parent.paddingRight - rightPadding

        val childCount = parent.childCount
        for (i in 0 until childCount) {
            val child = parent.getChildAt(i)
            val params = child.layoutParams as RecyclerView.LayoutParams
            val top = child.bottom + params.bottomMargin
            val bottom = top + (divider?.intrinsicHeight ?: 0)
            divider?.setBounds(left, top, right, bottom)
            divider?.draw(c)
        }
    }
}