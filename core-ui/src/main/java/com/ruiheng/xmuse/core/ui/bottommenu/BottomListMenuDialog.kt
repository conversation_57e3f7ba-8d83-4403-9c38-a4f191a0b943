package com.ruiheng.xmuse.core.ui.bottommenu

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.custom.PublicMenuImp
import com.ruiheng.xmuse.core.ui.custom.PublicNormalMenuRVAdapter
import com.ruiheng.xmuse.core.ui.ui.autoCleared

class BottomListMenuDialog(
    private val title: Any? = null,
    private val menuList: List<PublicMenuImp>,
    private val menuClickCallback: (PublicMenuImp) -> Unit
) :
    BottomSheetDialogFragment() {
    companion object {
        const val TAG = "BottomMenuDialog"
    }

    private var relateAdapter: PublicNormalMenuRVAdapter<in PublicMenuImp> by autoCleared()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val contentView = inflater.inflate(R.layout.dialog_bottom_list_menu, container, false)
        val tvTitle = contentView.findViewById<AppCompatTextView>(R.id.tv_title)
        val rvMenu = contentView.findViewById<RecyclerView>(R.id.rv_menu)
        rvMenu.layoutManager = LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)

        relateAdapter = PublicNormalMenuRVAdapter { menu ->
            menuClickCallback.invoke(menu)
            dismiss()
        }

        rvMenu.adapter = relateAdapter
        relateAdapter.submitList(menuList)
        val btnCancel = contentView.findViewById<MaterialButton>(R.id.bt_bottom_cancel)
        if (title != null) {
            if (title is Int) {
                tvTitle.setText(title)
            }
            if (title is String) {
                tvTitle.text = title
            }
            tvTitle.visibility = View.VISIBLE
        } else {
            tvTitle.visibility = View.GONE
        }
        btnCancel.setOnClickListener {
            dismiss()
        }


        return contentView
    }

    fun showMenuDialog(supportFragmentManager: FragmentManager) {
        show(supportFragmentManager, TAG)
    }
}