package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.Rect
import android.graphics.drawable.Animatable
import android.graphics.drawable.Drawable

abstract class Indicator : Drawable(), Animatable {
    private var mUpdateListeners = HashMap<ValueAnimator, ValueAnimator.AnimatorUpdateListener>()
    private var mAnimators: ArrayList<ValueAnimator>? = null
    private var alpha = 255


    companion object {
        private val ZERO_BOUNDS_RECT = Rect()
    }

    private var drawBounds: Rect = ZERO_BOUNDS_RECT

    private var mHasAnimators: Boolean = false

    private var mPaint = Paint()

    init {
        mPaint.color = Color.WHITE
        mPaint.style = Paint.Style.FILL
        mPaint.isAntiAlias = true
    }

    fun getColor() = mPaint.color

    fun setColor(color: Int) {
        mPaint.color = color
    }

    override fun draw(canvas: Canvas) {
        draw(canvas, mPaint)
    }

    abstract fun draw(canvas: Canvas, paint: Paint)

    abstract fun onCreateAnimators(): ArrayList<ValueAnimator>

    override fun setAlpha(alpha: Int) {
        this.alpha = alpha
    }

    override fun getAlpha() = alpha
    override fun getOpacity() = PixelFormat.OPAQUE


    override fun start() {
        ensureAnimators()
        if (mAnimators == null)
            return

        if (isStarted())
            return
        startAnimators()
        invalidateSelf()
    }

    private fun startAnimators() {
        for (valueAnimator in mAnimators!!) {
            var updateListener = mUpdateListeners.get(valueAnimator)
            if (updateListener != null) {
                valueAnimator.addUpdateListener(updateListener)
            }

            valueAnimator.start()
        }
    }

    private fun stopAnimators() {
        if (mAnimators != null) {
            for (animator in mAnimators!!) {
                if (animator.isStarted) {
                    animator.removeAllUpdateListeners()
                    animator.end()
                }
            }
        }
    }

    private fun ensureAnimators() {
        if (!mHasAnimators) {
            mAnimators = onCreateAnimators()
            mHasAnimators = true
        }
    }

    override fun stop() {
        stopAnimators()
    }

    private fun isStarted(): Boolean {
        if (mAnimators == null)
            return false
        for (animator in mAnimators!!) {
            return animator.isStarted
        }
        return false
    }

    override fun isRunning(): Boolean {
        if (mAnimators == null)
            return false
        for (animator in mAnimators!!) {
            return animator.isRunning
        }
        return false
    }

    fun addUpdateListener(animator: ValueAnimator, updateListener: ValueAnimator.AnimatorUpdateListener) {
        mUpdateListeners[animator] = updateListener
    }

    override fun onBoundsChange(bounds: Rect) {
        super.onBoundsChange(bounds)
        setDrawBounds(bounds)
    }

    fun setDrawBounds(drawBounds: Rect) {
        setDrawBounds(drawBounds.left, drawBounds.top, drawBounds.right, drawBounds.bottom)
    }

    fun setDrawBounds(left: Int, top: Int, right: Int, bottom: Int) {
        this.drawBounds = Rect(left, top, right, bottom)
    }

    fun postInvalidate() {
        invalidateSelf()
    }

    fun getWidth() = drawBounds.width()

    fun getHeight() = drawBounds.height()

    fun centerX() = drawBounds.centerX()

    fun centerY() = drawBounds.centerY()

    fun exactCenterX() = drawBounds.exactCenterX()

    fun exactCenterY() = drawBounds.exactCenterY()

    override fun setColorFilter(colorFilter: ColorFilter?) {
    }
}