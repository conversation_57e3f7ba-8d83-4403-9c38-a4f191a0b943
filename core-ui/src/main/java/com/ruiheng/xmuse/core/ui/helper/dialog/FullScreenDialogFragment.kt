package com.ruiheng.xmuse.core.ui.helper.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.ruiheng.xmuse.core.ui.R

abstract class FullScreenDialogFragment<T : ViewBinding> :
    DialogFragment() {
    protected lateinit var binding: T
    abstract fun bindDataBindingView(layoutInflater: LayoutInflater): T

    @SuppressLint("UseGetLayoutInflater")
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext(), R.style.CommomDialog)
        val inflater = LayoutInflater.from(context)
        binding = bindDataBindingView(inflater)
        val view = binding.root
        dialog.setContentView(view)
        return dialog
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    fun startShow(supportFragmentManager: FragmentManager) {
        show(supportFragmentManager, this.javaClass.simpleName)
    }
}