//package com.intretech.petivity.core.ui.bottommenu
//
//import android.content.Context
//import android.view.View
//import android.widget.TextView
//import androidx.databinding.ViewDataBinding
//import androidx.recyclerview.widget.LinearLayoutManager
//import androidx.recyclerview.widget.ListAdapter
//import androidx.recyclerview.widget.RecyclerView
//import com.google.android.material.bottomsheet.BottomSheetBehavior
//import com.google.android.material.bottomsheet.BottomSheetDialog
//import com.intretech.catfeeder.main.base.rv.DataBoundViewHolder
//import com.intretech.petivity.core.ui.R
//
//class BottomListDialog<T, V : ViewDataBinding>(
//    context: Context,
//    val adapter: ListAdapter<T, DataBoundViewHolder<V>>,
//    layoutId: Int = R.layout.view_bottom_list,
//    val layoutManager: RecyclerView.LayoutManager =
//        LinearLayoutManager(context, RecyclerView.VERTICAL, false),
//    isCancelShow: Boolean = true,
//    val title: String? = null,
//    initCallback: ((View) -> Unit)? = null
//) : BottomSheetDialog(context, R.style.BottomSheetDialog) {
//    val contentView: View
//    val rvMenu: RecyclerView
//
//    init {
//        contentView = View.inflate(context, layoutId, null)
//        setContentView(contentView)
//        val tvCancel = findViewById<TextView>(R.id.tv_bottom_list_cancel)
//        tvCancel?.setOnClickListener {
//            dismiss()
//        }
//
//        val tvTitle = findViewById<TextView>(R.id.tv_title)
//        tvTitle?.visibility = if (title.isNullOrEmpty()) View.GONE else View.VISIBLE
//        tvTitle?.text = title
//        tvCancel?.visibility = if (isCancelShow) View.VISIBLE else View.GONE
//        rvMenu = findViewById<RecyclerView>(R.id.rv_bottom_list)!!
//        rvMenu.layoutManager = layoutManager
//        rvMenu.adapter = adapter
//        val parent = contentView.parent as View
//
//        val behavior = BottomSheetBehavior.from(parent)
////        contentView.measure(0, 0)
//        behavior.peekHeight = ScreenUtils.getAppScreenHeight()
//        adapter.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
//            override fun onChanged() {
//                super.onChanged()
//
//                contentView.measure(0, 0)
//                behavior.peekHeight = contentView.measuredHeight
//                if (scrollPosition != null && scrollPosition != -1) {
//                    rvMenu.scrollToPosition(scrollPosition ?: 0)
//                    scrollPosition = null
//                }
//            }
//        })
//        initCallback?.invoke(contentView)
//    }
//
//    fun show(updatePeekHeight: Boolean) {
//        if (updatePeekHeight) {
////            val parent = contentView.parent as View
////            val behavior = BottomSheetBehavior.from(parent)
////            contentView.measure(0, 0)
////            behavior.peekHeight = contentView.measuredHeight
//        }
//        super.show()
//    }
//
//    fun show(position: Int?) {
//        super.show()
//        scrollPosition = position
////        val parent = contentView.parent as View
////        val behavior = BottomSheetBehavior.from(parent)
////        contentView.measure(0, 0)
////        behavior.peekHeight = contentView.measuredHeight
//        if (scrollPosition != null && scrollPosition != -1) {
//            rvMenu.postDelayed({
//                val layoutManager = rvMenu.layoutManager as LinearLayoutManager
//                val firstPos = layoutManager.findFirstVisibleItemPosition()
//                val lastPos = layoutManager.findLastVisibleItemPosition()
//                val movePosition = scrollPosition ?: 0
//
//                when {
//                    movePosition <= firstPos -> {
//                        rvMenu.scrollToPosition(movePosition)
//                    }
//                    movePosition <= lastPos -> {
//                        val childView = layoutManager.findViewByPosition(movePosition)
//                        val top = childView?.top ?: 0
//                        rvMenu.scrollBy(0, top)
//                    }
//                    else -> {
//                        layoutManager.scrollToPositionWithOffset(movePosition, 0)
//                    }
//                }
//                rvMenu.scrollToPosition(scrollPosition ?: 0)
//            }, 350)
//        }
//    }
//
//    private var scrollPosition: Int? = null
////    fun scrollToPosition(position: Int?) {
////        scrollPosition = position
////    }
//
//    override fun onAttachedToWindow() {
//        super.onAttachedToWindow()
//        window?.findViewById<View>(R.id.design_bottom_sheet)
//            ?.setBackgroundResource(android.R.color.transparent)
//    }
//}