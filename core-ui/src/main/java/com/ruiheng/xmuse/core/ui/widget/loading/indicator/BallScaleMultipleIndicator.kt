//package com.ruiheng.xmuse.core.ui.widget.loading.indicator
//
//import android.animation.ValueAnimator
//import android.graphics.Canvas
//import android.graphics.Paint
//import android.view.animation.LinearInterpolator
//
//class BallScaleMultipleIndicator : Indicator() {
//    private val scaleFloats = floatArrayOf(1f, 1f)
//    private val alphaInts = intArrayOf(0, 0)
//    private val count = 2
//    override fun draw(canvas: Canvas, paint: Paint) {
//        val circleSpacing = 4f
//        val x = getWidth() / 2f
//        val y = getHeight() / 2f
//
//        scaleFloats.forEachIndexed { index, scale ->
//            canvas.save()
//            paint.alpha = alphaInts[index]
//            canvas.scale(scale, scale, x, y)
//            canvas.drawCircle(x, y, x - circleSpacing, paint)
//            canvas.restore()
//        }
//        canvas.save()
//        paint.alpha = 255
//        canvas.scale(0.3f, 0.3f, x, y)
//        canvas.drawCircle(x, y, x - circleSpacing, paint)
//        canvas.restore()
//    }
//
//    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
//        val animators = arrayListOf<ValueAnimator>()
//        val delays = longArrayOf(0, 300, 600)
//        for (i in 0 until count) {
//            val index = i
//            val scaleAnim = ValueAnimator.ofFloat(0f, 1f)
//            scaleAnim.interpolator = LinearInterpolator()
//            scaleAnim.duration = 1200
//            scaleAnim.repeatCount = -1
//            addUpdateListener(scaleAnim) { animationX ->
//                scaleFloats[index] = animationX.animatedValue as Float
//                postInvalidate()
//            }
//            scaleAnim.startDelay = delays[i]
//
//            val alphaAnim = ValueAnimator.ofInt(200, 0)
//            alphaAnim.interpolator = LinearInterpolator()
//            alphaAnim.duration = 1200
//            alphaAnim.repeatCount = -1
//            addUpdateListener(alphaAnim) { animationY ->
//                alphaInts[index] = animationY.animatedValue as Int
//                postInvalidate()
//            }
//            alphaAnim.startDelay = delays[i]
//
//            animators.add(scaleAnim)
//            animators.add(alphaAnim)
//        }
//        return animators
//    }
//}