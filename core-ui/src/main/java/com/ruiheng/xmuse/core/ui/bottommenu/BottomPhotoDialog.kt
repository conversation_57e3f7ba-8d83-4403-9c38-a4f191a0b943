package com.ruiheng.xmuse.core.ui.bottommenu

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.databinding.DialogBottomPhotoActionBinding
import com.ruiheng.xmuse.core.ui.helper.dialog.BindBottomSheetDialog
import com.ruiheng.xmuse.core.ui.photopicker.GlideEngine
import com.ruiheng.xmuse.core.ui.photopicker.ImageFileCropEngine

class BottomPhotoDialog(
    private val title: String,
    private val onPathCallback: (String) -> Unit
) : BindBottomSheetDialog<DialogBottomPhotoActionBinding>() {
    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogBottomPhotoActionBinding.inflate(layoutInflater)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.tvTitle.text = title
        binding.btBottomTakePhotoAction.setOnClickListener {
            dismiss()
            startCamera()
        }
        binding.btBottomChooseAction.setOnClickListener {
            dismiss()
            startPhoto()
        }
    }

    private fun startPhoto() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setSelectionMode(SelectModeConfig.SINGLE)
            .isDirectReturnSingle(true)
            .isDisplayCamera(true)
            .setImageEngine(GlideEngine.instance)
            .setCropEngine(ImageFileCropEngine())
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    if (!result.isNullOrEmpty()) {
                        val path = result[0].availablePath
                        onPathCallback.invoke(path)
                    }
                }

                override fun onCancel() {

                }
            })
    }

    private fun startCamera() {
        PictureSelector.create(requireActivity())
            .openCamera(SelectMimeType.ofImage())
            .setCropEngine(ImageFileCropEngine())
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    if (!result.isNullOrEmpty()) {
                        val path = result[0].availablePath
                        onPathCallback.invoke(path)
                    }
                }

                override fun onCancel() {
                }

            })
    }

}