package com.ruiheng.xmuse.core.ui.bottommenu

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.FragmentManager
import com.ruiheng.xmuse.core.ui.databinding.DialogBottomPublicAlertBinding
import com.ruiheng.xmuse.core.ui.helper.dialog.BindBottomSheetDialog
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.autoStoppedFlow
import kotlinx.coroutines.flow.MutableStateFlow

class BottomPublicAlertDialog :
    BindBottomSheetDialog<DialogBottomPublicAlertBinding>() {
    companion object {
        const val TAG = "DialogBottomPublicAlertBinding"
    }

    private val imgStateFlow = MutableStateFlow<Any?>(null)
    private val textStateFlow = MutableStateFlow<String>("")
    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogBottomPublicAlertBinding.inflate(layoutInflater)

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        binding.btBottomAction.setOnClickListener(this)
        autoStoppedFlow(imgStateFlow) {
            binding.imgResult.showImage(it)
        }
        autoStoppedFlow(textStateFlow) {
            binding.tvResult.text = it
        }
    }

    fun showDialog(supportFragmentManager: FragmentManager, image: Any, content: String) {
        imgStateFlow.value = image
        textStateFlow.value = content
        show(supportFragmentManager, TAG)
    }

    override fun onWidgetClick(view: View) {
        when (view) {
            binding.btBottomAction -> {
                dismiss()
            }

            else -> {
                super.onWidgetClick(view)
            }
        }
    }
}