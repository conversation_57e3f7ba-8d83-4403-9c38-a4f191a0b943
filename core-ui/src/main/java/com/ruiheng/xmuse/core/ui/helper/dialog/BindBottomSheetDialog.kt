package com.ruiheng.xmuse.core.ui.helper.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.viewbinding.ViewBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.ruiheng.xmuse.core.ui.ui.IBaseView

abstract class BindBottomSheetDialog<T : ViewBinding> : BottomSheetDialogFragment(), IBaseView {
    protected lateinit var binding: T
    abstract fun bindDataBindingView(layoutInflater: LayoutInflater): T
    override var lastClick: Long? = null

    override fun bindTitle(): String? = null

    override fun bindTitleRes(): Int? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = bindDataBindingView(inflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(savedInstanceState, view)
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
    }

    override fun onWidgetClick(view: View) {
    }

    override fun initData(bundle: Bundle?) {

    }

    fun startShow(supportFragmentManager: FragmentManager) {
        show(supportFragmentManager, this.javaClass.simpleName)
    }

}