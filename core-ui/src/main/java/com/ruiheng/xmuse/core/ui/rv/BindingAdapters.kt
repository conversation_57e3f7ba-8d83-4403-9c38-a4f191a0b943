package com.ruiheng.xmuse.core.ui.rv

import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestListener

object BindingAdapters {
    @JvmStatic
    @BindingAdapter(value = ["imageUrl", "imageRequestListener"], requireAll = false)
    fun bindImage(imageView: ImageView, url: Any?, listener: RequestListener<Drawable?>?) {
        Glide.with(imageView).asDrawable().load(url).listener(listener).into(imageView)
    }
}