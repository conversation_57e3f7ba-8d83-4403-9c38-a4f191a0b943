package com.ruiheng.xmuse.core.ui

import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.signature.ObjectKey

fun AppCompatImageView.showImage(
    url: Any?, @DrawableRes placeHolder: Int? = null,
    transformation: BitmapTransformation? = null,
    isCircleCrop: Boolean = false,
    cacheStrategy: DiskCacheStrategy? = null,
    priority: Priority? = null,
    signatureVersion: String? = null
) {
    var requestOptions = RequestOptions()
    if (cacheStrategy != null) {
        requestOptions = requestOptions.diskCacheStrategy(cacheStrategy)
    }
    if (placeHolder != null) {
        requestOptions = requestOptions
            .placeholder(placeHolder)
            .error(placeHolder)
            .priority(Priority.HIGH)
    }

    if (!signatureVersion.isNullOrBlank()) {
        requestOptions = requestOptions.signature(ObjectKey(signatureVersion))
    }

    if (isCircleCrop) {
        requestOptions = requestOptions
            .circleCrop()
    }

    if (priority != null) {
        requestOptions = requestOptions.priority(priority)
    }

    if (transformation != null) {
        requestOptions = requestOptions.optionalTransform(transformation)
        Glide.with(this)
            .asDrawable()
            .load(url)
            .transition(DrawableTransitionOptions.withCrossFade(300))
            .apply(requestOptions)
            .into(this)

    } else {
        Glide.with(this)
            .asDrawable()
            .load(url)
            .apply(requestOptions)
            .into(this)
    }
}
