package com.ruiheng.xmuse.core.ui.helper.dialog

import android.view.LayoutInflater
import androidx.fragment.app.FragmentManager
import com.ruiheng.xmuse.core.ui.databinding.DialogLoadingBinding

class FullScreenLoadingDialogFragment : FullScreenDialogFragment<DialogLoadingBinding>() {

    fun showLoadingDialog(supportFragmentManager: FragmentManager) {
        super.show(supportFragmentManager, "Loading")
    }

    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogLoadingBinding.inflate(layoutInflater)
}