package com.ruiheng.xmuse.core.ui.ui.web

import com.google.gson.Gson

interface WebviewCommand {
    fun toJson(): String {
        return Gson().toJson(this)
    }

}

data class CommandUserToken(
    val command: String = "userToken",
    val token: String
) : WebviewCommand

data class StartContent(
    val command: String = "attack"
) : WebviewCommand {
}

data class CommandAppBarInfo(
    val command: String = "appbarHeight",
    val barHeight: Int
) : WebviewCommand