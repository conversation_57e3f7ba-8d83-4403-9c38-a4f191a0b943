package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF

class InVoiceSendIndicator : Indicator() {

    private var voiceCount = 2
    private val angle by lazy {
        Math.toDegrees(
            Math.atan(
                getHeight().toDouble() /
                        2.toDouble() / getWidth().toDouble()
            )
        ).toFloat()
    }

    override fun draw(canvas: Canvas, paint: Paint) {
        val oval = RectF(0f, 0f, 0f, 0f)
        val itemWidth = getWidth() / 5f
        for (i in 0..voiceCount) {
            canvas.save()
            canvas.translate(getWidth().toFloat(), getHeight() / 2f)
            if (i == 0) {
                oval.set(
                    -itemWidth, -itemWidth,
                    itemWidth, itemWidth
                )
                paint.style = Paint.Style.FILL
                canvas.drawArc(oval, 180 - angle, 2 * angle, true, paint)

            } else {
                paint.style = Paint.Style.STROKE
                paint.strokeWidth = itemWidth
                val widthSecond = itemWidth * 2 * i + itemWidth / 2
                oval.set(
                    -widthSecond, -widthSecond,
                    widthSecond, widthSecond
                )
                canvas.drawArc(oval, 180 - angle, 2 * angle, false, paint)
            }

            canvas.restore()
        }
    }

    override fun stop() {
        super.stop()
        voiceCount = 2
//        postInvalidate()
    }

    override fun start() {
        addUpdateListener(voiceAnimator, animatorUpdateListener)
        super.start()
    }

    private val voiceAnimator = ValueAnimator.ofInt(0, 1, 2, 3).apply {
        duration = 1000
        repeatCount = -1
    }

    private val animatorUpdateListener = ValueAnimator.AnimatorUpdateListener {
        voiceCount = it.animatedValue as Int
        postInvalidate()
    }

    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = ArrayList<ValueAnimator>()
        animators.add(voiceAnimator)
        return animators
    }

}