package com.ruiheng.xmuse.core.ui.custom

import android.view.LayoutInflater
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.databinding.ItemPublicOptionRowBinding

open class PublicMultiOptionRVAdapter<T : PublicOptionImp>(private val onCheckedChangeListener: (() -> Unit)? = null) :
    PublicOptionRVAdapter<T, ItemPublicOptionRowBinding>() {

    override fun bindDataBindingView(inflater: LayoutInflater, parent: ViewGroup?) =
        ItemPublicOptionRowBinding.inflate(inflater, parent, false)

    override fun bind(binding: ItemPublicOptionRowBinding, item: T, position: Int) {
        binding.option = item
        initBaseInfo(
            item,
            binding,
            binding.tvPublicOption,
            binding.tvPublicOptionContent,
            binding.imgPublicOption,
            binding.layoutContainer
        )
    }

    override fun update(binding: ItemPublicOptionRowBinding?) {
        binding?.let {
            if (it.option?.bindChecked() == true) {
                updateNormal(
                    binding.option as T,
                    binding,
                    binding.tvPublicOption,
                    binding.layoutContainer
                )
            } else {
                updateSelected(
                    binding.option as T,
                    binding,
                    binding.tvPublicOption,
                    binding.layoutContainer
                )
            }
        }
        onCheckedChangeListener?.invoke()
    }
}