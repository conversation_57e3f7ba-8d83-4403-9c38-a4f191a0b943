//package com.intretech.catfeeder.helper
//
//import android.Manifest
//import android.Manifest.permission
//import android.content.Context
//import android.content.pm.PackageManager
//import android.content.res.ColorStateList
//import android.content.res.Resources
//import android.graphics.*
//import android.graphics.drawable.Drawable
//import android.location.Criteria
//import android.location.Location
//import android.location.LocationListener
//import android.location.LocationManager
//import android.os.Bundle
//import android.text.*
//import android.text.method.LinkMovementMethod
//import android.text.style.ForegroundColorSpan
//import android.view.TouchDelegate
//import android.view.View
//import android.view.ViewGroup
//import android.widget.ImageView
//import android.widget.TextView
//import androidx.annotation.ColorInt
//import androidx.annotation.DrawableRes
//import androidx.appcompat.app.AppCompatActivity
//import androidx.core.app.ActivityCompat
//import androidx.core.graphics.drawable.DrawableCompat
//import com.blankj.utilcode.util.*
//import com.blankj.utilcode.util.Utils.getApp
//import com.bumptech.glide.Glide
//import com.bumptech.glide.Priority
//import com.bumptech.glide.load.engine.DiskCacheStrategy
//import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
//import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
//import com.bumptech.glide.request.RequestOptions
//import com.bumptech.glide.signature.ObjectKey
//import com.intretech.catfeeder.R
//import com.intretech.catfeeder.main.LandscapePage
//import com.intretech.catfeeder.main.base.BaseActivity
//import timber.log.Timber
//import java.net.URLEncoder
//import java.util.regex.Pattern
//
//fun ImageView.showImageImmediate(url: Any?, @DrawableRes placeHolder: Int? = null) {
//    showImage(url, priority = Priority.IMMEDIATE, placeHolder = placeHolder)
//}
//
//fun ImageView.showImage(
//    url: Any?, @DrawableRes placeHolder: Int? = null,
//    transformation: BitmapTransformation? = null,
//    isCircleCrop: Boolean = false,
//    cacheStrategy: DiskCacheStrategy? = null,
//    priority: Priority? = null,
//    signatureVersion: String? = null
//) {
//    var requestOptions = RequestOptions()
//    if (cacheStrategy != null) {
//        requestOptions = requestOptions.diskCacheStrategy(cacheStrategy)
//    }
//    if (placeHolder != null) {
//        requestOptions = requestOptions
//            .placeholder(placeHolder)
//            .error(placeHolder)
//            .priority(Priority.HIGH)
//    }
//
//    if (!signatureVersion.isNullOrBlank()) {
//        requestOptions = requestOptions.signature(ObjectKey(signatureVersion))
//    }
//
//    if (isCircleCrop) {
//        requestOptions = requestOptions
//            .circleCrop()
//    }
//
//    if (priority != null) {
//        requestOptions = requestOptions.priority(priority)
//    }
//
//    if (transformation != null) {
//        requestOptions = requestOptions.optionalTransform(transformation)
//        Glide.with(this)
//            .asDrawable()
//            .load(url)
//            .transition(DrawableTransitionOptions.withCrossFade(300))
//            .apply(requestOptions)
//            .into(this)
//
//    } else {
//        Glide.with(this)
//            .asDrawable()
//            .load(url)
//            .apply(requestOptions)
//            .into(this)
//    }
//}
//
//
//fun <T> ImageView.showImageWithCrossFade(url: T?) {
//    Glide.with(this)
//        .asDrawable()
//        .load(url)
//        .transition(DrawableTransitionOptions.withCrossFade(300))
//        .into(this)
//}
//
//fun <T> ImageView.showImageOrGif(url: T?) {
//    var requestOptions = RequestOptions()
//    requestOptions = requestOptions.priority(Priority.IMMEDIATE)
//    if (url is String && url.contains(".gif")) {
//        Glide.with(this)
//            .asGif()
//            .load(url)
//            .apply(requestOptions)
//            .transition(DrawableTransitionOptions.withCrossFade(300))
//            .into(this)
//    } else {
//        Glide.with(this)
//            .asDrawable()
//            .load(url)
//            .apply(requestOptions)
//            .transition(DrawableTransitionOptions.withCrossFade(300))
//            .into(this)
//    }
//}
//
//fun <T> ImageView.showImageFXY(
//    url: T?, @DrawableRes placeHolder: Int? = null,
//    transformation: BitmapTransformation? = null,
//    isFixXY: Boolean = true,
//    cacheStrategy: DiskCacheStrategy? = null,
//    priority: Priority? = null
//) {
//    var requestOptions = RequestOptions()
//    if (cacheStrategy != null) {
//        requestOptions = requestOptions.diskCacheStrategy(cacheStrategy)
//    }
//    if (placeHolder != null) {
//        requestOptions = requestOptions
//            .placeholder(placeHolder)
//            .error(placeHolder)
//            .priority(Priority.HIGH)
//    }
//
//    if (isFixXY) {
//        requestOptions = requestOptions
//            .optionalCenterInside()
//    }
//
//    if (priority != null) {
//        requestOptions = requestOptions.priority(priority)
//    }
//
//    if (transformation != null) {
//        requestOptions = requestOptions.optionalTransform(transformation)
//        Glide.with(this)
//            .asDrawable()
//            .load(url)
//            .transition(DrawableTransitionOptions.withCrossFade(300))
//            .apply(requestOptions)
//            .into(this)
//
//    } else {
//        Glide.with(this)
//            .asDrawable()
//            .load(url)
//            .apply(requestOptions)
//            .into(this)
//    }
//}
//
//fun <T> ImageView.showGifImage(url: T?) {
//    Glide.with(this)
//        .asGif()
//        .load(url)
//        .into(this)
//}
//
//fun TextView.setFakeBold() {
//    try {
//        val familyName = "sans-serif-medium"
//        val serifMedium = Typeface.create(familyName, Typeface.NORMAL)
//        typeface = serifMedium
//    } catch (e: Exception) {
//        typeface = Typeface.DEFAULT_BOLD
//    }
//
//}
//
//fun TextView.setPaintFakeBold(boldSize: Float = 0.7f) {
//    try {
//        paint.strokeWidth = boldSize
//        paint.style = Paint.Style.FILL_AND_STROKE
//    } catch (e: Exception) {
//        setFakeBold()
//    }
//}
//
//fun TextView.setMaxLength(max: Int, emojiFilterOnly: Boolean = true) {
//    filters = arrayOf(
//        InputFilter.LengthFilter(max),
//        if (emojiFilterOnly) EmojiFilter() else SpecialCharsFilter()
//    )
//}
//
//fun TextView.setMaxToastLength(max: Int, toast: String) {
//    filters = arrayOf(LengthToastFilter(max, toast))
//}
//
//fun TextView.setMaxLength(
//    max: Int,
//    emojiFilterOnly: Boolean = false,
//    specialCharsFilter: Boolean = false
//) {
//    filters = arrayOf(InputFilter.LengthFilter(max))
//}
//
//fun TextView.setTextImportant(@ColorInt color: Int = Color.RED) {
//    val spannableString = SpannableString("* $text")
//    spannableString.setSpan(ForegroundColorSpan(color), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
//    text = spannableString
//}
//
//fun TextView.getTextImportantWithColorSpannable(
//    @ColorInt color: Int = Color.RED,
//    vararg textImportants: String
//): SpannableString {
//    val spannableString = SpannableString(text)
//    textImportants.forEach { important ->
//        val index = text.indexOf(important)
//        spannableString.setSpan(
//            ForegroundColorSpan(color),
//            index,
//            index + important.length,
//            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
//        )
//    }
//    return spannableString
//}
//
//fun TextView.setTextUrlSpannable(
//    baseActivity: BaseActivity,
//    @ColorInt color: Int = Color.RED, urls: ArrayList<String>,
//    textUrls: ArrayList<String>
//) {
//    movementMethod = LinkMovementMethod.getInstance()
//    if (urls.size == textUrls.size) {
//        val spannableString = SpannableString(text)
//        textUrls.forEachIndexed { index, s ->
//            val startIndex = text.indexOf(s)
//            spannableString.setSpan(
//                TextUrlLinkClickSpan(baseActivity, urls[index], color),
//                startIndex,
//                startIndex + s.length,
//                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
//            )
//        }
//        text = spannableString
//    }
//}
//
//class EmojiFilter : InputFilter {
//
//    val emojiPattern = Pattern.compile(
//        "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]",
//        Pattern.UNICODE_CASE or Pattern.CASE_INSENSITIVE
//    )
//
//    override fun filter(
//        source: CharSequence,
//        start: Int,
//        end: Int,
//        dest: Spanned?,
//        dstart: Int,
//        dend: Int
//    ): CharSequence {
//        val matcher = emojiPattern.matcher(source)
//        return if (matcher.find()) "" else source
//    }
//}
//
//class LengthToastFilter(
//    val max: Int,
//    val toast: String
//) : InputFilter {
//
//    override fun filter(
//        source: CharSequence, start: Int, end: Int, dest: Spanned,
//        dstart: Int, dend: Int
//    ): CharSequence? {
//        var keep = max - (dest.length - (dend - dstart))
//        return when {
//            keep <= 0 -> {
//                ToastUtils.showShort(toast)
//                ""
//            }
//            keep >= end - start -> {
//
//                null // keep original
//            }
//            else -> {
//                keep += start
//                if (Character.isHighSurrogate(source[keep - 1])) {
//                    --keep
//                    if (keep == start) {
//                        return ""
//                    }
//                }
//                source.subSequence(start, keep)
//            }
//        }
//    }
//
//}
//
//class SpecialCharsFilter : InputFilter {
//
//    val emojiPattern = Pattern.compile(
//        "[^a-zA-Z0-9\u4E00-\u9FA5_()/ ]",
//        Pattern.UNICODE_CASE or Pattern.CASE_INSENSITIVE
//    )
//
//    override fun filter(
//        source: CharSequence,
//        start: Int,
//        end: Int,
//        dest: Spanned?,
//        dstart: Int,
//        dend: Int
//    ): CharSequence {
//        val matcher = emojiPattern.matcher(source)
//        return if (matcher.find()) "" else source
//    }
//}
//
//inline val Int.px: Float
//    get() = android.util.TypedValue.applyDimension(
//        android.util.TypedValue.COMPLEX_UNIT_DIP,
//        this.toFloat(),
//        Resources.getSystem().displayMetrics
//    )
//
//fun View.expandTouchArea(size: Int = (16.px).toInt()) {
//    (parent as ViewGroup?)?.post {
//        val rect = Rect()
//        getHitRect(rect)
//        rect.top -= size
//        rect.bottom += size
//        rect.left -= size
//        rect.right += size
//        (parent as ViewGroup?)?.touchDelegate = TouchDelegate(rect, this)
//    }
//}
//
//fun getProvince() {
//    PermissionUtils.permission(permission.ACCESS_FINE_LOCATION, permission.ACCESS_COARSE_LOCATION)
//        .callback(object : PermissionUtils.FullCallback {
////                override fun onGranted(permissionsGranted: MutableList<String>?) {
////                    grantedListener?.onPermissionGranted()
////                }
//
//            override fun onGranted(granted: MutableList<String>) {
//                initLocation()
//            }
//
//            override fun onDenied(
//                permissionsDeniedForever: MutableList<String>,
//                permissionsDenied: MutableList<String>
//            ) {
//                if (permissionsDeniedForever.isNotEmpty()) {
//                    val topActivity = ActivityUtils.getTopActivity()
//                    if (topActivity == null || topActivity.isFinishing || topActivity !is AppCompatActivity) return
//                    getConfirmDialog(
//                        topActivity.supportFragmentManager,
//                        cancelable = false,
//                        content = R.string.be_careful,
//                        subContent = R.string.we_need_to_get_permission,
//                        onSureClick = { PermissionUtils.launchAppDetailsSettings() },
//                        isLandscapePage = topActivity is LandscapePage
//                    )
//                    return
//                }
//            }
//        }).request()
//
//}
//
//fun initLocation() {
//    val locationListener = object : LocationListener {
//        override fun onLocationChanged(location: Location?) {
//            Timber.d("!!!!!!!${location?.latitude}${location?.longitude}")
//
//        }
//
//        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {
//            Timber.d("!!!!!!!onStatusChanged")
//        }
//
//        override fun onProviderEnabled(provider: String?) {
//            Timber.d("!!!!!!!onProviderEnabled")
//        }
//
//        override fun onProviderDisabled(provider: String?) {
//            Timber.d("!!!!!!!onProviderDisabled")
//        }
//
//    }
//    val systemService = getApp().getSystemService(Context.LOCATION_SERVICE) as LocationManager
//    val criteria = Criteria()
//    criteria.accuracy = Criteria.ACCURACY_FINE
//    criteria.isAltitudeRequired = false
//    criteria.isBearingRequired = false
//    criteria.isCostAllowed = true
//    criteria.powerRequirement = Criteria.POWER_LOW
//    val provider = systemService.getBestProvider(criteria, true)
////    val providerList = systemService.getProviders(true)
////    val provider = when {
////        providerList.contains(LocationManager.GPS_PROVIDER) -> {
////            LocationManager.GPS_PROVIDER
////        }
////        providerList.contains(LocationManager.NETWORK_PROVIDER) -> {
////            LocationManager.NETWORK_PROVIDER
////        }
////        else -> {
////            LocationManager.PASSIVE_PROVIDER
////        }
////    }
//    if (ActivityCompat.checkSelfPermission(
//            getApp(),
//            Manifest.permission.ACCESS_FINE_LOCATION
//        ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
//            getApp(),
//            Manifest.permission.ACCESS_COARSE_LOCATION
//        ) != PackageManager.PERMISSION_GRANTED
//    ) {
//        Timber.d("!!!!!!!permission")
//    }
//    Timber.d("!!!!!!!permission1")
//
//    systemService.requestLocationUpdates(provider, 1000, 0F, locationListener)
//
//}
//
//fun String.toShowHtml(): String {
//    if (this.isNotEmpty() && Pattern.matches("[\\u4e00-\\u9fa5]", this)) {
//        var resultUrl = URLEncoder.encode(this, "UTF-8")
//        resultUrl = resultUrl.replace("%3A", ":")
//        resultUrl = resultUrl.replace("%2F", "/")
//        return resultUrl
//    }
//
//    return this
//}
//
//fun TextView.setEllipsizeText(content: String) {
//    val availableWidth = width - paddingLeft - paddingRight
//    if (maxLines < 2) {
//        text = content
//    } else {
//        val lineStart = getLineStartAndEnd(content, availableWidth)
//        if (lineStart.size <= maxLines) {
//            text = content
//            return
//        }
//
//    }
//}
//
//fun TextView.getLineStartAndEnd(content: String, lineWidth: Int): List<Point> {
//    val layout =
//        StaticLayout(content, paint, lineWidth, Layout.Alignment.ALIGN_NORMAL, 1f, 0f, true)
//    val count = layout.lineCount
//    val pointList = ArrayList<Point>()
//    for (index in 0 until count) {
//        pointList.add(Point(layout.getLineStart(index), layout.getLineEnd(index)))
//    }
//    return pointList
//}
//
//fun ImageView.tintDrawable(drawable: Drawable?, colorStateList: ColorStateList) {
//    if (drawable == null) return
//    val wrappedDrawable = DrawableCompat.wrap(drawable)
//    DrawableCompat.setTintList(wrappedDrawable, colorStateList)
//    setImageDrawable(wrappedDrawable)
//}
//
//fun Long?.getFriendlyTimeSpanByNow(isSecond: Boolean = false): String {
//    if (this == null) return ""
//    val now = System.currentTimeMillis()
//    val millSecond = if (isSecond) this * 1000 else this
//    val span = now - millSecond
//    if (span < 0) {
//        return String.format("%tF", millSecond)
//    }
//    return TimeUtils.getFriendlyTimeSpanByNow(millSecond)
//}