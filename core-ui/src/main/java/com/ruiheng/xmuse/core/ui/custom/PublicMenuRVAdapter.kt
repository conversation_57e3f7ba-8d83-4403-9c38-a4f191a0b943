package com.ruiheng.xmuse.core.ui.custom

import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DiffUtil
import com.ruiheng.xmuse.core.ui.rv.DataBoundListAdapter
import com.ruiheng.xmuse.core.ui.showImage

abstract class PublicMenuRVAdapter<T : PublicMenuImp, V : ViewDataBinding> :
    DataBoundListAdapter<T, V>(diffCallback = object :
        DiffUtil.ItemCallback<T>() {
        override fun areItemsTheSame(oldItem: T, newItem: T) =
            oldItem.bindTitle() == newItem.bindTitle()

        override fun areContentsTheSame(oldItem: T, newItem: T) =
            false

    }) {

    abstract fun bindDataBindingView(
        inflater: LayoutInflater,
        parent: ViewGroup?
    ): V

    override fun createBinding(parent: ViewGroup): V {

        val binding = bindDataBindingView(LayoutInflater.from(parent.context), parent)
        return binding
    }

    protected open fun initBaseInfo(
        item: T,
        operationBinding: V,
        tvTitle: AppCompatTextView,
        tvContent: AppCompatTextView?,
        imageView: AppCompatImageView?,
        contentImageView: AppCompatImageView?,
        layoutContainer: View
    ) {
        val bgRes = item.bindBgResources()
        layoutContainer.setBackgroundResource(bgRes)
        val textColor = item.bindTextColorAttr()
        val compactColor = getThemeColor(textColor)
        if (compactColor != null) {
            tvTitle.setTextColor(compactColor)
        }
        val title = item.bindTitle()
        if (title is String) {
            tvTitle.text = title
        }
        if (title is Int) {
            tvTitle.setText(title)
        }
        tvTitle.gravity = item.bindTitleGravity()

        val contentImage = item.bindContentImage()
        if (contentImage == null) {
            contentImageView?.visibility = View.GONE
        } else {
            contentImageView?.visibility = View.VISIBLE
            contentImageView?.showImage(contentImage, isCircleCrop = true)
        }

        val image = item.bindImage()
        if (image == null) {
            imageView?.visibility = View.GONE
        } else {
            imageView?.visibility = View.VISIBLE

            val param = imageView?.layoutParams
            val bindWidth = item.bindImageWidth()
            if (bindWidth != null) {
                param?.width = bindWidth
            }
            val bindHeight = item.bindImageHeight()
            if (bindHeight != null) {
                param?.height = bindHeight
            }
            imageView?.layoutParams = param
            imageView?.showImage(image)
        }
        val content = item.bindContent()
        if (content != null) {
            if (content is String) {
                tvContent?.text = content
            }
            if (content is Int) {
                tvContent?.setText(content)
            }
        } else {
            tvContent?.visibility = View.GONE
        }
    }
}