//package com.intretech.base.ui.widget.indicator
//
//import android.view.View
//import android.content.Context
//import android.content.res.TypedArray
//import android.graphics.*
//import android.util.AttributeSet
//import com.intretech.base.R
//import java.util.*
//
//
//class VoiceLineView : View {
//    private val LINE = 0
//    private val RECT = 1
//
//    private var middleLineColor = Color.BLACK
//    private var voiceLineColor = Color.BLACK
//    private var middleLineHeight = 4f
//    private var paint: Paint? = null
//    private var paintVoicLine: Paint? = null
//    private var mode: Int = 0
//    /**
//     * 灵敏度
//     */
//    private var sensibility = 4
//
//    private var maxVolume = 100f
//
//
//    private var translateX = 0f
//    private var isSet = false
//
//    /**
//     * 振幅
//     */
//    private var amplitude = 1f
//    /**
//     * 音量
//     */
//    private var volume = 10f
//    private var fineness = 1
//    private var targetVolume = 1f
//
//
//    private var speedY: Long = 50
//    private var rectWidth = 25f
//    private var rectSpace = 5f
//    private var rectInitHeight = 4f
//    private var rectList: MutableList<Rect>? = null
//
//    private var lastTime: Long = 0
//    private var lineSpeed = 90
//
//    var paths: MutableList<Path>? = null
//
//    constructor(
//        context: Context,
//        attrs: AttributeSet? = null,
//        defStyleAttr: Int = 0,
//        defStyleRes: Int = 0
//    ) : super(context, attrs, defStyleAttr) {
//        initAtts(context, attrs)
//    }
//
//    constructor(
//        context: Context,
//        attrs: AttributeSet? = null,
//        defStyleAttr: Int = 0
//    ) : this(context, attrs, defStyleAttr, 0)
//
//    constructor(
//        context: Context,
//        attrs: AttributeSet
//    ) : this(context, attrs, 0)
//
//    private fun initAtts(context: Context, attrs: AttributeSet?) {
//        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.voiceView)
//        mode = typedArray.getInt(R.styleable.voiceView_viewMode, 0)
//        voiceLineColor = typedArray.getColor(R.styleable.voiceView_voiceLine, Color.BLACK)
//        maxVolume = typedArray.getFloat(R.styleable.voiceView_maxVolume, 100f)
//        sensibility = typedArray.getInt(R.styleable.voiceView_sensibility, 4)
//        if (mode == RECT) {
//            rectWidth = typedArray.getDimension(R.styleable.voiceView_rectWidth, 25f)
//            rectSpace = typedArray.getDimension(R.styleable.voiceView_rectSpace, 5f)
//            rectInitHeight = typedArray.getDimension(R.styleable.voiceView_rectInitHeight, 4f)
//        } else {
//            middleLineColor = typedArray.getColor(R.styleable.voiceView_middleLine, Color.BLACK)
//            middleLineHeight = typedArray.getDimension(R.styleable.voiceView_middleLineHeight, 4f)
//            lineSpeed = typedArray.getInt(R.styleable.voiceView_lineSpeed, 90)
//            fineness = typedArray.getInt(R.styleable.voiceView_fineness, 1)
//            paths = ArrayList(20)
//            for (i in 0..19) {
//                paths!!.add(Path())
//            }
//        }
//        typedArray.recycle()
//    }
//
//    override fun onDraw(canvas: Canvas) {
//        if (mode == RECT) {
//            drawVoiceRect(canvas)
//        } else {
//            drawMiddleLine(canvas)
//            drawVoiceLine(canvas)
//        }
//        run()
//    }
//
//    private fun drawMiddleLine(canvas: Canvas) {
//        if (paint == null) {
//            paint = Paint()
//            paint!!.setColor(middleLineColor)
//            paint!!.setAntiAlias(true)
//        }
//        canvas.save()
//        canvas.drawRect(0F, height / 2 - middleLineHeight / 2, width.toFloat(), height / 2 + middleLineHeight / 2, paint)
//        canvas.restore()
//    }
//
//    private fun drawVoiceLine(canvas: Canvas) {
//        lineChange()
//        if (paintVoicLine == null) {
//            paintVoicLine = Paint()
//            paintVoicLine!!.color = voiceLineColor
//            paintVoicLine!!.isAntiAlias = true
//            paintVoicLine!!.style = Paint.Style.STROKE
//            paintVoicLine!!.strokeWidth = 2F
//        }
//        canvas.save()
//        val moveY = height / 2
//        for (i in paths!!.indices) {
//            paths!![i].reset()
//            paths!![i].moveTo(width, height / 2)
//        }
//        var i = (width - 1).toFloat()
//        while (i >= 0) {
//            amplitude = 4f * volume * i / width - 4f * volume * i * i / width.toFloat() / width.toFloat()
//            for (n in 1..paths!!.size) {
//                val sin =
//                    amplitude * Math.sin((i - Math.pow(1.22, n.toDouble())) * Math.PI / 180 - translateX).toFloat()
//                paths!![n - 1].lineTo(i, 2f * n.toFloat() * sin / paths!!.size - 15 * sin / paths!!.size + moveY)
//            }
//            i -= fineness.toFloat()
//        }
//        for (n in paths!!.indices) {
//            if (n == paths!!.size - 1) {
//                paintVoicLine!!.alpha = 255
//            } else {
//                paintVoicLine!!.setAlpha(n * 130 / paths!!.size)
//            }
//            if (paintVoicLine!!.getAlpha() > 0) {
//                canvas.drawPath(paths!![n], paintVoicLine)
//            }
//        }
//        canvas.restore()
//    }
//
//    private fun drawVoiceRect(canvas: Canvas) {
//        if (paintVoicLine == null) {
//            paintVoicLine = Paint()
//            paintVoicLine!!.setColor(voiceLineColor)
//            paintVoicLine!!.setAntiAlias(true)
//            paintVoicLine!!.setStyle(Paint.Style.STROKE)
//            paintVoicLine!!.setStrokeWidth(2)
//        }
//        if (rectList == null) {
//            rectList = LinkedList()
//        }
//        val totalWidth = (rectSpace + rectWidth).toInt()
//        if (speedY % totalWidth < 6) {
//            val rect = Rect(
//                (-rectWidth - 10f - speedY.toFloat() + speedY % totalWidth).toInt(),
//                ((height / 2).toFloat() - rectInitHeight / 2 - if (volume == 10f) 0 else volume / 2).toInt(),
//                (-10 - speedY + speedY % totalWidth).toInt(),
//                ((height / 2).toFloat() + rectInitHeight / 2 + if (volume == 10f) 0 else volume / 2).toInt()
//            )
//            if (rectList!!.size > width / (rectSpace + rectWidth) + 2) {
//                rectList!!.removeAt(0)
//            }
//            rectList!!.add(rect)
//        }
//        canvas.translate(speedY, 0)
//        for (i in rectList!!.indices.reversed()) {
//            canvas.drawRect(rectList!![i], paintVoicLine)
//        }
//        rectChange()
//    }
//
//    fun setVolume(volume: Int) {
//        if (volume > maxVolume * sensibility / 25) {
//            isSet = true
//            this.targetVolume = (height * volume).toFloat() / 2f / maxVolume
//        }
//    }
//
//    private fun lineChange() {
//        if (lastTime == 0L) {
//            lastTime = System.currentTimeMillis()
//            translateX += 1.5f
//        } else {
//            if (System.currentTimeMillis() - lastTime > lineSpeed) {
//                lastTime = System.currentTimeMillis()
//                translateX += 1.5f
//            } else {
//                return
//            }
//        }
//        if (volume < targetVolume && isSet) {
//            volume += (height / 30).toFloat()
//        } else {
//            isSet = false
//            if (volume <= 10) {
//                volume = 10f
//            } else {
//                if (volume < height / 30) {
//                    volume -= (height / 60).toFloat()
//                } else {
//                    volume -= (height / 30).toFloat()
//                }
//            }
//        }
//    }
//
//    private fun rectChange() {
//        speedY += 6
//        if (volume < targetVolume && isSet) {
//            volume += (height / 30).toFloat()
//        } else {
//            isSet = false
//            if (volume <= 10) {
//                volume = 10f
//            } else {
//                if (volume < height / 30) {
//                    volume -= (height / 60).toFloat()
//                } else {
//                    volume -= (height / 30).toFloat()
//                }
//            }
//        }
//    }
//
//    fun run() {
//        if (mode == RECT) {
//            postInvalidateDelayed(30)
//        } else {
//            invalidate()
//        }
//    }
//}