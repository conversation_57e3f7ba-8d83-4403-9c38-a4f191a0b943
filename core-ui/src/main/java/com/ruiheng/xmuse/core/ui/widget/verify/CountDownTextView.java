package com.ruiheng.xmuse.core.ui.widget.verify;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;

import androidx.annotation.ColorRes;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.widget.AppCompatTextView;

import com.ruiheng.xmuse.core.ui.R;


public class CountDownTextView extends AppCompatTextView {

    private int mOriginalText = -1;

    private int mCountInfo = -1;

    private int mRepeatInfo = -1;

    private long mCountDownMillis = 60000;

    private long mLastMillis;

    private long mIntervalMillis = 1000;

    private final int MSG_WHAT_START = 10010;

    private int usableColorId = R.color.colorAccent;

    private int unusableColorId = android.R.color.darker_gray;

    private boolean uiUpdateEnable = true;

    public CountDownTextView(Context context) {
        super(context);
    }

    public CountDownTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CountDownTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == MSG_WHAT_START) {
                if (mLastMillis > 0) {
                    setUsable(false);
                    mLastMillis -= mIntervalMillis;
                    mHandler.removeMessages(MSG_WHAT_START);
                    mHandler.sendEmptyMessageDelayed(MSG_WHAT_START, mIntervalMillis);
                } else {
                    setUsable(true);
                }
            }
        }
    };


    /**
     * 设置是否可用
     *
     * @param usable
     */
    public void setUsable(boolean usable) {
        if (uiUpdateEnable) {
            if (usable) {
                if (!isClickable()) {
                    setTextColor(getResources().getColor(usableColorId));
                    if (mRepeatInfo != -1) {
                        setText(getContext().getString(mRepeatInfo));
                    }
                }
            } else {
                if (isClickable()) {
                    setTextColor(getResources().getColor(unusableColorId));
                }
                if (mCountInfo != R.string.second_count) {
                    setText(getContext().getString(mCountInfo, getContext().getString(mRepeatInfo), mLastMillis / 1000));
                } else {
                    setText(getContext().getString(mCountInfo, getContext().getString(mRepeatInfo), mLastMillis / 1000));
                }
            }
            setClickable(usable);
        }
    }

    public void setCurrentEnable(boolean enable) {
        if (enable) {
            if (!isClickable() && mLastMillis <= 0) {
                setTextColor(getResources().getColor(usableColorId));
            }
        } else {
            if (isClickable()) {
                setTextColor(getResources().getColor(unusableColorId));
            }
        }
        setClickable(enable);
    }


    /**
     * 设置倒计时颜色
     *
     * @param usableColorId   可用状态下的颜色
     * @param unusableColorId 不可用状态下的颜色
     */
    public void setCountDownColor(@ColorRes int usableColorId, @ColorRes int unusableColorId) {
        this.usableColorId = usableColorId;
        this.unusableColorId = unusableColorId;
    }

    /**
     * 设置倒计时时间
     *
     * @param millis 毫秒值
     */
    public void setCountDownMillis(long millis) {
        mCountDownMillis = millis;
    }

    public long getCountDownMillis() {
        return mCountDownMillis;
    }

    public void init(@StringRes int countString, @StringRes int mRepeatInfo) {
        this.mCountInfo = countString;
        this.mRepeatInfo = mRepeatInfo;
    }

    public void init(@StringRes int mRepeatInfo) {
        this.mCountInfo = R.string.second_count;
        this.mRepeatInfo = mRepeatInfo;
    }

    /**
     * 开始倒计时
     */
    public void start() {
        if (mRepeatInfo == -1) {
            throw new ExceptionInInitializerError("You should call init(...) to initialize this Object");
        }
        mLastMillis = mCountDownMillis;
        //延时一秒开始计时
        mHandler.removeMessages(MSG_WHAT_START);
        mHandler.sendEmptyMessageDelayed(MSG_WHAT_START, mIntervalMillis);
    }

    /**
     * 重置倒计时
     */
    public void reset() {
        mLastMillis = 0;
        mHandler.removeMessages(MSG_WHAT_START);
        mHandler.sendEmptyMessage(MSG_WHAT_START);
    }

    public void setUiUpdate(boolean enable) {
        this.uiUpdateEnable = enable;
        setUsable(mLastMillis <= 0);
    }

    @Override
    public void setOnClickListener(@Nullable final OnClickListener onClickListener) {
        super.setOnClickListener(v -> {
            if (onClickListener != null) {
                onClickListener.onClick(v);
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mHandler.removeMessages(MSG_WHAT_START);
    }
}