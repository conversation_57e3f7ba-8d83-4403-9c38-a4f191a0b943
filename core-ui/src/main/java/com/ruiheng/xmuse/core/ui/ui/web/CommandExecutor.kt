package com.ruiheng.xmuse.core.ui.ui.web

import android.util.Log
import com.google.gson.Gson


class CommandExecutor constructor() {
    companion object {
        private const val DISCONNECTED_SECS = 15

        private const val COMMAND_KEY = "command"
        private const val FORM_DATA_KEY = "formData"
        private const val MESSAGE_KEY = "message"
        private const val ENABLED_KEY = "enabled"
        private const val NAME_KEY = "name"
        private const val VISIBLE_KEY = "visible"
        private const val EVENT_NAME_KEY = "eventName"
        private const val EVENT_ATTRIBUTES_KEY = "eventAttributes"
        private const val SCREEN_NAME_KEY = "screenName"
        private const val ANNOTATION_TYPE_KEY = "annotationType"
        private const val ANNOTATION_JSON_KEY = "annotationJson"
        private const val SENT_EPOCH_TIME_MS_KEY = "sentEpochTimeMs"
        private const val SCREEN_KEY = "screen"
        private const val DAYS_SELECTED_KEY = "daysSelected"
        private const val SIGNAL_QUALITY_INDICATOR_VISIBLE_KEY = "signalQualityIndicatorVisible"

        private const val ERROR_COMMAND = "error"
        private const val EXIT_COMMAND = "exit"
        private const val COMPLETED_COMMAND = "completed"
        private const val ON_VIDEO_PLAY_COMMAND = "onVideoPlay"
        private const val SAVE_FORM_DATA_COMMAND = "saveFormData"
        private const val REQUEST_BLUETOOTH_ACCESS_COMMAND = "requestBluetoothAccess"
        private const val START_HEADBAND_DISCOVERY_COMMAND = "startHeadbandDiscovery"
        private const val REQUEST_MUSE_CONNECTION_COMMAND = "requestMuseConnection"
        private const val SET_SIGNAL_QUALITY_INDICATOR_VISIBILITY_COMMAND =
            "setSignalQualityIndicatorVisibility"
        private const val ENABLE_MUSE_DISCONNECTION_MONITOR_COMMAND =
            "enableMuseDisconnectionMonitor"
        private const val LOG_EVENT_COMMAND = "logEvent"
        private const val LOG_SCREEN_VIEW_COMMAND = "logScreenView"
        private const val ENABLE_CONSOLE_LOGGING_COMMAND = "enableConsoleLogging"
        private const val START_SESSION_COMMAND = "startSession"
        private const val END_SESSION_COMMAND = "endSession"
        private const val WRITE_SESSION_FILE_ANNOTATION_COMMAND = "writeSessionFileAnnotation"
        private const val PING_COMMAND = "ping"
        private const val SHOW_NATIVE_SCREEN = "showNativeScreen"
        private const val SET_SESSION_NFB_PROCESSING_ENABLED = "setSessionNfbProcessingEnabled"
        private const val SET_SESSION_DATA_TRACKING_ENABLED = "setSessionDataTrackingEnabled"

        private const val REQUEST_TOKEN = "requestToken"

    }

    private class ProgramModuleWebException(message: String) : RuntimeException(message)

    sealed class CommandCallback {
//        object OnExit : CommandCallback()
//        object OnCompleted : CommandCallback()
//        data class SetMuseStatusIndicatorVisible(val visible: Boolean) : CommandCallback()
//        object OnRequestBluetoothAccess : CommandCallback()
//        data class ShowHeadbandDisconnectedDialog(val museName: String) : CommandCallback()
//        object OnHeadbandDisconnected : CommandCallback()
//        data class EnableConsoleLogging(val enabled: Boolean) : CommandCallback()
//        data class ShowPracticeReminders(val daysSelected: Map<DayOfWeek, Boolean>) :
//            CommandCallback()
//
//        object ShowSqc : CommandCallback()
//        object ShowLoadingIndicator : CommandCallback()
//        object HideLoadingIndicator : CommandCallback()
//        data class ShowSessionReview(
//            val sessionReviewType: SessionReviewType,
//            val utcTimestamp: Long
//        ) : CommandCallback()
//
//        data class OnSessionStarted(val sessionMuse: UserSessionMuse?) : CommandCallback()
//        object OnSessionEnded : CommandCallback()
//        object ShowMaxDataTrackingTimeReached : CommandCallback()
    }


}