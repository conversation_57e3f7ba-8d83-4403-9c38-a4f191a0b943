//package com.ruiheng.xmuse.core.ui.bottommenu
//
//import com.ruiheng.xmuse.core.ui.R
//
//
//data class BottomMenu(
//    var title: String,
//    var imgResource: Any? = null,
//    var type: BottomMenuType = BottomMenuType.TEXT,
//    var textColor: Int = R.color.grey900
//) : BottomMenuImp {
//    companion object {
//        fun getDeviceOptionMenu() = listOf(
//            BottomMenu(title = "Unbind Device"),
//            BottomMenu(title = "Reset Network")
//        )
//    }
//
//    override fun bindTitle() = title
//    override fun bindTextColor() = textColor
//}
//
//interface BottomMenuImp {
//    fun bindTitle() = ""
//    fun bindTextColor(): Int
//}
//
//enum class BottomMenuType {
//    IMG,
//    TEXT
//}