//package com.ruiheng.xmuse.core.ui.bottommenu
//
//import android.view.LayoutInflater
//import android.view.ViewGroup
//import androidx.databinding.DataBindingUtil
//import androidx.recyclerview.widget.DiffUtil
//import com.ruiheng.xmuse.core.ui.R
//import com.ruiheng.xmuse.core.ui.rv.DataBoundListAdapter
//
//class BottomTextMenuAdapter(
//    private var bottomMenu: ((Int, BottomMenuImp) -> Unit)?
//) : DataBoundListAdapter<BottomMenuImp, ItemBottomTextMenuRowBinding>(
//    diffCallback = object : DiffUtil.ItemCallback<BottomMenuImp>() {
//        override fun areItemsTheSame(oldItem: BottomMenuImp, newItem: BottomMenuImp): Boolean {
//            return oldItem.bindTitle() == newItem.bindTitle()
//
//        }
//
//        override fun areContentsTheSame(oldItem: BottomMenuImp, newItem: BottomMenuImp): Boolean {
//            return oldItem.bindTitle() == newItem.bindTitle()
//                    && oldItem.bindTextColor() == newItem.bindTextColor()
//        }
//    }
//) {
//    override fun createBinding(parent: ViewGroup): ItemBottomTextMenuRowBinding {
//        return DataBindingUtil.inflate(
//            LayoutInflater.from(parent.context),
//            R.layout.item_bottom_text_menu_row,
//            parent,
//            false
//        )
//    }
//
//    override fun bind(binding: ItemBottomTextMenuRowBinding, item: BottomMenuImp, position: Int) {
//        binding.menu = item
//        binding.tvBottomMenuTitle.setTextColor(compatColor(item.bindTextColor()))
//        binding.root.setOnClickListener {
//            if (!isFastClick()) {
//                bottomMenu?.invoke(position, item)
//            }
//        }
//    }
//}