package com.ruiheng.xmuse.core.ui.photopicker

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.widget.ImageView
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.engine.CropEngine
import com.luck.picture.lib.entity.LocalMedia
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropImageEngine
import java.io.File


class ImageCropEngine : CropEngine {

    override fun onStartCrop(
        fragment: Fragment,
        currentLocalMedia: LocalMedia,
        dataSource: ArrayList<LocalMedia>,
        requestCode: Int
    ) {
        val currentCropPath = currentLocalMedia.availablePath
        val inputUri: Uri = if (PictureMimeType.isContent(currentCropPath) || PictureMimeType.isHasHttp(currentCropPath)) {
            Uri.parse(currentCropPath)
        } else {
            Uri.fromFile(File(currentCropPath))
        }
        val fileName = "${System.currentTimeMillis()}CROP_.jpg"
        val destinationUri = Uri.fromFile(File(getSandboxPath(fragment.requireContext()), fileName))
        val options = buildOptions()
        val dataCropSource = ArrayList<String>()
        for (media in dataSource) {
            dataCropSource.add(media.availablePath)
        }
        val uCrop = UCrop.of(inputUri, destinationUri, dataCropSource)
        //options.setMultipleCropAspectRatio(buildAspectRatios(dataSource.size()))
        uCrop.withOptions(options)
        uCrop.setImageEngine(object : UCropImageEngine {
            override fun loadImage(context: Context, url: String, imageView: ImageView) {
                if (!ImageLoaderUtils.assertValidRequest(context)) {
                    return
                }
                Glide.with(context).load(url).override(180, 180).into(imageView)
            }

            override fun loadImage(context: Context, url: Uri, maxWidth: Int, maxHeight: Int, call: UCropImageEngine.OnCallbackListener<Bitmap>?) {
            }
        })
        uCrop.start(fragment.requireActivity(), fragment, requestCode)
    }

    private fun buildOptions(): UCrop.Options {
        val options = UCrop.Options()
        return options
    }


    private fun getSandboxPath(context: Context): String {
        val externalFilesDir: File? = context.getExternalFilesDir("")
        val customFile = File(externalFilesDir?.absolutePath, "Sandbox")
        if (!customFile.exists()) {
            customFile.mkdirs()
        }
        return customFile.absolutePath + File.separator
    }
}