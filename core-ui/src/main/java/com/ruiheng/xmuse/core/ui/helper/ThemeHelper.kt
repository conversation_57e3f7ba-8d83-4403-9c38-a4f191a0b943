package com.ruiheng.xmuse.core.ui.helper

import android.content.Context
import android.content.res.Configuration
import android.util.TypedValue
import androidx.annotation.AttrRes
import androidx.annotation.ColorInt

@ColorInt
fun Context.getAttrColor(@AttrRes colorAttrRes: Int): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(colorAttrRes, typedValue, true)
    return typedValue.data
}

@ColorInt
fun Context.getColorOnSurfaceVariant(): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(com.google.android.material.R.attr.colorOnSurfaceVariant, typedValue, true)
    return typedValue.data
}

@ColorInt
fun Context.getColorOnSurfaceInverse(): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(com.google.android.material.R.attr.colorOnSurfaceInverse, typedValue, true)
    return typedValue.data
}

@ColorInt
fun Context.getColorSurface(): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(com.google.android.material.R.attr.colorSurface, typedValue, true)
    return typedValue.data
}

@ColorInt
fun Context.getColorOnSurface(): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(com.google.android.material.R.attr.colorOnSurface, typedValue, true)
    return typedValue.data
}

@ColorInt
fun Context.getColorPrimary(): Int {
    val typedValue = TypedValue()
    val theme = theme
    theme.resolveAttribute(com.google.android.material.R.attr.colorPrimary, typedValue, true)
    return typedValue.data
}

fun Context.isNightMode(): Boolean {
    val currentNightMode = resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
    return currentNightMode == Configuration.UI_MODE_NIGHT_YES
}