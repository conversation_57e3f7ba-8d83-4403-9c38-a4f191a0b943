package com.ruiheng.xmuse.core.ui.helper

import android.view.View
import androidx.annotation.AttrRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.IBaseView

interface IBasePageErrorView {
    fun initPageErrorView(
        errorContainer: View,
        baseView: IBaseView,
        @AttrRes textContentColor: Int? = null,
        @AttrRes textSubContentColor: Int? = null,
        textContentSize: Float? = null,
        textSubContentSize: Float? = null,
        imageSize: Int? = null
    ) {
        errorContainer.visibility = View.GONE
        /**
         * TODO reload
         */
        if (textContentColor != null) {
            //TODO
        }

        if (textSubContentColor != null) {
            //TODO
        }

        if (textContentSize != null) {
            val tvContent =
                errorContainer.findViewById<AppCompatTextView>(R.id.tv_error_container_content)
            tvContent?.textSize = textContentSize
        }

        if (textSubContentSize != null) {
            val tvSubContent =
                errorContainer.findViewById<AppCompatTextView>(R.id.tv_error_container_sub_content)
            tvSubContent?.textSize = textSubContentSize
        }

        if (imageSize != null) {
            val image = errorContainer.findViewById<AppCompatImageView>(R.id.img_error_container)
            val param = image.layoutParams as ConstraintLayout.LayoutParams
            param.width = imageSize
            param.height = imageSize
            image.layoutParams = param
        }
        errorContainer.setOnClickListener {}
    }

    fun showErrorContainer(
        errorContainer: View?,
        image: Any,
        content: Int? = null,
        subContent: Int? = null,
        contentString: String? = null
    ) {
        if (errorContainer == null) return
        errorContainer.visibility = View.VISIBLE
        val imageView = errorContainer.findViewById<AppCompatImageView>(R.id.img_error_container)
        val tvContent =
            errorContainer.findViewById<AppCompatTextView>(R.id.tv_error_container_content)
        val tvSubContent =
            errorContainer.findViewById<AppCompatTextView>(R.id.tv_error_container_sub_content)
        imageView.showImage(image)
        if (content != null) {
            tvContent.setText(content)
        }

        if (!contentString.isNullOrEmpty()) {
            tvContent.text = contentString
        }

        tvSubContent.visibility = if (subContent == null) View.GONE else View.VISIBLE
        if (subContent != null) {
            tvSubContent.setText(subContent)
        }
    }

    fun <T> handleListDataResult(
        errorContainer: View?,
        data: T?,
        image: Any,
        content: Int? = null,
        subContent: Int? = null,
        contentString: String? = null
    ): Boolean {
        val isEmpty = when (data) {
            is List<*> -> {
                data.isEmpty()
            }

            else -> {
                data == null
            }
        }
        if (isEmpty) {
            showErrorContainer(
                errorContainer,
                image,
                content,
                subContent,
                contentString = contentString
            )
            return true
        }
        errorContainer?.visibility = View.GONE
        return false
    }
}