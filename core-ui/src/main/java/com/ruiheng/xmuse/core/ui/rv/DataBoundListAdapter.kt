package com.ruiheng.xmuse.core.ui.rv

import android.content.Context
import android.util.TypedValue
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.AsyncDifferConfig
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import com.ruiheng.xmuse.core.ui.ui.IBaseView
import java.util.concurrent.Executors

abstract class DataBoundListAdapter<T, V : ViewDataBinding>(
    diffCallback: DiffUtil.ItemCallback<T>
) : ListAdapter<T, DataBoundViewHolder<V>>(
    AsyncDifferConfig.Builder<T>(diffCallback)
        .setBackgroundThreadExecutor(Executors.newSingleThreadExecutor())
        .build()
) {
    protected val theme by lazy {
        context?.theme
    }

    protected fun getThemeColor(colorAttr: Int?): Int? {
        if (colorAttr != null && theme != null) {
            val typedValue = TypedValue()
            theme!!.resolveAttribute(colorAttr, typedValue, true)
            return typedValue.data
        }
        return null
    }

    protected var context: Context? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DataBoundViewHolder<V> {
        context = parent.context
        val binding = createBinding(parent)
        return DataBoundViewHolder(binding)
    }

    protected abstract fun createBinding(parent: ViewGroup): V

    override fun onBindViewHolder(holder: DataBoundViewHolder<V>, position: Int) {
        bind(holder.binding, getItem(position), position)
        holder.binding.executePendingBindings()
    }

    protected abstract fun bind(binding: V, item: T, position: Int)

    protected fun compatColor(res: Int): Int {
        return ContextCompat.getColor(context!!, res)
    }

    private var lastClick: Long? = null
    fun isFastClick(time: Int = 600): Boolean {
        if (context is IBaseView) {
            return (context as IBaseView).isFastClick()
        }
        val now = System.currentTimeMillis()
        if (lastClick == null || now - lastClick!! >= time) {
            lastClick = now
            return false
        }
        return true
    }
}