package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF

class InWaveIndicator : Indicator() {

    private var offset: Float = 1F

    private var colorArray = arrayListOf("#00EDED", "#00EDED", "#00EDED", "#00EDED")

    override fun draw(canvas: Canvas, paint: Paint) {

        colorArray.forEachIndexed { index, color ->
            canvas.save()
            val heigtX = (canvas.height - index * 40)* offset 
            val path = Path()
            val startPoint = PointF(0F, (canvas.height / 2).toFloat())
            val secondPoint = PointF((canvas.width / 3).toFloat() - index * 60, canvas.height - heigtX)
            val thirdPoint = PointF((2 * canvas.width / 3).toFloat() - index * 60, heigtX)
            val endPoint = PointF(canvas.width.toFloat(), (canvas.height / 2).toFloat())
            path.moveTo(startPoint.x, startPoint.y)
            if (index == 1 || index == 3) {
                path.cubicTo(
                    secondPoint.x + index * 120, thirdPoint.y ,
                    thirdPoint.x + index * 80, secondPoint.y ,
                    endPoint.x, endPoint.y
                )
            } else {
                path.cubicTo(
                    secondPoint.x, secondPoint.y,
                    thirdPoint.x, thirdPoint.y,
                    endPoint.x, endPoint.y
                )
            }

            paint.color = Color.parseColor(color)
            paint.strokeWidth = 8f - 2 * index
            paint.style = Paint.Style.STROKE
            canvas.drawPath(path, paint)
            canvas.restore()
        }

//        canvas.drawLine(startPoint.x, startPoint.y, secondPoint.x, secondPoint.y, paint)
//        canvas.drawLine(secondPoint.x, secondPoint.y, thirdPoint.x, thirdPoint.y, paint)
//        canvas.drawLine(thirdPoint.x, thirdPoint.y, endPoint.x, endPoint.y, paint)
    }

    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = ArrayList<ValueAnimator>()
        val waveAnimator = ValueAnimator.ofFloat(1.5F, -0.5F)
        waveAnimator.duration = 2000
        waveAnimator.repeatCount = ValueAnimator.INFINITE
        waveAnimator.repeatMode = ValueAnimator.REVERSE
        waveAnimator.startDelay = 120
        addUpdateListener(waveAnimator, ValueAnimator.AnimatorUpdateListener {
            offset = it.animatedValue as Float
            postInvalidate()
        })
        animators.add(waveAnimator)
        return animators
    }

}