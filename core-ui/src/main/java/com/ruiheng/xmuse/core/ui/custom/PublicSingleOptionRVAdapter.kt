package com.ruiheng.xmuse.core.ui.custom

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import com.ruiheng.xmuse.core.ui.databinding.ItemPublicOptionRowBinding

open class PublicSingleOptionRVAdapter<T : PublicOptionImp>(private val onCheckedChangeListener: (() -> Unit)? = null) :
    PublicOptionRVAdapter<T, ItemPublicOptionRowBinding>() {


    private var selectedBinding: ItemPublicOptionRowBinding? = null
    override fun bindDataBindingView(inflater: LayoutInflater, parent: ViewGroup?) =
        ItemPublicOptionRowBinding.inflate(inflater, parent, false)

    override fun bind(binding: ItemPublicOptionRowBinding, item: T, position: Int) {
        binding.option = item
        initBaseInfo(
            item,
            binding,
            binding.tvPublicOption,
            binding.tvPublicOptionContent,
            binding.imgPublicOption,
            binding.layoutContainer
        )
    }

    override fun updateSelected(
        item: T?,
        operationBinding: ItemPublicOptionRowBinding,
        tvTitle: AppCompatTextView?,
        layoutContainer: View
    ) {
        super.updateSelected(item, operationBinding, tvTitle, layoutContainer)
        selectedBinding = operationBinding
    }

    override fun update(binding: ItemPublicOptionRowBinding?) {
        if (binding == null && selectedBinding == null) return
        val option = binding?.option
        val selectedOption = selectedBinding?.option

        if (selectedBinding == null) {
            updateSelected(
                option!! as T,
                binding,
                binding.tvPublicOption,
                binding.layoutContainer
            )
            selectedBinding = binding
        } else if (binding == null) {
            updateNormal(
                selectedOption!! as T,
                selectedBinding!!,
                selectedBinding!!.tvPublicOption,
                selectedBinding!!.layoutContainer
            )
            selectedBinding = null
        } else {
            updateNormal(
                selectedOption!! as T,
                selectedBinding!!,
                selectedBinding!!.tvPublicOption,
                selectedBinding!!.layoutContainer
            )
            updateSelected(
                option!! as T,
                binding,
                binding.tvPublicOption,
                binding.layoutContainer
            )
            selectedBinding = binding
        }
        onCheckedChangeListener?.invoke()
    }

    override fun submitList(list: List<T>?) {
        super.submitList(list)
        onCheckedChangeListener?.invoke()
    }
}