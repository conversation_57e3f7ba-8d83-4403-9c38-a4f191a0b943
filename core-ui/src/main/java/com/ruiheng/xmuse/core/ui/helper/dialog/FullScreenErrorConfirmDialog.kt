package com.ruiheng.xmuse.core.ui.helper.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import androidx.fragment.app.FragmentManager
import com.ruiheng.xmuse.core.ui.databinding.DialogErrorConfirmBinding

class FullScreenErrorConfirmDialog(
    private val title: String,
    private val content: String,
    private val sureText: String,
    private val cancelText: String,
    private val onSureClick: () -> Unit
) : FullScreenDialogFragment<DialogErrorConfirmBinding>() {

    fun showLoadingDialog(supportFragmentManager: FragmentManager) {
        super.show(supportFragmentManager, "FullScreenErrorConfirmDialog")
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        binding.tvDialogTitle.text = title
        binding.tvDialogContent.text = content
        binding.btnDialogSure.text = sureText
        binding.btnDialogCancel.text = cancelText
        binding.btnDialogCancel.setOnClickListener {
            dismiss()
        }
        binding.btnDialogSure.setOnClickListener {
            dismiss()
            onSureClick.invoke()
        }
        return dialog
    }

    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogErrorConfirmBinding.inflate(layoutInflater)

}