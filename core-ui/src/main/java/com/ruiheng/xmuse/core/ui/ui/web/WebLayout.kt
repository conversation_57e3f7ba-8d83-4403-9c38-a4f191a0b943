package com.ruiheng.xmuse.core.ui.ui.web

import android.view.LayoutInflater
import android.view.ViewGroup
import android.webkit.WebView
import androidx.constraintlayout.widget.ConstraintLayout
import com.just.agentweb.IWebLayout
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.scwang.smart.refresh.layout.SmartRefreshLayout

class WebLayout(val activity: BaseActivity) : IWebLayout<WebView, ViewGroup> {

    private val mRefreshLayout: ConstraintLayout =
        LayoutInflater.from(activity).inflate(R.layout.item_refresh_web_view, null) as ConstraintLayout

    private val webView = mRefreshLayout.findViewById<WebView>(R.id.web_view)

    override fun getLayout() = mRefreshLayout

    override fun getWebView() = webView

}