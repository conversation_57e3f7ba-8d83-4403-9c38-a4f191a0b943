package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import android.view.animation.LinearInterpolator

class MuseSessionAlarmIndicator : Indicator() {
    private val scaleFloats = floatArrayOf(1f, 1f, 1f)
    private val alphaInts = intArrayOf(0, 0, 0)
    private val count = 3
    override fun draw(canvas: Canvas, paint: Paint) {
        val circleSpacing = 4f
        val x = getWidth() / 2f
        val y = getHeight() / 2f

        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 4f
        scaleFloats.forEachIndexed { index, scale ->
            canvas.save()
            paint.alpha = alphaInts[index]
            canvas.scale(scale, scale, x, y)
            canvas.drawCircle(x, y, x - circleSpacing, paint)
            canvas.restore()
        }
        canvas.save()
        paint.style = Paint.Style.FILL
        paint.alpha = 40
        canvas.scale(0.5f, 0.5f, x, y)
        canvas.drawCircle(x, y, x - circleSpacing, paint)
        canvas.restore()
    }

    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = arrayListOf<ValueAnimator>()
        val delays = longArrayOf(0, 600, 1200)
        for (i in 0 until count) {
            val index = i
            val scaleAnim = ValueAnimator.ofFloat(0.5f, 1f)
            scaleAnim.interpolator = LinearInterpolator()
            scaleAnim.duration = 2000
            scaleAnim.repeatCount = -1
            addUpdateListener(scaleAnim) { animationX ->
                scaleFloats[index] = animationX.animatedValue as Float
                postInvalidate()
            }
            scaleAnim.startDelay = delays[i]

            val alphaAnim = ValueAnimator.ofInt(155, 0)
            alphaAnim.interpolator = LinearInterpolator()
            alphaAnim.duration = 2000
            alphaAnim.repeatCount = -1
            addUpdateListener(alphaAnim) { animationY ->
                alphaInts[index] = animationY.animatedValue as Int
                postInvalidate()
            }
            alphaAnim.startDelay = delays[i]

            animators.add(scaleAnim)
            animators.add(alphaAnim)
        }
        return animators
    }
}