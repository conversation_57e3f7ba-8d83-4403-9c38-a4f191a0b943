package com.ruiheng.xmuse.core.ui.ui

import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.ruiheng.xmuse.core.ui.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

abstract class BaseBindingFragment<T : ViewBinding> : BaseFragment(), BaseNavHandler {

    private var _binding: T? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    protected val binding get() = _binding!!
    private var reload: Boolean = false
    private var isNavigationViewInit = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        (requireActivity() as AppCompatActivity).enableEdgeToEdge()
        if (_binding == null) {
            _binding = bindDataBindingView(inflater, container)
        }
        val parent = _binding?.root?.parent as ViewGroup?
        if (parent != null) {
            reload = true
            parent.removeAllViewsInLayout()
            _binding = bindDataBindingView(inflater, container)
        }

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {

        if (!isNavigationViewInit) {
            super.onViewCreated(view, savedInstanceState)
            isNavigationViewInit = true
            return
        }
        if (reload) {
            super.onViewCreated(view, savedInstanceState)
            reload = false
        }
    }

    override fun initData(bundle: Bundle?) {

    }

    private val theme by lazy {
        requireContext().theme
    }

    fun getThemeColor(colorAttr: Int?): Int? {
        if (colorAttr != null && theme != null) {
            val typedValue = TypedValue()
            theme!!.resolveAttribute(colorAttr, typedValue, true)
            return typedValue.data
        }
        return null
    }

    abstract fun bindDataBindingView(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): T

//    override fun onDestroyView() {
//        super.onDestroyView()
//        _binding = null
//    }
}

abstract class BaseNavFragment<T : ViewBinding> : BaseBindingFragment<T>() {


    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        initOnBackPressedDispatcher(this)
    }

    fun startNavPage(
        action: Int,
        finish: Boolean = false,
        delay: Boolean = false,
        delayDuration: Long = 500L
    ) {
        val navigationController = findNavController()
        val navOptionBuilder = NavOptions.Builder().setEnterAnim(R.anim.fragment_open_enter)
            .setPopEnterAnim(R.anim.fragment_open_enter)
            .setExitAnim(R.anim.fragment_close_exit)
            .setPopExitAnim(R.anim.fragment_close_exit)
        if (finish) {
            navOptionBuilder.setPopUpTo(navigationController.currentDestination!!.id, true)
        }
        if (delay) {
            lifecycleScope.launch {
                delay(delayDuration)
                navigationController.navigate(action, null, navOptionBuilder.build())
            }
        } else {
            navigationController.navigate(action, null, navOptionBuilder.build())
        }
    }

    fun finishNavPage() = finishNavPage(this)
}

interface BaseNavHandler {

    fun initOnBackPressedDispatcher(
        baseFragment: BaseFragment,
        pageNavId: Int? = baseFragment.findNavController().currentDestination?.id,
        onBackCallback: ((Boolean) -> Boolean)? = bindOnBackCallback()
    ) {
        if (pageNavId == null) {
            baseFragment.requireActivity().finish()
        }
        val callback =
            baseFragment.requireActivity().onBackPressedDispatcher.addCallback(baseFragment) {
                val isStartDestination = isStartDestination(baseFragment)
                val shouldHandler =
                    onBackCallback == null || !onBackCallback.invoke(isStartDestination)
                if (shouldHandler) {
                    finishNavPage(baseFragment)
                }
            }
        callback.isEnabled = true
    }

    fun finishNavPage(fragment: BaseFragment) {
        val navigationController = fragment.findNavController()
        val isStartDestination = isStartDestination(fragment)
        if (isStartDestination) {
            fragment.requireActivity().finish()
        } else {
            if (navigationController.previousBackStackEntry != null) {
                navigationController.popBackStack()
            } else {
                fragment.requireActivity().finish()
            }
        }
    }

    fun isStartDestination(
        baseFragment: BaseFragment,
        pageNavId: Int? = baseFragment.findNavController().currentDestination?.id
    ) = baseFragment.findNavController().currentDestination?.parent?.startDestinationId == pageNavId

    //Boolean ->是否为根Fragment Boolean2 -> 重写方法是否已经处理
    fun bindOnBackCallback(): ((Boolean) -> Boolean)? = null
}