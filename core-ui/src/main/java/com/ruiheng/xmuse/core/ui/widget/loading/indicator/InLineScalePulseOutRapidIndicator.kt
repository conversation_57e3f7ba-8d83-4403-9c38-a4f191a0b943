package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator

class InLineScalePulseOutRapidIndicator: InLineScaleIndicator() {
    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = ArrayList<ValueAnimator>()
        val delays = longArrayOf(400,200,0,200,400)
        for (i in 0 until lineCount){
            val index = i
            val scaleAnim = ValueAnimator.ofFloat(1f ,0.4f ,1f)
            scaleAnim.duration = 1000
            scaleAnim.repeatCount = -1
            scaleAnim.startDelay = delays[i]
            addUpdateListener(scaleAnim) {
                scaleYFloatArray[index] = it.animatedValue as Float
                postInvalidate()
            }
            animators.add(scaleAnim)
        }
        return animators
    }
}