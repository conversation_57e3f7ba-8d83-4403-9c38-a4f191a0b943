package com.ruiheng.xmuse.core.ui.helper

import android.text.InputFilter
import android.text.Spanned
import android.widget.TextView
import java.util.regex.Pattern

fun TextView.setCustomInputFilter(
    maxLength: Int?,
    emojiFilter: Boolean = true,
    specialCharFilter: Boolean = true
) {
    val filterList = mutableListOf<InputFilter>()
    if (maxLength != null) {
        filterList.add(InputFilter.LengthFilter(maxLength))
    }
    if (emojiFilter) {
        filterList.add(EmojiFilter())
    }
    if (specialCharFilter) {
        filterList.add(SpecialCharsFilter())
    }
    filters = filterList.toTypedArray()
}

class EmojiFilter : InputFilter {

    val emojiPattern = Pattern.compile(
        "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]",
        Pattern.UNICODE_CASE or Pattern.CASE_INSENSITIVE
    )

    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence {
        val matcher = emojiPattern.matcher(source)
        return if (matcher.find()) "" else source
    }
}

class SpecialCharsFilter : InputFilter {

    val emojiPattern = Pattern.compile(
        "[^a-zA-Z0-9\u4E00-\u9FA5_()/ ]",
        Pattern.UNICODE_CASE or Pattern.CASE_INSENSITIVE
    )

    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence {
        val matcher = emojiPattern.matcher(source)
        return if (matcher.find()) "" else source
    }
}