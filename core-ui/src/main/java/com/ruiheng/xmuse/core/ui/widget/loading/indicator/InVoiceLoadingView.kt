package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.Animatable
import android.graphics.drawable.Drawable
import android.os.Build
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.view.animation.AnimationUtils
import com.ruiheng.xmuse.core.ui.R

class InVoiceLoadingView : View {

    companion object {
        private val DEFAULT_INDICATOR = InLineScalePulseOutRapidIndicator()
        private const val MIN_SHOW_TIME = 500L
        private const val MIN_DELAY = 500L
    }

    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
        defStyleRes: Int = 0
    ) : super(context, attrs, defStyleAttr) {
        init(context, attrs, defStyleAttr, defStyleRes)
    }

    constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : this(context, attrs, defStyleAttr, 0)

    constructor(
        context: Context,
        attrs: AttributeSet
    ) : this(context, attrs, 0)

    private var mStartTime = -1L
    private var mPostedHide = false
    private var mPostedShow = false
    private var mDismissed = false

    private var mDelayedHide = Runnable {
        mPostedHide = false
        mStartTime = -1
        visibility = GONE
    }

    private var mDelayedShow = Runnable {
        mPostedShow = false
        if (!mDismissed) {
            mStartTime = System.currentTimeMillis()
            visibility = VISIBLE
        }
    }

    private var mMinWidth: Int = 24
    private var mMaxWidth: Int = 48
    private var mMinHeight: Int = 24
    private var mMaxHeight: Int = 48

    private var mIndicator: Indicator? = null
    private var mIndicatorColor: Int? = null

    private var mShouldStartAnimationDrawable = false

    @SuppressLint("CustomViewStyleable")
    private fun init(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0,
        defStyleRes: Int = 0
    ) {
        val typedArray = context.obtainStyledAttributes(
            attrs,
            R.styleable.In_LoadingIndicatorView,
            defStyleAttr,
            if (defStyleRes == 0) R.style.in_LoadingIndicatorView else defStyleRes
        ) ?: return

        with(typedArray) {
            mMinWidth = getDimensionPixelSize(R.styleable.In_LoadingIndicatorView_in_minWidth, mMinWidth)
            mMaxWidth = getDimensionPixelSize(R.styleable.In_LoadingIndicatorView_in_maxWidth, mMaxWidth)
            mMinHeight = getDimensionPixelSize(R.styleable.In_LoadingIndicatorView_in_minHeight, mMinHeight)
            mMaxHeight = getDimensionPixelSize(R.styleable.In_LoadingIndicatorView_in_maxHeight, mMaxHeight)
            val indicatorName = getString(R.styleable.In_LoadingIndicatorView_in_indicatorName)
            mIndicatorColor = getColor(R.styleable.In_LoadingIndicatorView_in_indicatorColor, Color.WHITE)
            setIndicator(indicatorName)
            if (mIndicator == null) {
                setIndicator(DEFAULT_INDICATOR)
            }
            recycle()
        }
    }

    private fun setIndicator(d: Indicator) {
        if (mIndicator != d) {
            if (mIndicator != null) {
                mIndicator!!.callback = null
                unscheduleDrawable(mIndicator)
            }

            mIndicator = d

            setIndicatorColor(mIndicatorColor!!)
            d.callback = this
            postInvalidate()
        }
    }

    private fun setIndicatorColor(color: Int) {
        this.mIndicatorColor = color
        mIndicator!!.setColor(color)
    }

    private fun setIndicator(indicatorName: String?) {
        try {
            if (TextUtils.isEmpty(indicatorName))
                return
            val drawableClassName = StringBuilder()
            if (!indicatorName!!.contains(".")) {
                val defaultPackageName = javaClass.`package`!!.name
                drawableClassName.append(defaultPackageName)
                    .append(".")
            }
            drawableClassName.append(indicatorName)

            val drawableClass = Class.forName(drawableClassName.toString())
            val indicator: Indicator = drawableClass.newInstance() as Indicator
            setIndicator(indicator)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun smoothToShow() {
        startAnimation(AnimationUtils.loadAnimation(context, android.R.anim.fade_in))
        visibility = VISIBLE
    }

    fun smoothToHide() {
        startAnimation(AnimationUtils.loadAnimation(context, android.R.anim.fade_out))
        visibility = GONE
    }

    fun hide() {
        mDismissed = true
        removeCallbacks(mDelayedShow)
        var diff = System.currentTimeMillis() - mStartTime
        if (diff >= MIN_SHOW_TIME || mStartTime == -1L) {
            visibility = GONE
        } else {
            if (!mPostedHide) {
                postDelayed(mDelayedHide, MIN_SHOW_TIME - diff)
                mPostedHide = true
            }
        }
    }

    fun show() {
        mStartTime = -1L
        mDismissed = false
        removeCallbacks(mDelayedHide)
        if (!mPostedShow) {
            postDelayed(mDelayedShow, MIN_DELAY)
            mPostedShow = true
        }
    }

    override fun verifyDrawable(who: Drawable): Boolean = who == mIndicator || super.verifyDrawable(who)

    fun startAnimation() {
        if (visibility != VISIBLE)
            return
        if (mIndicator is Animatable) {
            mShouldStartAnimationDrawable = true
        }
        postInvalidate()
    }

    fun stopAnimation() {
        if (mIndicator is Animatable) {
            mIndicator?.stop()
            mShouldStartAnimationDrawable = false
        }
        postInvalidate()
    }

    override fun setVisibility(visibility: Int) {
        if (this.visibility != visibility) {
            super.setVisibility(visibility)
            if (visibility == GONE || visibility == INVISIBLE) {
                stopAnimation()
            } else {
//                startAnimation()
            }
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == GONE || visibility == INVISIBLE) {
            stopAnimation()
        } else {
//            startAnimation()
        }
    }

    override fun invalidateDrawable(drawable: Drawable) {
        if (verifyDrawable(drawable)) {
//            val dirty = drawable.bounds
//            val scrollX = scrollX + paddingLeft
//            val scrollY = scrollY + paddingTop
            invalidate()
//            invalidate(dirty.left + scrollX ,dirty.top + scrollY ,dirty.right + scrollX ,dirty.bottom + scrollY)
        } else {
            super.invalidateDrawable(drawable)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        updateDrawableBounds(w, h)
    }

    private fun updateDrawableBounds(w: Int, h: Int) {
        var mW = w
        var mH = h
        // onDraw will translate the canvas so we draw starting at 0,0.
        // Subtract out padding for the purposes of the calculations below.
        mW -= paddingRight + paddingLeft
        mH -= paddingTop + paddingBottom

        val right = mW + paddingLeft
        val bottom = mH + paddingTop
        val top = paddingTop
        val left = paddingLeft

        if (mIndicator != null) {
            mIndicator?.setBounds(left, top, right, bottom)
        }
    }

    @Synchronized
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawTrack(canvas)
    }

    private fun drawTrack(canvas: Canvas) {
        val d = mIndicator as Drawable
        val saveCount = canvas.save()
        canvas.translate(paddingLeft.toFloat(), paddingTop.toFloat())

        d.draw(canvas)
        canvas.restoreToCount(saveCount)

        if (mShouldStartAnimationDrawable && d is Animatable) {
            d.start()
            mShouldStartAnimationDrawable = false
        }
    }

    @Synchronized
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var dw: Int
        var dh: Int

        val d = mIndicator as Drawable
        dw = Math.max(mMinWidth, Math.min(mMinWidth, d.intrinsicWidth))
        dh = Math.max(mMinHeight, Math.min(mMaxHeight, d.intrinsicHeight))

        updateDrawableState()

        dw += paddingLeft + paddingRight
        dh += paddingTop + paddingBottom

        val measuredWidth = View.resolveSizeAndState(dw, widthMeasureSpec, 0)
        val measuredHeight = View.resolveSizeAndState(dh, heightMeasureSpec, 0)
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun drawableStateChanged() {
        super.drawableStateChanged()
        updateDrawableState()
    }

    private fun updateDrawableState() {
        val state = drawableState
        if (mIndicator != null && mIndicator!!.isStateful) {
            mIndicator!!.state = state
        }
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    override fun drawableHotspotChanged(x: Float, y: Float) {
        super.drawableHotspotChanged(x, y)
        if (mIndicator != null) {
            mIndicator!!.setHotspot(x, y)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
//        startAnimation()
        removeCallbacks()
    }

    override fun onDetachedFromWindow() {
        stopAnimation()
        super.onDetachedFromWindow()
        removeCallbacks()
    }

    private fun removeCallbacks() {
        removeCallbacks(mDelayedHide)
        removeCallbacks(mDelayedShow)
    }
}