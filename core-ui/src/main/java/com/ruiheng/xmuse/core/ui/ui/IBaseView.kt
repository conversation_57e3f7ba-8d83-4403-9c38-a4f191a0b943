package com.ruiheng.xmuse.core.ui.ui

import android.os.Bundle
import android.os.SystemClock
import android.view.View

/**
 *     author: YQ09506
 *     email : <EMAIL>
 *     time  : 2024/10/11
 *     desc  : 
 */

interface LandscapePage

interface FullScreenPage

interface IBaseView : View.OnClickListener {

    fun initData(bundle: Bundle?)

    fun initView(saveInsBundle: Bundle?, contentView: View)
    
    fun onWidgetClick(view: View)

    var lastClick: Long?
    fun isFastClick(): Boolean {
        val now = SystemClock.elapsedRealtime()
        if (lastClick == null || now - lastClick!! >= 600) {
            lastClick = now
            return false
        }
        return true
    }

    override fun onClick(v: View) {
        if (!isFastClick()) {
            onWidgetClick(v)
        }
    }

    fun bindTitle(): String?

    fun bindTitleRes(): Int?

}