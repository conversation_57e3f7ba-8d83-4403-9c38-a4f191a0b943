package com.ruiheng.xmuse.core.ui.ui.web

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.webkit.ConsoleMessage
import android.webkit.JavascriptInterface
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.databinding.ActivityAgentWebBinding
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class AgentWebActivity : BaseBindingActivity<ActivityAgentWebBinding>() {
    override fun bindDataBindingView(): ActivityAgentWebBinding =
        ActivityAgentWebBinding.inflate(layoutInflater)


    companion object {
        private const val KEY_WEB_URL = "webUrl"
        private const val KEY_USER_TOKEN = "userToken"
        fun start(context: Context, url: String, userToken: String) {
            val intent = Intent(context, AgentWebActivity::class.java)
            intent.putExtra(KEY_WEB_URL, url)
            intent.putExtra(KEY_USER_TOKEN, userToken)
            context.startActivity(intent)
        }
    }

    private var webUrl: String? = null
    private var userToken: String? = null
    override fun initData(bundle: Bundle?) {
        super.initData(bundle)
        webUrl = bundle?.getString(KEY_WEB_URL, "https://www.xmuse.cn/")
        userToken = bundle?.getString(KEY_USER_TOKEN, "")
    }

    private var mAgentWeb: com.just.agentweb.AgentWeb? = null
    private val mWebChromeClient = object : com.just.agentweb.WebChromeClient() {

        override fun onConsoleMessage(p0: ConsoleMessage?): Boolean {
            Timber.d("LogFromWeb:${p0?.message()?.toString()}")
            if (p0?.message()?.toString()?.contains("打印错误") == true) {
                Timber.d("LogFromWeb:${p0?.message()?.toString()}")
            }
            return super.onConsoleMessage(p0)
        }

        override fun onReceivedTitle(view: WebView?, title: String?) {
            super.onReceivedTitle(view, title)
            Timber.d("onReceivedTitle:${title}")
            if (title?.startsWith("http") == false && title?.startsWith("qn.xmuse.cn") == false) {
                setToolbarTitle(title)
            } else {
                setToolbarTitle("")
            }
//            if (showTitle) {
//                if (fixedTitle.isNullOrBlank()) {
//                    if (title?.startsWith("http") == false) {
//                        setToolbarTitle(title)
//                    } else {
//                        setToolbarTitle("")
//                    }
//                } else {
//                    setToolbarTitle(fixedTitle)
//                }
//            } else {
//                setToolbarTitle("")
//            }
        }
    }

    private val mWebViewClient = object : com.just.agentweb.WebViewClient() {

        override fun onPageStarted(webView: WebView?, p1: String?, p2: Bitmap?) {
            super.onPageStarted(webView, p1, p2)
//            val barHeight = 100
//            val js = "window.barsize = ${barHeight};"
//            mAgentWeb?.jsAccessEntrace?.callJs(js)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
//            val barHeight = 200
//            val js = "window.barHeight = ${barHeight};"
//            mAgentWeb?.jsAccessEntrace?.callJs("window.updateSize(200)")

//            mAgentWeb?.jsAccessEntrace?.callJs(js)
//            view?.evaluateJavascript(js, null)
//            setPageToke()
//            initCallback?.invoke(contentView1!!, url)

            startSendToken()
//            RetrofitNetworkModule.saveUserToken()
        }


        private var lastUrl: Uri? = null
        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest?
        ): Boolean {
            return super.shouldOverrideUrlLoading(view, request)

        }
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

//        this.contentView1 = contentView
//        initCallback?.invoke(contentView, null)

//        val errorView = LayoutInflater.from(this).inflate(R.layout.empty_web_view, null)
        mAgentWeb = com.just.agentweb.AgentWeb.with(this)
            .setAgentWebParent(
                binding.container,
                LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)
            )
            .useDefaultIndicator(
                ContextCompat.getColor(
                    this,
                    R.color.colorAccent
                )
            )
            .setWebChromeClient(mWebChromeClient)
            .setWebViewClient(mWebViewClient)
//            .setMainFrameErrorView(errorView)
            .setSecurityType(com.just.agentweb.AgentWeb.SecurityType.STRICT_CHECK)
            .setWebLayout(WebLayout(this))
            .setOpenOtherPageWays(com.just.agentweb.DefaultWebClient.OpenOtherPageWays.ASK)
            .interceptUnkownUrl()
            .createAgentWeb()
            .ready()
            .go(webUrl)
        mAgentWeb?.getWebCreator()?.getWebView()?.getSettings()
            ?.setCacheMode(WebSettings.LOAD_NO_CACHE)
        mAgentWeb?.jsInterfaceHolder?.addJavaObject("xmuseAndroid", ProgramJavascriptInterface())
//        val frameLayout = mAgentWeb?.webCreator?.webParentLayout
//        frameLayout?.setBackgroundColor(compatColor(R.color.grey50))

    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (mAgentWeb?.handleKeyEvent(keyCode, event) == true) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onPause() {
        mAgentWeb?.webLifeCycle?.onPause()
        super.onPause()
    }

    override fun onResume() {
        mAgentWeb?.webLifeCycle?.onResume()
        super.onResume()
    }

    override fun onDestroy() {
        mAgentWeb?.clearWebCache()
        mAgentWeb?.webLifeCycle?.onDestroy()
        super.onDestroy()
    }

    private fun startSendToken() {
        Timber.d("!!!!!!!startSendToken")
        mAgentWeb?.jsAccessEntrace?.callJs("window.fromNative(${CommandUserToken(token = userToken ?: "").toJson()})")
        mAgentWeb?.jsAccessEntrace?.callJs("window.fromNative(${CommandAppBarInfo(barHeight = (68.px).toInt()).toJson()})")
    }

    inner class ProgramJavascriptInterface {
        @JavascriptInterface
        fun processWebMsg(jsonString: String) {
            runOnUiThread {
                executeCommandFromJson(jsonString)
            }
        }
    }

    private val COMMAND_KEY = "command"
    private val REQUEST_TOKEN = "requestToken"
    fun executeCommandFromJson(jsonString: String) {
        val jsonMap = try {
            Gson().fromJson(jsonString, Map::class.java)
        } catch (e: Exception) {
            return
        }
        when (jsonMap[COMMAND_KEY] as? String) {
            REQUEST_TOKEN -> {
                startSendToken()
            }

            else -> {

            }
        }
    }

    override fun bindNavigationSpaceView() = binding.viewNavigationSpace

//    override fun onBackPressed() {
//        if (mAgentWeb?.back() == false) {
//            finish()
//        }
//    }
}