/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.core.ui.ui

import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.asLiveData
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

/**
 * A lazy property that gets cleaned up when the fragment is destroyed.
 *
 * Accessing this variable in a destroyed fragment will throw NPE.
 */
class FragmentAutoClearedValue<T : Any>(val fragment: Fragment) : ReadWriteProperty<Fragment, T> {
    private var _value: T? = null

    init {
        fragment.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _value = null
            }
        })
    }

    override fun getValue(thisRef: Fragment, property: KProperty<*>): T {
        return _value ?: throw IllegalStateException(
            "should never call auto-cleared-value get when it might not be available"
        )
    }

    override fun setValue(thisRef: Fragment, property: KProperty<*>, value: T) {
        _value = value
    }
}

fun <T : Any> Fragment.autoCleared() = FragmentAutoClearedValue<T>(this)

fun <K : Any?, T : Flow<K>> Fragment.autoStoppedFlow(flow: T, callback: (K) -> Unit) {
    lifecycleScope.launch {
        flow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)
            .collect {
                callback.invoke(it)
            }
    }
}

fun <D : Any, K : Result<D>, T : Flow<K>> Fragment.autoRemoveFlow(
    flow: T?,
    callback: (K) -> Unit
) {
    lifecycleScope.launch {
        val flowLiveData = flow?.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)?.asLiveData()
        flowLiveData?.observe(this@autoRemoveFlow) { result ->
            callback.invoke(result)
            if (!result.isLoading()) {
                flowLiveData.removeObservers(this@autoRemoveFlow)
            }
        }
    }
}

fun <D : Any?, K : Result<D>, T : LiveData<K>> Fragment.autoRemoveLiveData(
    liveData: T?,
    callback: (K) -> Unit
) {
    liveData?.observe(this@autoRemoveLiveData) { result ->
        callback.invoke(result)
        if (!result.isLoading()) {
            liveData.removeObservers(this@autoRemoveLiveData)
        }
    }
}

fun <D : Any?, K : Result<D>, T : LiveData<K>> LifecycleOwner.autoRemoveLiveData(
    liveData: T?,
    callback: (K) -> Unit
) {
    liveData?.observe(this@autoRemoveLiveData) { result ->
        callback.invoke(result)
        if (!result.isLoading()) {
            liveData.removeObservers(this@autoRemoveLiveData)
        }
    }
}
