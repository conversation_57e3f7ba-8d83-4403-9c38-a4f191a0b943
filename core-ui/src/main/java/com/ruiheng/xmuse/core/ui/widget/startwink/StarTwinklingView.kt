package com.ruiheng.xmuse.core.ui.widget.startwink

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.ruiheng.xmuse.core.ui.R
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.min
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

class StarTwinklingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 星星相关属性
    private val stars = mutableListOf<Star>()
    private val starPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        style = Paint.Style.FILL
    }

    // 流星相关属性
    private val meteors = mutableListOf<Meteor>()
    private val meteorPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeCap = Paint.Cap.ROUND
        isAntiAlias = true
        isDither = true
    }

    // 流星颜色选项
    private val defaultMeteorColors = listOf(
        Color.WHITE
    )
    private var meteorColors = defaultMeteorColors

    private var viewWidth = 0
    private var viewHeight = 0
    private var starCount = 100 // 星星数量
    private var meteorCount = 1 // 最大同时显示的流星数
    private var meteorProbability = 0.05f // 每帧生成流星的概率
    private var isAnimating = false
    private var lastUpdateTime = 0L
    private val updateInterval = 25L // 更新间隔(毫秒)

    init {
        // 加载自定义属性
        context.obtainStyledAttributes(attrs, R.styleable.StarTwinklingView).apply {
            starCount = getInteger(R.styleable.StarTwinklingView_starCount, starCount)
            meteorCount = getInteger(R.styleable.StarTwinklingView_meteorCount, meteorCount)
            meteorProbability = getFloat(R.styleable.StarTwinklingView_meteorProbability, meteorProbability)
            recycle()
        }
    }

    /**
     * 视图尺寸变化时调用，初始化星星和开始动画
     */
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        viewWidth = w
        viewHeight = h

        // 初始化星星
        stars.clear()
        repeat(starCount) {
            stars.add(createRandomStar())
        }

        // 开始动画
        if (!isAnimating) {
            isAnimating = true
            postInvalidateOnAnimation()
        }
    }


    /**
     * 创建随机属性的星星
     */
    private fun createRandomStar(): Star {
        return Star(
            x = Random.nextFloat() * viewWidth,
            y = Random.nextFloat() * viewHeight,
            size = 1f + Random.nextFloat() * 3f,
            brightness = Random.nextFloat(),
            speed = 0.02f + Random.nextFloat() * 0.01f
        )
    }

    /**
     * 创建随机属性的流星，确保从右上角到左下角的完整轨迹
     */
    private fun createRandomMeteor(): Meteor {
        // 起始位置在右上角区域
        val startX = viewWidth * (0.9f + Random.nextFloat() * 0.1f)
        val startY = viewHeight * Random.nextFloat() * 0.2f

        // 计算到左下角的精确角度
        val angleToBottomLeft = atan2((viewHeight - startY).toDouble(), (-startX).toDouble())

        // 添加一些随机角度偏移，使流星轨迹更自然
        val angleOffset = Math.toRadians(Random.nextDouble(10.0, 20.0))
        val finalAngle = angleToBottomLeft + angleOffset

        // 计算流星需要移动的总距离
        val distanceToTravel = sqrt((viewWidth * viewWidth + viewHeight * viewHeight).toFloat()) * 1.2

        return Meteor(
            x = startX,
            y = startY,
            angle = finalAngle,
            speed = 8f, // 随机速度(8-20px/帧)
            length = 240f + Random.nextFloat() * 50f, // 随机长度
            maxAlpha = 255f, // 最大透明度
            distanceToTravel = distanceToTravel, // 总移动距离
            distanceTraveled = 0.0,
            color = meteorColors.random()
        )
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val currentTime = System.currentTimeMillis()
        if (currentTime - lastUpdateTime >= updateInterval) {
            updateStars()
            updateMeteors()
            maybeCreateMeteor()
            lastUpdateTime = currentTime
        }

        // 绘制所有星星
        for (star in stars) {
            starPaint.alpha = (star.brightness * 255).toInt()
            canvas.drawCircle(star.x, star.y, star.size, starPaint)
        }

        // 绘制所有流星
        for (meteor in meteors) {
            // 计算流星透明度（随距离增加而降低）
            val alphaFactor = 1.0 - min(1.0, meteor.distanceTraveled / meteor.distanceToTravel * 1.2)
            val alpha = (meteor.maxAlpha * alphaFactor).toInt()

            // 设置画笔颜色和透明度
            meteorPaint.color = Color.argb(
                alpha,
                Color.red(meteor.color),
                Color.green(meteor.color),
                Color.blue(meteor.color)
            )

            // 计算流星头部位置
            val headX = meteor.x
            val headY = meteor.y

            // 计算流星尾部位置
            val tailX = headX - cos(meteor.angle) * meteor.length
            val tailY = headY - sin(meteor.angle) * meteor.length

            // 绘制流星轨迹（头部粗，尾部细）
            for (i in 0 until 10) {
                val segmentLength = meteor.length / 10
                val startFraction = i / 10f
                val endFraction = (i + 1) / 10f

                val startX = headX - cos(meteor.angle) * segmentLength * i
                val startY = headY - sin(meteor.angle) * segmentLength * i
                val endX = headX - cos(meteor.angle) * segmentLength * (i + 1)
                val endY = headY - sin(meteor.angle) * segmentLength * (i + 1)

                // 头部最粗，尾部最细
                meteorPaint.strokeWidth = meteor.width * (1.0f - startFraction * 0.9f)
                canvas.drawLine(startX.toFloat(), startY.toFloat(), endX.toFloat(), endY.toFloat(), meteorPaint)
            }

            // 绘制流星头部（最亮的部分）
            meteorPaint.strokeWidth = meteor.width * 2
            canvas.drawPoint(headX.toFloat(), headY.toFloat(), meteorPaint)
        }

        // 继续动画
        if (isAnimating) {
            postInvalidateOnAnimation()
        }
    }

    private fun updateStars() {
        for (star in stars) {
            star.brightness += star.speed

            if (star.brightness > 1f) {
                star.brightness = 1f
                star.speed = -star.speed
            } else if (star.brightness < 0f) {
                star.brightness = 0f
                star.speed = -star.speed
            }
        }
    }

    private fun updateMeteors() {
        val iterator = meteors.iterator()
        while (iterator.hasNext()) {
            val meteor = iterator.next()

            // 计算这一帧移动的距离
            val distanceThisFrame = meteor.speed * (updateInterval / 16f) // 标准化速度

            // 更新流星位置
            meteor.x += (cos(meteor.angle) * distanceThisFrame).toFloat()
            meteor.y += (sin(meteor.angle) * distanceThisFrame).toFloat()
            meteor.distanceTraveled += distanceThisFrame

            // 移除已经完成旅程的流星
            if (meteor.distanceTraveled >= meteor.distanceToTravel) {
                iterator.remove()
            }
        }
    }

    private fun maybeCreateMeteor() {
        if (meteors.size < meteorCount && Random.nextFloat() < meteorProbability) {
            meteors.add(createRandomMeteor())
        }
    }

    // 设置星星数量
    fun setStarCount(count: Int) {
        this.starCount = count
        if (viewWidth > 0 && viewHeight > 0) {
            stars.clear()
            repeat(starCount) {
                stars.add(createRandomStar())
            }
        }
    }

    // 设置流星参数
    fun setMeteorParams(count: Int, probability: Float) {
        this.meteorCount = count
        this.meteorProbability = probability.coerceIn(0f, 0.05f)
    }

    // 设置流星颜色
    fun setMeteorColors(colors: List<Int>) {
        this.meteorColors = colors.ifEmpty { defaultMeteorColors }
    }

    // 星星数据类
    data class Star(
        var x: Float,
        var y: Float,
        var size: Float,
        var brightness: Float,
        var speed: Float
    )

    // 流星数据类
    data class Meteor(
        var x: Float,
        var y: Float,
        val angle: Double,
        val speed: Float,
        val length: Float,
        val width: Float = 2f + Random.nextFloat(),
        val maxAlpha: Float,
        val distanceToTravel: Double,
        var distanceTraveled: Double,
        val color: Int
    )
}