package com.ruiheng.xmuse.core.ui.ui

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.helper.dialog.FullScreenLoadingDialogFragment

/**
 *     author: YQ09506
 *     email : <EMAIL>
 *     time  : 2024/10/11
 *     desc  :
 */
abstract class BaseFragment : Fragment(), IBaseView {
    private var btnToolbarBack: AppCompatImageView? = null
    private var tvTitle: AppCompatTextView? = null
    private var imgBpHomeTitleLogo: AppCompatImageView? = null
    private var mViewSpaceToolbar: View? = null

    override var lastClick: Long? = null

    override fun bindTitle(): String? = null

    override fun bindTitleRes(): Int? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tvTitle = view.findViewById(R.id.tv_toolbar_title)
        setToolbarTitle()
        btnToolbarBack = view.findViewById(R.id.btn_toolbar_back)
        btnToolbarBack?.setOnClickListener(this)

        // 初始化logo并设置点击事件（如果存在的话）
        setupLogoClickListener(view)

        mViewSpaceToolbar = view.findViewById(R.id.view_toolbar_gradient)

        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val topHeight = if (systemBars.top == 0) 1 else systemBars.top
            val param = mViewSpaceToolbar?.layoutParams
            param?.height = topHeight
            mViewSpaceToolbar?.layoutParams = param
            insets
        }

        (requireActivity() as BaseActivity).bottomNavigationSpaceLiveData.observe(viewLifecycleOwner) { navigationBarHeight ->
            if (navigationBarHeight > 0) {
                val viewBottomSpace = bindNavigationSpaceView()
                if (viewBottomSpace != null) {
                    (requireActivity() as BaseActivity).bindNavigationSpaceView()?.visibility =
                        View.GONE
                } else {
                    (requireActivity() as BaseActivity).bindNavigationSpaceView()?.visibility =
                        View.VISIBLE
                }
                val navigationParam = viewBottomSpace?.layoutParams
                navigationParam?.height = navigationBarHeight
                viewBottomSpace?.layoutParams = navigationParam
            }
        }

        initView(savedInstanceState, view)
    }

    override fun onResume() {
        super.onResume()
        (requireActivity() as BaseActivity).bottomNavigationSpaceLiveData.removeObservers(viewLifecycleOwner)
        (requireActivity() as BaseActivity).bottomNavigationSpaceLiveData.observe(viewLifecycleOwner) { navigationBarHeight ->
            if (navigationBarHeight > 0) {
                val viewBottomSpace = bindNavigationSpaceView()
                if (viewBottomSpace != null) {
                    (requireActivity() as BaseActivity).bindNavigationSpaceView()?.visibility =
                        View.GONE
                } else {
                    (requireActivity() as BaseActivity).bindNavigationSpaceView()?.visibility =
                        View.VISIBLE
                }
                val navigationParam = viewBottomSpace?.layoutParams
                navigationParam?.height = navigationBarHeight
                viewBottomSpace?.layoutParams = navigationParam
            }
        }
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
    }

    protected fun setToolbarTitle(updateTitle: String? = null) {
        if (!updateTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = updateTitle
            return
        }
        val bindResTitle = if (bindTitleRes() != null) getString(bindTitleRes()!!) else null
        val mTitle = if (bindTitle() != null) bindTitle() else bindResTitle
        if (!mTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = mTitle
        } else if (!TextUtils.isEmpty(bindTitle())) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = bindTitle()
        } else {
            tvTitle?.visibility = View.INVISIBLE
        }
    }

    override fun onWidgetClick(view: View) {
        if (view == btnToolbarBack) {
            requireActivity().onBackPressed()
        } else if (view == imgBpHomeTitleLogo) {
            navigateToMainActivity()
        } else {
//            super.onWidgetClick(view)
        }
    }

    protected var loadingDialog: FullScreenLoadingDialogFragment? = null
    fun showLoadingDialog() {
        try {
            if (!childFragmentManager.isStateSaved) {
                loadingDialog?.dismiss()
                loadingDialog = null
            }
            loadingDialog = FullScreenLoadingDialogFragment()
            loadingDialog?.showLoadingDialog(childFragmentManager)
        } catch (e: Exception) {

        }
    }

    fun isLoadingDialogShowing(): Boolean {
        return loadingDialog != null && !loadingDialog!!.isVisible && !loadingDialog!!.isAdded
    }

    fun hideLoadingDialog() {
        try {
            loadingDialog?.dismiss()
            loadingDialog = null
        } catch (_: Exception) {

        }
    }

    open fun bindNavigationSpaceView(): View? = null

    /**
     * 设置logo点击监听器
     * 子类可以重写getLogoResourceId方法来指定logo的资源ID
     */
    private fun setupLogoClickListener(view: View) {
        try {
            val logoId = getLogoResourceId()
            if (logoId != null) {
                imgBpHomeTitleLogo = view.findViewById(logoId)
                imgBpHomeTitleLogo?.setOnClickListener(this)
            }
        } catch (e: Exception) {
            // 如果找不到logo资源ID，则忽略
            e.printStackTrace()
        }
    }

    /**
     * 获取logo的资源ID
     * 子类可以重写此方法来指定不同的logo资源ID
     * 默认返回null，表示没有logo
     */
    protected open fun getLogoResourceId(): Int? {
        return null
    }

    /**
     * 导航到主页面
     * 子类可以重写此方法来自定义行为，比如HomeFragment可以重写为不执行任何操作
     */
    protected open fun navigateToMainActivity() {
        try {
            val mainActivityClassName = getMainActivityClassName()
            val mainActivityClass = Class.forName(mainActivityClassName)
            val intent = Intent(requireContext(), mainActivityClass)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            requireActivity().finish()
        } catch (e: ClassNotFoundException) {
            // 如果找不到主Activity类，则不执行任何操作
            e.printStackTrace()
        }
    }

    /**
     * 获取主Activity的类名
     * 子类可以重写此方法来指定不同的主Activity
     */
    protected open fun getMainActivityClassName(): String {
        // 默认返回通用的MainActivity类名
        return "${requireContext().packageName}.ui.MainActivity"
    }

//    override fun isBaseOnWidth() = this !is LandscapePage
//
//    override fun getSizeInDp() = 375F
}