package com.ruiheng.xmuse.core.ui.custom

import android.graphics.Rect
import android.graphics.Typeface
import android.view.Gravity
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.px

interface PublicOptionImp {

    var checked: Boolean

    fun bindTitle(): Any
    fun bindImage(): Any? = null
    fun bindContent(): Any? = null
    fun bindImageWidth(): Int? = null
    fun bindImageHeight(): Int? = null
    fun bindTitleGravity(): Int? = null
    fun bindNormalTextColorAttr(): Int = com.google.android.material.R.attr.colorOnPrimaryContainer
    fun bindSelectedTextColorAttr(): Int = com.google.android.material.R.attr.colorPrimary
    fun bindBgResources(): Int = R.drawable.shape_corner_surface_bg
    fun bindSelectedBgResources(): Int = R.drawable.shape_corner_surface_selected_bg
    fun bindChecked(): Boolean = checked
    fun bindTextTypeFace() = Typeface.BOLD
    fun bindId(): String? = null

    //    fun <T : PublicOptionImp> bindDiffUtil() = object :
//        DiffUtil.ItemCallback<T>() {
//        override fun areItemsTheSame(oldItem: T, newItem: T) =
//            oldItem.bindTitle() == newItem.bindTitle()
//
//        override fun areContentsTheSame(oldItem: T, newItem: T) =
//            false
//    }
    fun <T : PublicOptionImp> bindContentTheSame(newItem: T) = false
}

interface PublicMenuImp {
    fun bindTitle(): Any
    fun bindTitleGravity(): Int = Gravity.START
    fun bindImage(): Any? = null
    fun bindArrowImage(): Any? = null
    fun bindContent(): Any? = null
    fun bindImageWidth(): Int? = null
    fun bindImageHeight(): Int? = null
    fun bindTextColorAttr(): Int = com.google.android.material.R.attr.colorOnPrimaryContainer
    fun bindBgResources(): Int = R.drawable.shape_corner_surface_bg
    fun bindArrowShow() = View.VISIBLE
    fun bindContentImage(): Any? = null
}

data class PublicMenuNormalText(
    val title: Any,
    val content: Any? = null,
    val showArrow: Int = View.VISIBLE
) :
    PublicMenuImp {
    override fun bindTitle() = title
    override fun bindContent() = content
    override fun bindArrowShow(): Int = showArrow
}

data class PublicMenuNormalImage(
    val title: String,
    val image: Int,
    val showArrow: Int = View.VISIBLE,
    val imageWidth: Int = (24.px).toInt(),
    val imageHeight: Int = (24.px).toInt()
) :
    PublicMenuImp {
    override fun bindImageHeight() = imageHeight
    override fun bindImageWidth() = imageWidth
    override fun bindImage() = image
    override fun bindTitle() = title
    override fun bindArrowShow(): Int = showArrow
}

data class PublicOptionNormalText(
    val title: String,
    override var checked: Boolean = false,
) : PublicOptionImp {
    override fun bindTitle() = title
    override fun bindTitleGravity() = Gravity.CENTER
}

class PublicOptionNormalTextItemDecoration() : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val layoutManager = parent.layoutManager
        val position = parent.getChildAdapterPosition(view)
        if (layoutManager is GridLayoutManager) {
            val spanCount = layoutManager.spanCount
            if (layoutManager.orientation == RecyclerView.VERTICAL) {
                if (position % spanCount != 0) {
                    outRect.left = (8.px).toInt()
                }
            }
            if (layoutManager.orientation == RecyclerView.HORIZONTAL) {
                outRect.right = (12.px).toInt()
            }
        }
        if (layoutManager is LinearLayoutManager) {
            if (layoutManager.orientation == RecyclerView.VERTICAL) {
                outRect.bottom = (16.px).toInt()
            }
        }
//        super.getItemOffsets(outRect, view, parent, state)
    }
}

class GridLayoutItemDecoration(private val paddingSize: Int = (8.px).toInt()) :
    RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val layoutManager = parent.layoutManager
        val position = parent.getChildAdapterPosition(view)
        if (layoutManager is GridLayoutManager) {
            val spanCount = layoutManager.spanCount
            if (layoutManager.orientation == RecyclerView.VERTICAL) {
                outRect.right = paddingSize
            }
        }
//        super.getItemOffsets(outRect, view, parent, state)
    }
}
