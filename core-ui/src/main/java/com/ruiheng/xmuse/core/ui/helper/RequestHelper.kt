package com.ruiheng.xmuse.core.ui.helper

import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.ui.ui.BaseActivity
import com.ruiheng.xmuse.core.ui.ui.BaseFragment

fun <T> Result<T>.controlLoading(baseFragment: BaseFragment) {
    if (this is Result.Loading) {
        baseFragment.showLoadingDialog()
    } else {
        baseFragment.hideLoadingDialog()
    }
}

fun <T> Result<T>.controlLoading(baseActivity: BaseActivity) {
    if (this is Result.Loading) {
        baseActivity.showLoadingDialog()
    } else {
        baseActivity.hideLoadingDialog()
    }
}