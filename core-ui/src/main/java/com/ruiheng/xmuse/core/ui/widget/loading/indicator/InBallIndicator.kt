package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint

class InBallIndicator : Indicator() {
    companion object {
        const val SCALE = 1.0F
    }

    private var scaleFloatArray = FloatArray(5) { SCALE }

    override fun draw(canvas: Canvas, paint: Paint) {
        val circleSpacing = 8f
        val radius: Float = (Math.min(getWidth(), getHeight()) - circleSpacing * 2) / 6
        val x: Float = getWidth() / 2 - (radius * 2 + circleSpacing)
        val y: Float = (getHeight() / 2).toFloat()
        for (i in 0..4) {
            canvas.save()
            val translateX = x + (radius * 2) * i + circleSpacing * i
            canvas.translate(translateX, y)
            canvas.scale(scaleFloatArray[i], scaleFloatArray[i])
            canvas.drawCircle(0F, 0F, radius, paint)
            canvas.restore()
        }
    }

    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = ArrayList<ValueAnimator>()
        val delays = intArrayOf(120, 240, 360 , 480 ,600)
        for (i in 0..4) {

            val scaleAnimator = ValueAnimator.ofFloat(0.9F, 0.6f, 0.9F)
            scaleAnimator.duration = 750
            scaleAnimator.repeatCount = -1
            scaleAnimator.startDelay = delays[i].toLong()

            addUpdateListener(scaleAnimator, ValueAnimator.AnimatorUpdateListener {
                scaleFloatArray[i] = it.animatedValue as Float
                postInvalidate()
            })
            animators.add(scaleAnimator)
        }
        return animators
    }

}