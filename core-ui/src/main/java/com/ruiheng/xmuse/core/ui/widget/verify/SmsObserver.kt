package com.ruiheng.xmuse.core.ui.widget.verify

import android.content.ContentResolver
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import com.blankj.utilcode.util.Utils
import timber.log.Timber
import java.util.regex.Pattern

class SmsObserver(
    private val handler: <PERSON>ler,
    private val onSmsReceived: (String) -> Unit
) : ContentObserver(handler) {
    override fun onChange(selfChange: Boolean, uri: Uri?) {
        super.onChange(selfChange,uri)
        uri?.let {
            if (it.toString().contains("content://sms/")) {
                val code = getSmsCode()
                code?.let { onSmsReceived(it) }
            }
        }
    }
    private fun getSmsCode(): String? {
        val contentResolver: ContentResolver = Utils.getApp().contentResolver
        val cursor = contentResolver.query(
            Uri.parse("content://sms/inbox"),
            null,
            null,
            null,
            "date DESC"
        )
        cursor?.use {
            if (it.moveToFirst()) {
                val bodyIndex = it.getColumnIndex("body")
                val body = it.getString(bodyIndex)
                Timber.d("!!!!!!!SmsObserver${body}")
                return extractVerificationCode(body)
            }
        }
        return null
    }

    private fun extractVerificationCode(smsBody: String): String? {
        val pattern = Pattern.compile("\\d{4,6}")
        val matcher = pattern.matcher(smsBody)
        if (matcher.find()) {
            return matcher.group()
        }
        return null
    }
}