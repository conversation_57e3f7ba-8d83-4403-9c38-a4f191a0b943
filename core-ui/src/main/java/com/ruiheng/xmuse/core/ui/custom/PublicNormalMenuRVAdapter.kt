package com.ruiheng.xmuse.core.ui.custom

import android.view.LayoutInflater
import android.view.ViewGroup
import com.ruiheng.xmuse.core.ui.databinding.ItemPublicMenuRowBinding
import com.ruiheng.xmuse.core.ui.showImage

open class PublicNormalMenuRVAdapter<T : PublicMenuImp>(private val onItemClickListener: ((T) -> Unit)? = null) :
    PublicMenuRVAdapter<T, ItemPublicMenuRowBinding>() {

    override fun bindDataBindingView(
        inflater: LayoutInflater,
        parent: ViewGroup?
    ): ItemPublicMenuRowBinding {
        val binding = ItemPublicMenuRowBinding.inflate(inflater, parent, false)
        binding.root.setOnClickListener {
            if (isFastClick()) return@setOnClickListener
            binding.option?.let {
                onItemClickListener?.invoke(it as T)
            }
        }
        return binding
    }


    override fun bind(binding: ItemPublicMenuRowBinding, item: T, position: Int) {
        binding.option = item
        binding.imgPublicMenuEnd.visibility = item.bindArrowShow()
        val arrowImage = item.bindArrowImage()
        if (arrowImage != null) {
            binding.imgPublicMenuEnd.showImage(arrowImage)
        }
        initBaseInfo(
            item,
            binding,
            binding.tvPublicOption,
            binding.tvPublicOptionContent,
            binding.imgPublicOption,
            binding.imgPublicOptionContent,
            binding.layoutContainer
        )
    }
}