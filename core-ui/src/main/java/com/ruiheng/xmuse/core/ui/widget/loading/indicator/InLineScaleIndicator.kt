package com.ruiheng.xmuse.core.ui.widget.loading.indicator

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF

open class InLineScaleIndicator : Indicator() {
    var scaleYFloatArray = floatArrayOf(
        SCALE,
        SCALE,
        SCALE,
        SCALE,
        SCALE
    )

    var lineCount = 4
    
    override fun draw(canvas: Canvas, paint: Paint) {
        val translateX = (getWidth() / (lineCount * 2 + 1)).toFloat()
        val translateY = (getHeight() / 2).toFloat()
        for (i in 0 until lineCount) {
            canvas.save()
            canvas.translate((2 + i * 2) * translateX - translateX / 2, translateY)
            canvas.scale(SCALE, scaleYFloatArray[i])
            val rectF = RectF(-translateX / 2, -getHeight() / 2.5f, translateX / 2, getHeight() / 2.5f)
            canvas.drawRoundRect(rectF, 5f, 5f, paint)
            canvas.restore()
        }
    }

    override fun onCreateAnimators(): ArrayList<ValueAnimator> {
        val animators = ArrayList<ValueAnimator>()
        val delays = longArrayOf(100, 200, 300, 400, 500)
        for (i in 0 until lineCount) {
            val scaleAnimator = ValueAnimator.ofFloat(1f, 0.4f, 1f)
            scaleAnimator.duration = 1000
            scaleAnimator.repeatCount = -1
            scaleAnimator.startDelay = delays[i]
            addUpdateListener(scaleAnimator, ValueAnimator.AnimatorUpdateListener { animation ->
                scaleYFloatArray[i] = (animation.animatedValue as Float)
                postInvalidate()
            })
            animators.add(scaleAnimator)
        }
        return animators
    }

    companion object {
        const val SCALE = 1.0f
    }

}