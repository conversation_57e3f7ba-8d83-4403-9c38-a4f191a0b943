package com.ruiheng.xmuse.core.ui.ui

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.BarUtils
import com.ruiheng.xmuse.core.ui.R
import com.ruiheng.xmuse.core.ui.helper.dialog.FullScreenLoadingDialogFragment
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurface

abstract class BaseActivity : AppCompatActivity(), IBaseView {

    private var btnToolbarBack: AppCompatImageView? = null
    private var tvTitle: AppCompatTextView? = null
    private var imgBpHomeTitleLogo: AppCompatImageView? = null
    override var lastClick: Long? = null
    private var mViewSpaceToolbar: View? = null

    //适配手机底部导航栏，某些Activity直接留出区域，某些Activity可以通过该值进行全面屏适配
    val bottomNavigationSpaceLiveData = MutableLiveData<Int>(1)

    override fun bindTitle(): String? = null

    override fun bindTitleRes(): Int? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        initData(intent.extras)

        val rootView = bindContentView()
        setContentView(rootView)

        tvTitle = rootView.findViewById(R.id.tv_toolbar_title)
        setToolbarTitle()
        btnToolbarBack = rootView.findViewById(R.id.btn_toolbar_back)
        btnToolbarBack?.setOnClickListener(this)

        // 初始化logo并设置点击事件（如果存在的话）
        setupLogoClickListener(rootView)

        mViewSpaceToolbar = rootView.findViewById(R.id.view_toolbar_gradient)
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val topHeight = systemBars.top
            val param = mViewSpaceToolbar?.layoutParams
            param?.height = topHeight
            mViewSpaceToolbar?.layoutParams = param

            val navigationBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars())
            val navigationBarHeight = navigationBars.bottom
            if (navigationBarHeight > 0) {
                val navigationSpace = bindNavigationSpaceView()
                val navigationParam = navigationSpace?.layoutParams
                navigationParam?.height = navigationBarHeight
                navigationSpace?.layoutParams = navigationParam
                bottomNavigationSpaceLiveData.value = navigationBarHeight
//                BarUtils.setNavBarLightMode(this ,true)
                if (navigationSpace != null) {
//                    navigationSpace.setBackgroundColor(baseContext.getColorOnSurface())
//                    BarUtils.setNavBarLightMode(this ,false)
                }
//                rootView.setPadding(
//                    rootView.paddingLeft,
//                    rootView.paddingTop,
//                    rootView.paddingRight,
//                    navigationBarHeight
//                )
            }

            insets
        }

        initView(savedInstanceState, rootView)
//        initView(savedInstanceState)
    }

    override fun initData(bundle: Bundle?) {

    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
    }

    protected fun setToolbarTitle(updateTitle: String? = null) {
        if (!updateTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = updateTitle
            return
        }
        val bindResTitle = if (bindTitleRes() != null) getString(bindTitleRes()!!) else null
        val mTitle = if (bindTitle() != null) bindTitle() else bindResTitle
        if (!mTitle.isNullOrEmpty()) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = mTitle
        } else if (!TextUtils.isEmpty(bindTitle())) {
            tvTitle?.visibility = View.VISIBLE
            tvTitle?.text = bindTitle()
        } else {
            tvTitle?.visibility = View.INVISIBLE
        }
    }

    override fun onWidgetClick(view: View) {
        if (view == btnToolbarBack) {
            onBackPressed()
        } else if (view == imgBpHomeTitleLogo) {
            navigateToMainActivity()
        } else {
//            super.onWidgetClick(view)
        }
    }

    protected var loadingDialog: FullScreenLoadingDialogFragment? = null
    fun showLoadingDialog() {
        try {
            loadingDialog?.dismiss()
            loadingDialog = null
            loadingDialog = FullScreenLoadingDialogFragment()
            loadingDialog?.showLoadingDialog(supportFragmentManager)
        } catch (e: Exception) {

        }
    }

    fun isLoadingDialogShowing(): Boolean {
        return loadingDialog != null && !loadingDialog!!.isVisible && !loadingDialog!!.isAdded
    }

    fun hideLoadingDialog() {
        try {
            loadingDialog?.dismiss()
            loadingDialog = null
        } catch (_: Exception) {

        }
    }

    protected abstract fun bindContentView(): View

//    override fun isBaseOnWidth() = this !is LandscapePage
//
//    override fun getSizeInDp() = 375F

    open fun bindNavigationSpaceView(): View? = null

    /**
     * 设置logo点击监听器
     * 子类可以重写getLogoResourceId方法来指定logo的资源ID
     */
    private fun setupLogoClickListener(rootView: View) {
        try {
            val logoId = getLogoResourceId()
            if (logoId != null) {
                imgBpHomeTitleLogo = rootView.findViewById(logoId)
                imgBpHomeTitleLogo?.setOnClickListener(this)
            }
        } catch (e: Exception) {
            // 如果找不到logo资源ID，则忽略
            e.printStackTrace()
        }
    }

    /**
     * 获取logo的资源ID
     * 子类可以重写此方法来指定不同的logo资源ID
     * 默认返回null，表示没有logo
     */
    protected open fun getLogoResourceId(): Int? {
        return null
    }

    /**
     * 导航到主页面
     * 子类可以重写此方法来自定义主页面的类名
     */
    protected open fun navigateToMainActivity() {
        try {
            val mainActivityClassName = getMainActivityClassName()
            val mainActivityClass = Class.forName(mainActivityClassName)
            val intent = Intent(this, mainActivityClass)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            finish()
        } catch (e: ClassNotFoundException) {
            // 如果找不到主Activity类，则不执行任何操作
            e.printStackTrace()
        }
    }

    /**
     * 获取主Activity的类名
     * 子类可以重写此方法来指定不同的主Activity
     */
    protected open fun getMainActivityClassName(): String {
        // 默认返回通用的MainActivity类名
        // 子类可以重写此方法来指定具体的MainActivity
        return "${packageName}.ui.MainActivity"
    }

}