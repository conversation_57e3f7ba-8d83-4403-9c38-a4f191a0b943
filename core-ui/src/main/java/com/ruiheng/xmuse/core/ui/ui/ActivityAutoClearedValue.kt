/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.core.ui.ui

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.asLiveData
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.isLoading
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

/**
 * A lazy property that gets cleaned up when the fragment is destroyed.
 *
 * Accessing this variable in a destroyed fragment will throw NPE.
 */
class ActivityAutoClearedValue<T : Any>(val activity: AppCompatActivity) : ReadWriteProperty<AppCompatActivity, T> {
    private var _value: T? = null

    init {
        activity.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _value = null
            }
        })
    }

    override fun getValue(thisRef: AppCompatActivity, property: KProperty<*>): T {
        return _value ?: throw IllegalStateException(
            "should never call auto-cleared-value get when it might not be available"
        )
    }

    override fun setValue(thisRef: AppCompatActivity, property: KProperty<*>, value: T) {
        _value = value
    }
}

fun <T : Any> AppCompatActivity.autoCleared() = ActivityAutoClearedValue<T>(this)

fun <K : Any?, T : Flow<K>> AppCompatActivity.autoStoppedFlow(flow: T, callback: (K) -> Unit) {
    lifecycleScope.launch {
        flow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)
            .collect {
                callback.invoke(it)
            }
    }
}

fun <D : Any, K : Result<D>, T : Flow<K>> AppCompatActivity.autoRemoveFlow(
    flow: T?,
    callback: (K) -> Unit
) {
    lifecycleScope.launch {
        val flowLiveData = flow?.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)?.asLiveData()
        flowLiveData?.observe(this@autoRemoveFlow) { result ->
            callback.invoke(result)
            if (!result.isLoading()) {
                flowLiveData.removeObservers(this@autoRemoveFlow)
            }
        }
    }
}