package com.ruiheng.xmuse.core.ui.widget.loading.indicator;

import android.animation.ValueAnimator;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;

import java.util.ArrayList;

class BallRotateMultipleIndicator extends Indicator {

    float degrees;


    @Override
    public void draw(Canvas canvas, Paint paint) {
        paint.setStrokeWidth(3);
        paint.setStyle(Paint.Style.STROKE);

        float circleSpacing = 12;
        float x = getWidth() / 2;
        float y = getHeight() / 2;

        canvas.save();

        canvas.translate(x, y);
        canvas.rotate(degrees);

        //draw two big arc
        float[] bStartAngles = new float[]{135, -45};
        for (int i = 0; i < 2; i++) {
            RectF rectF = new RectF(-x + circleSpacing, -y + circleSpacing, x - circleSpacing, y - circleSpacing);
            canvas.drawArc(rectF, bStartAngles[i], 90, false, paint);
        }

        canvas.restore();
//        canvas.translate(x, y);
//        canvas.rotate(-degrees);
        //draw two small arc
//        float[] sStartAngles = new float[]{225, 45};
//        for (int i = 0; i < 2; i++) {
//            RectF rectF = new RectF(-x / 1.8f + circleSpacing, -y / 1.8f + circleSpacing, x / 1.8f - circleSpacing, y / 1.8f - circleSpacing);
//            canvas.drawArc(rectF, sStartAngles[i], 90, false, paint);
//        }
    }

    @Override
    public ArrayList<ValueAnimator> onCreateAnimators() {
        ArrayList<ValueAnimator> animators = new ArrayList<>();
      

        ValueAnimator rotateAnim = ValueAnimator.ofFloat(0, 180, 360);
        rotateAnim.setDuration(1500);
        rotateAnim.setRepeatCount(-1);
        addUpdateListener(rotateAnim, animation -> {
            degrees = (float) animation.getAnimatedValue();
            postInvalidate();
        });
        animators.add(rotateAnim);
        return animators;
    }

}
