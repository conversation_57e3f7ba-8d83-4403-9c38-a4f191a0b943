/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@Suppress("DSL_SCOPE_VIOLATION") // Remove when fixed https://youtrack.jetbrains.com/issue/KTIJ-19369
plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.hilt.gradle)
    alias(libs.plugins.ksp)
    alias(libs.plugins.kotlin.kapt)

}
//val fmod_api_root = project.file("src/fmodlib").absolutePath

android {
    namespace = "${rootProject.ext.get("namespacePrefix")}"

    compileSdk = 34

    defaultConfig {
        applicationId = "${rootProject.ext.get("namespacePrefix")}"
        minSdk = 26
        targetSdk = 34
        versionCode = 11010000 //前缀1 + 正式标志1 大版本号01 + 小版本XX +两位测试版本
        versionName = "V1.0.0"
        multiDexEnabled = true
        vectorDrawables {
            useSupportLibrary = true
        }
        ndk {
            abiFilters += listOf("arm64-v8a","x86_64")
        }
        // Enable room auto-migrations
        ksp {
            arg("room.schemaLocation", "$projectDir/schemas")
        }
    }

    signingConfigs {
        create("release") {
            keyAlias = "jdintreremote"
            keyPassword = "20110613"
            storeFile = file("../signature/IntretechKey.jks")
            storePassword = "20110613"
        }
    }


    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
//            buildConfigField("String[]", "FMOD_LIBS", "{ \"fmod\" }")
            externalNativeBuild {
                cmake {
//                    arguments(
//                        "-DFMOD_API_ROOT=$fmod_api_root",
//                        "-DFMOD_LIB_SUFFIX="
//                    )
                }
            }
        }
        debug {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
//            buildConfigField("String[]", "FMOD_LIBS", "{ \"fmod\" }")
            externalNativeBuild {
                cmake {
//                    arguments(
//                        "-DFMOD_API_ROOT=$fmod_api_root",
//                        "-DFMOD_LIB_SUFFIX="
//                    )
                }
            }
        }
    }

    externalNativeBuild {
        cmake {
//            cppFlags("-std=c++11", "-fno-exceptions", "-fno-rtti")
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.18.1"
        }
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("libs")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        aidl = false
        buildConfig = true
        renderScript = false
        shaders = false
        dataBinding = true
        viewBinding = true
    }

    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    implementation(project(":core-data"))
    implementation(project(":core-common"))
    implementation(project(":core-ui"))
    implementation(project(":feature:login"))
    implementation(project(":feature:user"))
    implementation(project(":feature:device"))

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar", "*.jar"))))
    implementation(libs.other.multidex)
    implementation(libs.other.agentweb)
    //UI
    implementation(libs.android.auto.size)

    // Core Android dependencies
    implementation(libs.androidx.constraintlayout)
    implementation(libs.material)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.glide.runtime)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    implementation(libs.other.mpandroidchart)
    implementation(libs.glide.transformations)

    kapt(libs.glide.annotation)
    // Hilt Dependency Injection
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    implementation(libs.other.rx.android)
    implementation(libs.other.rx.java)

//    debugImplementation(libs.other.leakcanary)

//    implementation(libs.picker.view)
}
