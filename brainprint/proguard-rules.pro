# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# 崩溃分析的混淆配置
-keep class com.aliyun.emas.apm.**{*;}
# 远程日志的混淆配置
-keep class com.taobao.tao.log.**{*;}
-keep public class * extends com.taobao.android.tlog.protocol.model.request.base.FileInfo{*;}
# 性能分析的混淆配置
-keep class com.taobao.monitor.impl.**{*;}
-keep class com.taobao.monitor.adapter.**{*;}
-keep class com.taobao.application.common.**{*;}
-keep class com.facebook.drawee.generic.RootDrawable{*;}
-keep class com.facebook.drawee.drawable.FadeDrawable{*;}
-keep class androidx.fragment.app.Fragment{*;}