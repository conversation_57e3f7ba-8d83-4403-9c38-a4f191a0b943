package com.ruiheng.xmuse.calculate

class XmuseEEGCalculateUtil {
    companion object {
        init {
            System.loadLibrary("eegcalculate")
        }
    }

    /**
     * 专注力
     */
    external fun attentionMethod(
        alphaArray: DoubleArray,
        betaArray: DoubleArray,
        gammaArray: DoubleArray,
        thetaArray: DoubleArray,
        deltaArray: DoubleArray,
        hsiArray: DoubleArray,
        electrodeNum: Int
    ): DoubleArray

    /**
     * 压力
     */
    external fun stressMethod(
        alphaArray: DoubleArray,
        betaArray: DoubleArray,
        gammaArray: DoubleArray,
        thetaArray: DoubleArray,
        deltaArray: DoubleArray,
        hsiArray: DoubleArray,
        electrodeNum: Int
    ): DoubleArray

    /**
     * 稳定
     */
    external fun stabilityMethod(
        alphaArray: DoubleArray,
        betaArray: DoubleArray,
        gammaArray: DoubleArray,
        thetaArray: DoubleArray,
        deltaArray: DoubleArray,
        hsiArray: DoubleArray,
        electrodeNum: Int
    ): DoubleArray

    /**
     * 疲劳
     */
    external fun anxietyMethod(
        alphaArray: DoubleArray,
        betaArray: DoubleArray,
        gammaArray: DoubleArray,
        thetaArray: DoubleArray,
        deltaArray: DoubleArray,
        hsiArray: DoubleArray,
        electrodeNum: Int
    ): DoubleArray

    external fun cognitionMethod(
        alphaArray: DoubleArray,
        betaArray: DoubleArray,
        gammaArray: DoubleArray,
        thetaArray: DoubleArray,
        deltaArray: DoubleArray,
        hsiArray: DoubleArray,
        electrodeNum: Int
    ): DoubleArray

}