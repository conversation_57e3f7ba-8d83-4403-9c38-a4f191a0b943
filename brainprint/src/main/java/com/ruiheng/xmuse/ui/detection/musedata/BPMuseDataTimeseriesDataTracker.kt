//package com.ruiheng.xmuse.ui.detection.musedata
//
//import com.choosemuse.libmuse.MuseDataPacket
//import com.choosemuse.libmuse.MuseDataPacketType
//import com.choosemuse.libmuse.Ppg
//import io.reactivex.rxjava3.core.Observable
//import io.reactivex.rxjava3.disposables.Disposable
//
//class BPMuseDataTimeseriesDataTracker() {
//
//    private val lock = Object()
//
//    //    private val inputBuffer = ArrayList<Double>()
//    private var disposeable: Disposable? = null
//    private var lastBuffer: List<Double> = listOf()
//    fun startTracking(inputData: Observable<Pair<MuseDataPacketType,MuseDataPacket>>) {
//        inputData
//            .map { pair->
//                val result = mutableListOf<Double>()
//                val packet = pair.second
//                if (pair.first == MuseDataPacketType.PPG){
//                    val irValue = packet.getPpgChannelValue(Ppg.IR)
//                    val redValue = packet.getPpgChannelValue(Ppg.RED)
//                    val  ambient = packet.getPpgChannelValue(Ppg.AMBIENT)
//                }
//            }
//            .subscribe {
//                synchronized(lock) {
//                    lastBuffer = it
//                }
//            }.let { disposeable = it }
//    }
//
//    fun stopTracking() {
//        disposeable?.dispose()
//        disposeable = null
//    }
//
//    fun processValue() = lastBuffer
//
//}