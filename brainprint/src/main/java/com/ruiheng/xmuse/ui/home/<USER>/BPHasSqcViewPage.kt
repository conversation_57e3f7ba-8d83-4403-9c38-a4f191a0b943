package com.ruiheng.xmuse.ui.home.helper

import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.ruiheng.xmuse.core.common.result.permission.requestDeviceBlePermission
import com.ruiheng.xmuse.feature.device.ui.BottomMuseDialog
import com.ruiheng.xmuse.feature.device.ui.BottomMuseListDialog
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.MuseStatusIndicatorView
import com.ruiheng.xmuse.ui.widget.pop.BPBluetoothConnectPopover

interface BPHasSqcViewPage {

    fun initSqc(
        activity: FragmentActivity, sqcLayout: MuseStatusIndicatorView,
        checkUser: () -> Boolean = { true },
        museListDialogCallback: (Boolean) -> Unit
    ): (Boolean) -> Boolean {
        var popDialog: BPBluetoothConnectPopover? = null
        val dialogVisibilityCheck: (Boolean) -> Boolean = { dismiss ->
            val isShowing = popDialog?.isShowing == true
            if (isShowing && dismiss) {
                popDialog?.dismiss()
            }
            isShowing
        }

        fun startDeviceDiscovery() {
            activity.requestDeviceBlePermission {
                if (popDialog?.isShowing == true) {
                    popDialog?.dismiss()
                    return@requestDeviceBlePermission
                }
                popDialog = BPBluetoothConnectPopover(activity, sqcLayout) {
                    museListDialogCallback.invoke(false)
//                    binding.viewMuseListMask.visibility = View.GONE
                }
//                binding.viewMuseListMask.visibility = View.VISIBLE
                museListDialogCallback.invoke(true)
                popDialog?.show()
//                BottomMuseListDialog().showMuseListDialog(fragment.childFragmentManager)
            }
        }

        sqcLayout.onBluetoothClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }

        sqcLayout.signalQualityOnClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }
        return dialogVisibilityCheck
    }

    fun <T : MuseStatusIndicatorView> initSqc(
        fragment: Fragment,
        sqcLayout: T,
        checkUser: () -> Boolean = { true },
        museListDialogCallback: (Boolean) -> Unit
    ): (Boolean) -> Boolean {
        var popDialog: BPBluetoothConnectPopover? = null
        val dialogVisibilityCheck: (Boolean) -> Boolean = { dismiss ->
            val isShowing = popDialog?.isShowing == true
            if (isShowing && dismiss) {
                popDialog?.dismiss()
            }
            isShowing
        }

        fun startDeviceDiscovery() {
            fragment.requireContext().requestDeviceBlePermission {
                if (popDialog?.isShowing == true) {
                    popDialog?.dismiss()
                    return@requestDeviceBlePermission
                }
                popDialog = BPBluetoothConnectPopover(fragment.requireActivity(), sqcLayout) {
                    museListDialogCallback.invoke(false)
//                    binding.viewMuseListMask.visibility = View.GONE
                }
//                binding.viewMuseListMask.visibility = View.VISIBLE
                museListDialogCallback.invoke(true)
                popDialog?.show()
//                BottomMuseListDialog().showMuseListDialog(fragment.childFragmentManager)
            }
        }

        sqcLayout.onBluetoothClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }

        sqcLayout.signalQualityOnClick = {
            if (checkUser.invoke()) {
                startDeviceDiscovery()
            }
        }
        return dialogVisibilityCheck
    }
}