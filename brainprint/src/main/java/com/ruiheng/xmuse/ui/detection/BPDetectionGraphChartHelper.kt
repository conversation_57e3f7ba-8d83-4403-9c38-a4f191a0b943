package com.ruiheng.xmuse.ui.detection

import android.content.Context
import android.graphics.Color
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.components.YAxis.AxisDependency
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.SessionConsts
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurfaceVariant
import com.ruiheng.xmuse.feature.device.ui.visualization.loadVisualizationLineColor

fun DetectionActivity.initChartGraph(
    char: LineChart,
    maxValue: Float,
    minValue: Float,
    customValueFormatter: ValueFormatter? = null
) {
    char.apply {
        description.isEnabled = false
        setTouchEnabled(true)
        isDragEnabled = true
        setScaleEnabled(true)
        setPinchZoom(false)
        isScaleXEnabled = true
        isScaleYEnabled = false
        setDrawGridBackground(false)

        val data = LineData()
        data.setValueTextColor(Color.BLACK)
        setData(data)
        legend.isEnabled = false
        xAxis.apply {
            isEnabled = false
        }
        axisLeft.apply {
            val color = Color.GRAY
            textColor = color
            axisMaximum = maxValue
            axisMinimum = minValue
            setDrawGridLines(true)
            if (customValueFormatter != null) {
                valueFormatter = customValueFormatter
            }
        }
        axisRight.isEnabled = false
    }
}

fun initBodyGraph(context: Context, char: LineChart) {
    val data = LineData()
    data.setValueTextColor(Color.BLACK)
    char.apply {
        description.isEnabled = false
        char.setData(data)
        xAxis.apply {
            isEnabled = true
            setDrawGridLines(false)
            position =  XAxis.XAxisPosition.BOTTOM
            valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    return ""
                }
            }
        }
        legend.isEnabled = false
    }
    char.initLineChartLeft(
        context,
        listOf(SessionConsts.BODY_STILL.toFloat(),SessionConsts.BODY_MAX.toFloat()),
        SessionConsts.BODY_MIN.toFloat(),
        SessionConsts.BODY_MAX.toFloat(),
        0.05f,
        10,
        mapOf(0.2f to "静态", 0.7f to "动态")
    )
}

fun LineChart.initLineChartLeft(
    context: Context,
    yDashLineValue: List<Float>,
    minimum: Float,
    maximum: Float,
    mGranularity: Float = 0.05f,
    mLabelCount: Int = 10,
    labelTextList: Map<Float, String>
) {
    axisLeft.apply {
//        axisLineColor = Color.TRANSPARENT
        setDrawGridLines(false) // 绘制网格线
        axisMinimum = minimum // Y轴最小值
        axisMaximum = maximum // Y轴最大值
        labelCount = 10
        granularity = mGranularity
        isGranularityEnabled = true
        textColor =
            context.getColorOnSurfaceVariant()
        setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART)
        valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                if (labelTextList.containsKey(value)) {
                    return labelTextList[value] ?: ""
                }
                return ""
            }
        }
        yDashLineValue.forEach { dashValue ->
            val limitLine1 = LimitLine(dashValue)
//            limitLine1.enableDashedLine(10f, 10f, 0f)
            limitLine1.lineColor = Color.DKGRAY
            addLimitLine(limitLine1)
        }
    }
    axisRight.isEnabled = false
}

fun DetectionActivity.loadSet(
    char: LineChart,
    size: Int,
    name: String,
    lineColor: Int
): ILineDataSet? {
    val data = char.data ?: return null
    fun createSet() = LineDataSet(null, name).apply {
        axisDependency = AxisDependency.LEFT
        color = lineColor
        setCircleColor(Color.WHITE)
        lineWidth = 1.5f
        circleRadius = 4f
        fillAlpha = 65
        highLightColor = lineColor
        valueTextColor = Color.WHITE
        valueTextSize = 9f
        setDrawValues(false)
        setDrawCircles(false)
        data.addDataSet(this)
    }

    val set = data.getDataSetByLabel(name, true) ?: createSet()
    return set
}

fun Ppg.loadGraphColor(): Int {
    val color = when (this) {
        Ppg.AMBIENT -> {
            com.ruiheng.xmuse.core.ui.R.color.green500
        }

        Ppg.RED -> {
            com.ruiheng.xmuse.core.ui.R.color.red500
        }

        else -> {
            com.ruiheng.xmuse.core.ui.R.color.color3
        }
    }
    return ContextCompat.getColor(Utils.getApp(), color)
}

fun BusymindData.loadGraphColor(): Int {
    val color = when (this) {
        BusymindData.THETA -> {
            com.ruiheng.xmuse.core.ui.R.color.color1
        }

        BusymindData.ALPHA -> {
            com.ruiheng.xmuse.core.ui.R.color.color2
        }

        BusymindData.BETA -> {
            com.ruiheng.xmuse.core.ui.R.color.color3
        }

        BusymindData.GAMMA -> {
            com.ruiheng.xmuse.core.ui.R.color.color4
        }

        BusymindData.DELTA -> {
            com.ruiheng.xmuse.core.ui.R.color.color5
        }

        BusymindData.BODY -> {
            com.ruiheng.xmuse.core.ui.R.color.color3
        }

        else -> {
            com.ruiheng.xmuse.core.ui.R.color.color5
        }
    }
    return ContextCompat.getColor(Utils.getApp(), color)
}
