package com.ruiheng.xmuse.ui.web.model

/**
 * Web命令常量定义
 * 统一管理所有Web相关的命令常量
 */
object WebCommands {
    
    // 基础命令键
    const val COMMAND_KEY = "command"
    
    // Token相关命令
    const val REQUEST_TOKEN = "requestUserToken"
    const val USER_TOKEN = "userToken"
    const val USER_TOKEN_RESULT = "userTokenResult"
    
    // 测试命令
    const val TEST_COMMAND = "test"
    const val TEST_RESULT = "testResult"
    
    // 缓存操作命令
    const val SAVE_CACHE_DATA = "saveCacheData"
    const val GET_CACHE_DATA = "getCacheData"
    const val DELETE_CACHE_DATA = "deleteCacheData"
    const val SAVE_CACHE_DATA_RESULT = "saveCacheDataResult"
    const val GET_CACHE_DATA_RESULT = "getCacheDataResult"
    const val DELETE_CACHE_DATA_RESULT = "deleteCacheDataResult"
    
    // 特殊命令
    const val NFB_PACKET = "nfbpacket"
    
    // 错误命令
    const val ERROR_COMMAND = "error"
    
    // 响应字段
    const val SUCCESS_FIELD = "success"
    const val MESSAGE_FIELD = "message"
    const val TOKEN_FIELD = "token"
    const val KEY_FIELD = "key"
    const val DATA_FIELD = "data"
    const val TIMESTAMP_FIELD = "timestamp"
    const val DEBUG_FIELD = "debug"
}

/**
 * Web命令响应构建器
 * 提供统一的响应格式构建方法
 */
object WebResponseBuilder {
    
    /**
     * 构建成功响应
     */
    fun buildSuccessResponse(
        command: String,
        data: Map<String, Any?> = emptyMap()
    ): Map<String, Any?> {
        return mutableMapOf<String, Any?>(
            WebCommands.COMMAND_KEY to command,
            WebCommands.SUCCESS_FIELD to true
        ).apply {
            putAll(data)
        }
    }
    
    /**
     * 构建错误响应
     */
    fun buildErrorResponse(message: String): Map<String, Any?> {
        return mapOf(
            WebCommands.COMMAND_KEY to WebCommands.ERROR_COMMAND,
            WebCommands.SUCCESS_FIELD to false,
            WebCommands.MESSAGE_FIELD to message
        )
    }
    
    /**
     * 构建Token响应
     */
    fun buildTokenResponse(token: String): Map<String, Any?> {
        return buildSuccessResponse(
            WebCommands.USER_TOKEN_RESULT,
            mapOf(
                WebCommands.TOKEN_FIELD to token,
                WebCommands.MESSAGE_FIELD to "Token获取成功"
            )
        )
    }
    
    /**
     * 构建测试响应
     */
    fun buildTestResponse(message: String): Map<String, Any?> {
        return buildSuccessResponse(
            WebCommands.TEST_RESULT,
            mapOf(
                WebCommands.MESSAGE_FIELD to "Android收到消息: $message",
                WebCommands.TIMESTAMP_FIELD to System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 构建缓存操作响应
     */
    fun buildCacheResponse(
        command: String,
        success: Boolean,
        key: String,
        message: String,
        data: String? = null,
        debug: Map<String, Any?> = emptyMap()
    ): Map<String, Any?> {
        return mutableMapOf<String, Any?>(
            WebCommands.COMMAND_KEY to command,
            WebCommands.SUCCESS_FIELD to success,
            WebCommands.KEY_FIELD to key,
            WebCommands.MESSAGE_FIELD to message
        ).apply {
            data?.let { put(WebCommands.DATA_FIELD, it) }
            if (debug.isNotEmpty()) {
                put(WebCommands.DEBUG_FIELD, debug)
            }
        }
    }
}
