package com.ruiheng.xmuse.ui.widget.pop

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.PopupWindow
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LiveData
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.choosemuse.libmuse.Muse
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.common.result.bluetooth.isBluetoothEnabled
import com.ruiheng.xmuse.core.common.result.bluetooth.openBluetoothSettings
import com.ruiheng.xmuse.core.common.result.findActivity
import com.ruiheng.xmuse.core.common.result.isLoading
import com.ruiheng.xmuse.core.common.result.permission.requestDeviceBlePermission
import com.ruiheng.xmuse.core.ui.custom.PublicOptionNormalTextItemDecoration
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.FragmentAutoClearedValue
import com.ruiheng.xmuse.core.ui.ui.autoCleared
import com.ruiheng.xmuse.core.ui.ui.autoRemoveLiveData
import com.ruiheng.xmuse.databinding.DialogMuseListBinding
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.R
import com.ruiheng.xmuse.feature.device.ui.BottomMuseConnectDialog
import com.ruiheng.xmuse.feature.device.ui.adapter.BottomMuseAdapter
import com.ruiheng.xmuse.ui.home.helper.BPMuseListAdapter
import timber.log.Timber
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

@SuppressLint("MissingPermission")
class BPBluetoothConnectPopover(
    private val context: Activity,
    private val anchorView: View,
    private val onDismissListener: () -> Unit
) : PopupWindow(context, null, 0) {

    var isCancelable = true
    private val popupLifecycleOwner = PopupLifecycleOwner()
    private lateinit var binding: DialogMuseListBinding
    private var mueseAdapter: BPMuseListAdapter?
    private val rotateAnimator: ObjectAnimator by lazy {
        ObjectAnimator.ofFloat(binding.imgBtnScan, "rotation", 0f, 360f).apply {
            duration = 2000 // 设置动画持续时间为 2 秒
            repeatCount = ObjectAnimator.INFINITE // 设置动画无限循环
        }
    }
    private val connectViewModel: BluetoothConnectionViewModel? by lazy {
        try {
            // 获取当前 Activity
            val activity =
                context.findActivity() ?: throw IllegalStateException("Activity not found")
            // 通过 Activity 获取 ViewModel
            ViewModelProvider(activity).get(BluetoothConnectionViewModel::class.java)
        } catch (e: Exception) {
            null
        }
    }

    init {
        setOnDismissListener {
            mueseAdapter = null
            rotateAnimator.apply {
                cancel()
                removeAllListeners()
            }
            onDismissListener.invoke()
            popupLifecycleOwner.onDestroy()
        }
        binding = DialogMuseListBinding.inflate(context.layoutInflater)
        contentView = binding.root
        binding.layoutScanning.visibility = View.VISIBLE
        binding.tvLoading.text = "设备搜索中..."
        binding.layoutMuseList.visibility = View.GONE
        mueseAdapter = BPMuseListAdapter { muse ->
            startConnect(muse)
        }

        binding.rvMenu.adapter = mueseAdapter
        binding.rvMenu.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
//        binding.rvMenu.isNestedScrollingEnabled = false
//        binding.rvMenu.addItemDecoration(PublicOptionNormalTextItemDecoration())


        binding.imgBtn.showImage(R.drawable.ico_bt_scan_01)
        binding.imgBtnScan.showImage(R.drawable.ico_bt_scan_02)
        rotateAnimator.start()
        context.requestDeviceBlePermission {
            if (!context.isBluetoothEnabled()) {
                context.openBluetoothSettings()
            } else {
                connectViewModel?.turnOnDiscovery()
            }
        }
    }

    private var connectLiveData: LiveData<Result<Muse?>>? = null
    private fun startConnect(muse: Muse) {
        connectViewModel?.allMusesDiscovered?.removeObservers(popupLifecycleOwner)
        connectLiveData?.removeObservers(popupLifecycleOwner)
        binding.layoutMuseList.visibility = View.GONE
        binding.loading.visibility = View.GONE
        connectLiveData = connectViewModel?.createConnectionAttempt(muse)
        popupLifecycleOwner.autoRemoveLiveData(connectLiveData!!) { result ->
            binding.layoutScanning.visibility = View.VISIBLE
            binding.tvLoading.text = "设备连接中...."
            isCancelable = !result.isLoading()
            if (!result.isLoading()) {
                if (result.isSuccessWithData()) {
                    connectViewModel?.enableMuseUsage()
                    dismiss()
                } else {
//                    binding.layoutScanning.visibility = View.GONE
//                    binding.layoutErrorContainer.visibility = View.VISIBLE
                }
            }
        }
    }

    fun show() {
        popupLifecycleOwner.onCreate()
        popupLifecycleOwner.onStart()
        popupLifecycleOwner.onResume()
        showAsDropDown(anchorView, ((-180).px).toInt(), (16.px).toInt())
        startObservingLiveData()
    }

    private fun startObservingLiveData() {
        binding.loading.visibility = View.VISIBLE
        connectViewModel?.allMusesDiscovered?.observe(popupLifecycleOwner) { result ->
            val data = result.data
            mueseAdapter?.submitList(data)
            binding.layoutScanning.visibility =
                if (data.isNullOrEmpty()) View.VISIBLE else View.GONE
            binding.loading.visibility = if (data.isNullOrEmpty()) View.GONE else View.VISIBLE
            binding.layoutMuseList.visibility =
                if (data.isNullOrEmpty()) View.GONE else View.VISIBLE
        }
    }
}

