package com.ruiheng.xmuse.ui.detection.busymind

import com.choosemuse.libmuse.MuseDataPacketType
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

@ActivityRetainedScoped
class BPDetectionDataTracker @Inject constructor(
    private val busymindDataMonitor: BPDetectionBusymindDataMonitor,
    private val museDataObservableFactory: MuseDataObservableFactory,
    private val museConnector: MuseConnector
) {

    companion object {

        val VISUALIZATION_BUSYMIND_DATA = BusymindData.entries.filter {
            when (it) {
                BusymindData.ALPHA, BusymindData.BETA, BusymindData.DELTA,
                BusymindData.GAMMA, BusymindData.THETA,
                BusymindData.HEART,
                BusymindData.PPG_HRV_PNN50, BusymindData.PPG_HRV_LF, BusymindData.PPG_HRV_HF,
                BusymindData.PPG_HRV_LFvsHF, BusymindData.PPG_HRV_SDNN, BusymindData.PPG_HRV_RMSSD,
                BusymindData.BODY -> true

                BusymindData.HBO_LEFT, BusymindData.HBO_RIGHT, BusymindData.HBR_LEFT, BusymindData.HBR_RIGHT,
                BusymindData.MIND, BusymindData.BREATH, BusymindData.FOX_SCORE -> false
            }
        }

    }

    private val busymindTimeseriesDataTrackers =
        VISUALIZATION_BUSYMIND_DATA.associateWith { busymindData ->
            BPBusymindTimeseriesDataTracker()
        }

//    private val museDataTimeseriesDataTracker =
//        VISUALIZATION_MUSE_DATA.associateWith { museDataPacketType ->
//            BPMuseDataTimeseriesDataTracker()
//        }

    private var tracking = false

    fun startTracking() {
        if (tracking) {
            return
        }

        tracking = true

        busymindTimeseriesDataTrackers.forEach { (busymindData, tracker) ->
            tracker.startTracking(
                busymindDataMonitor.busymindData
                    .filter { it.containsKey(busymindData) }
                    .map { it[busymindData]!! }
            )
        }
//        val muse = museConnector.getCurrentMuse()?:return
//        museDataTimeseriesDataTracker.forEach { (museDataPacketType, tracker) ->
//            tracker.startTracking(
//                museDataObservableFactory.createDataObservable(muse ,museDataPacketType).map {
//
//                }
//            )
//        }
    }

    fun stopTracking() {
        busymindTimeseriesDataTrackers.forEach { it.value.stopTracking() }
        tracking = false
    }

    fun loadLastValueMap() = VISUALIZATION_BUSYMIND_DATA.associateWith { busymindData ->
        busymindTimeseriesDataTrackers[busymindData]?.processValue() ?: 0.0
    }

    fun startNewDataGap() {
        busymindDataMonitor.setGapEnabled(true)
    }

    fun endDataGap() {
        busymindDataMonitor.setGapEnabled(false)
    }

}
