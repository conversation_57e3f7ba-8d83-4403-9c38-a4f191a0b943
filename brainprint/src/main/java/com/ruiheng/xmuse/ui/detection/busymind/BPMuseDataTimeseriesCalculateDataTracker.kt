package com.ruiheng.xmuse.ui.detection.busymind

import com.choosemuse.libmuse.Eeg
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import com.ruiheng.xmuse.calculate.MuseCalculateDataType
import com.ruiheng.xmuse.calculate.XmuseEEGCalculateUtil
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import timber.log.Timber

class BPMuseDataTimeseriesCalculateDataTracker() {


    private val eegCalculateUtil = XmuseEEGCalculateUtil()

    private val lock = Object()

    //    private val inputBuffer = ArrayList<Double>()
    private var disposeable: CompositeDisposable = CompositeDisposable()

    private var lastBufferMap: Map<MuseDataPacketType, DoubleArray>? = null

    fun startTracking(
        inputDataMap: Map<MuseDataPacketType, Observable<MuseDataPacket>>
    ) {
        val lastValueMap = mutableMapOf<MuseDataPacketType, DoubleArray>()
        inputDataMap.map { keyValue ->
            lastValueMap[keyValue.key] = doubleArrayOf()
        }
        lastBufferMap = lastValueMap
        inputDataMap.forEach { dataType, observable ->
            observable.map { museDataPakcet ->
                var result = doubleArrayOf()
                if (museDataPakcet.packetType() == dataType) {
                    result = arrayOf(
                        museDataPakcet.getEegChannelValue(Eeg.EEG1),
                        museDataPakcet.getEegChannelValue(Eeg.EEG2),
                        museDataPakcet.getEegChannelValue(Eeg.EEG3),
                        museDataPakcet.getEegChannelValue(Eeg.EEG4)
                    ).toDoubleArray()
                }
                result
            }.subscribe { arrayResult ->
                synchronized(lock) {
                    lastValueMap[dataType] = arrayResult
                }
            }.let { disposeable.add(it) }
        }
    }

    fun stopTracking() {
        disposeable.dispose()
    }

    private val electrodeNum = 4
    fun processValue(): Map<MuseCalculateDataType, Double>? {
        val alphaArray = lastBufferMap?.get(MuseDataPacketType.ALPHA_RELATIVE) ?: return null
        val betaArray = lastBufferMap?.get(MuseDataPacketType.BETA_RELATIVE) ?: return null
        val gammaArray = lastBufferMap?.get(MuseDataPacketType.GAMMA_RELATIVE) ?: return null
        val thetaArray = lastBufferMap?.get(MuseDataPacketType.THETA_RELATIVE) ?: return null
        val deltaArray = lastBufferMap?.get(MuseDataPacketType.DELTA_RELATIVE) ?: return null
        val hsiArray = lastBufferMap?.get(MuseDataPacketType.HSI_PRECISION) ?: return null
        //专注力
        val attention = eegCalculateUtil.attentionMethod(
            alphaArray,
            betaArray,
            gammaArray,
            thetaArray,
            deltaArray,
            hsiArray,
            electrodeNum
        )[0]
        //压力
        val stress = eegCalculateUtil.stressMethod(
            alphaArray,
            betaArray,
            gammaArray,
            thetaArray,
            deltaArray,
            hsiArray,
            electrodeNum
        )[0]
        //稳定
        val stability = eegCalculateUtil.stabilityMethod(
            alphaArray,
            betaArray,
            gammaArray,
            thetaArray,
            deltaArray,
            hsiArray,
            electrodeNum
        )[0]
        val cognition = eegCalculateUtil.cognitionMethod(
            alphaArray,
            betaArray,
            gammaArray,
            thetaArray,
            deltaArray,
            hsiArray,
            electrodeNum
        )[0]

        val anxiety = eegCalculateUtil.anxietyMethod(
            alphaArray,
            betaArray,
            gammaArray,
            thetaArray,
            deltaArray,
            hsiArray,
            electrodeNum
        )[0]
        Timber.d("processValue-Attention:${attention}::${stress}::${stability}:${cognition}:${anxiety}")
        return MuseCalculateDataType.entries.associateWith { key ->
            when (key) {
                MuseCalculateDataType.ATTENTION -> attention
                MuseCalculateDataType.STRESS -> stress
                MuseCalculateDataType.COGNITION -> cognition
                MuseCalculateDataType.STABILITY -> stability
                MuseCalculateDataType.ANXIETY -> anxiety
            }
        }
    }

}