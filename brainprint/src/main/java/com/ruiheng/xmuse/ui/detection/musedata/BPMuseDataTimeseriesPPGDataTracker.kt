package com.ruiheng.xmuse.ui.detection.musedata

import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable

class BPMuseDataTimeseriesPPGDataTracker() {
    companion object {

    }
    private val lock = Object()

    //    private val inputBuffer = ArrayList<Double>()
    private var disposeable: CompositeDisposable = CompositeDisposable()
    private var lastBuffer: Double = 0.0

    //    private var lastBuffer: DoubleArray = doubleArrayOf()
    fun startTracking(inputData: Observable<MuseDataPacket>) {
        inputData
            .map { museDataPakcet ->
                var redValue: Double = 0.0
                if (museDataPakcet.packetType() == MuseDataPacketType.PPG) {
                    redValue = museDataPakcet.getPpgChannelValue(Ppg.RED)
                }
                redValue
            }
//          return processPpgStream(inputDouble)

            .subscribe { redValue ->
                synchronized(lock) {
                    if (redValue != 0.0) {
                        lastBuffer = redValue
//                        lastBuffer.add(redValue)
                    }
                }
            }.let { disposeable.add(it) }
    }

    fun stopTracking() {
        disposeable.dispose()
    }

    fun processValue() = lastBuffer

}