package com.ruiheng.xmuse.ui.web

import com.ruiheng.xmuse.core.ui.ui.web.*
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.webkit.ConsoleMessage
import android.webkit.JavascriptInterface
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ResourceUtils
import com.blankj.utilcode.util.ScreenUtils
import com.google.gson.Gson
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.ui.R as CoreR
import com.ruiheng.xmuse.R
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.databinding.BpActivityAgentWebBinding
import com.ruiheng.xmuse.ui.home.helper.BPHasSqcViewPage
import timber.log.Timber
import dagger.hilt.android.AndroidEntryPoint
import me.jessyan.autosize.internal.CustomAdapt
import kotlinx.coroutines.launch

@AndroidEntryPoint
class BPWebActivity : BaseBindingActivity<BpActivityAgentWebBinding>(), CustomAdapt,
    BPHasSqcViewPage {

    override fun bindDataBindingView(): BpActivityAgentWebBinding =
        BpActivityAgentWebBinding.inflate(layoutInflater)

    // ViewModel
    private val viewModel: BPWebViewModel by viewModels()

    companion object {
        private const val KEY_WEB_URL = "webUrl"
        private const val KEY_USER_TOKEN = "userToken"
        private const val KEY_COMMAND_STRING = "commandString"

        fun start(context: Context, url: String, userToken: String) {
            val intent = Intent(context, BPWebActivity::class.java)
            intent.putExtra(KEY_WEB_URL, url)
            intent.putExtra(KEY_USER_TOKEN, userToken)
            context.startActivity(intent)
        }

        fun start(
            context: Context,
            url: String,
            reportData: Map<BusymindData, MutableList<Double>>
        ) {
            val intent = Intent(context, BPWebActivity::class.java)
            val commandString = createBPBusymindReportCommand(reportData)
            intent.putExtra(KEY_WEB_URL, url)
            intent.putExtra(KEY_COMMAND_STRING, commandString)
            context.startActivity(intent)
        }
    }

    private var checkMuseDialogVisible: ((Boolean) -> Boolean)? = null

    override fun initData(bundle: Bundle?) {
        super.initData(bundle)
        val webUrl = bundle?.getString(KEY_WEB_URL, "https://www.xmuse.cn/")
        val userToken = bundle?.getString(KEY_USER_TOKEN, "")
        val commandString = bundle?.getString(KEY_COMMAND_STRING, "")

        // 初始化ViewModel数据
        viewModel.initData(webUrl, userToken, commandString)
    }

    private var mAgentWeb: com.just.agentweb.AgentWeb? = null

    private val mWebChromeClient = object : com.just.agentweb.WebChromeClient() {

        override fun onConsoleMessage(p0: ConsoleMessage?): Boolean {
            Timber.d("LogFromWeb:${p0?.message()?.toString()}")
            if (p0?.message()?.toString()?.contains("打印错误") == true) {
                Timber.d("LogFromWeb:${p0?.message()?.toString()}")
            }
            return super.onConsoleMessage(p0)
        }

        override fun onReceivedTitle(view: WebView?, title: String?) {
            super.onReceivedTitle(view, title)
            // 通过ViewModel处理标题设置
            viewModel.setPageTitle(title)
        }
    }

    private val mWebViewClient = object : com.just.agentweb.WebViewClient() {

        override fun onPageStarted(webView: WebView?, p1: String?, p2: Bitmap?) {
            super.onPageStarted(webView, p1, p2)
            viewModel.onPageStarted()
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            viewModel.onPageFinished()
            startSendToken()
        }

        private var lastUrl: Uri? = null
        override fun shouldOverrideUrlLoading(
            view: WebView?,
            request: WebResourceRequest?
        ): Boolean {
            return super.shouldOverrideUrlLoading(view, request)
        }
    }

    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)

        // 设置UI
        setupUI()

        // 初始化WebView
        initWebView()

        // 设置观察者
        setupObservers()

        // 初始化SQC
        initSqcLayout()
    }

    private fun setupUI() {
        binding.imgBpHomeTitleLogo.showImage(com.ruiheng.xmuse.R.drawable.pic_bp_xmuse_logo01)
        ScreenUtils.setFullScreen(this)
        BarUtils.setStatusBarVisibility(this, false)
    }

    private fun initWebView() {
        mAgentWeb = com.just.agentweb.AgentWeb.with(this)
            .setAgentWebParent(
                binding.container,
                LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)
            )
            .useDefaultIndicator(
                ContextCompat.getColor(
                    this,
                    CoreR.color.colorAccent
                )
            )
            .setWebChromeClient(mWebChromeClient)
            .setWebViewClient(mWebViewClient)
            .setSecurityType(com.just.agentweb.AgentWeb.SecurityType.STRICT_CHECK)
            .setWebLayout(WebLayout(this))
            .setOpenOtherPageWays(com.just.agentweb.DefaultWebClient.OpenOtherPageWays.ASK)
            .interceptUnkownUrl()
            .createAgentWeb()
            .ready()
            .go(viewModel.getActualWebUrl())

        // 配置WebView设置
        configureWebView()

        // 添加JavaScript接口
        mAgentWeb?.jsInterfaceHolder?.addJavaObject("xmuseAndroid", ProgramJavascriptInterface())
    }

    private fun configureWebView() {
        val webView = mAgentWeb?.getWebCreator()?.getWebView()
        val webSettings = webView?.settings
        webSettings?.apply {
            setCacheMode(WebSettings.LOAD_NO_CACHE)
            javaScriptEnabled = true
            textZoom = 100
            useWideViewPort = true
            loadWithOverviewMode = true

            // 字体大小相关设置
            defaultFontSize = 16       // 默认字体大小 (默认16)
            defaultFixedFontSize = 13   // 默认等宽字体大小 (默认13)
            minimumFontSize = 10         // 最小字体大小 (默认8)
            minimumLogicalFontSize = 8  // 最小逻辑字体大小 (默认8)

            // 缩放相关设置
            setSupportZoom(true)        // 支持缩放
            builtInZoomControls = true  // 启用内置缩放控件
            displayZoomControls = false // 隐藏缩放控件按钮

            // 其他影响显示的设置
            layoutAlgorithm = WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING // 自动调整文本大小
        }

        // 禁止长按菜单
        webView?.setOnLongClickListener { true }
    }

    private fun setupObservers() {
        // 观察页面标题变化
        viewModel.pageTitle.observe(this) { title ->
            setToolbarTitle(title)
        }

        // 观察JavaScript调用结果
        viewModel.jsCallResult.observe(this) { jsCommand ->
            jsCommand?.let {
                mAgentWeb?.jsAccessEntrace?.callJs(it)
            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { error ->
            error?.let {
                Timber.e("Web页面错误: $it")
                // 这里可以显示错误提示
            }
        }
    }

    private fun initSqcLayout() {
        checkMuseDialogVisible =
            initSqc(this, binding.sqcLayout, museListDialogCallback = { isVisible ->
                // 处理Muse列表对话框可见性变化
            })
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (mAgentWeb?.handleKeyEvent(keyCode, event) == true) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onPause() {
        mAgentWeb?.webLifeCycle?.onPause()
        super.onPause()
    }

    override fun onResume() {
        mAgentWeb?.webLifeCycle?.onResume()
        super.onResume()
    }

    override fun onDestroy() {
        mAgentWeb?.clearWebCache()
        mAgentWeb?.webLifeCycle?.onDestroy()
        super.onDestroy()
    }

    private fun startSendToken() {
        val tokenCommand = viewModel.getUserTokenCommand()
        mAgentWeb?.jsAccessEntrace?.callJs("window.fromNative($tokenCommand)")
    }

    inner class ProgramJavascriptInterface {
        @JavascriptInterface
        fun processWebMsg(jsonString: String): String? {
            return viewModel.handleWebCommand(jsonString)
        }
    }

    override fun isBaseOnWidth(): Boolean = true

    override fun getSizeInDp() = 960f

    override fun bindNavigationSpaceView() = binding.viewNavigationSpace

    /**
     * 重写getLogoResourceId方法，直接返回brainprint模块中的logo资源ID
     */
    override fun getLogoResourceId(): Int? {
        return R.id.img_bp_home_title_logo
    }
}