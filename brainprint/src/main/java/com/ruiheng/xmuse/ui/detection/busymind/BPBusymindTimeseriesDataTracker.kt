package com.ruiheng.xmuse.ui.detection.busymind

import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable

class BPBusymindTimeseriesDataTracker() {

    private val lock = Object()

    //    private val inputBuffer = ArrayList<Double>()
    private var disposeable: Disposable? = null
    private var lastBuffer: Double = 0.0
    fun startTracking(inputData: Observable<Double>) {
        inputData
            .map {
                if (it.isNaN()) {
                    -1.0
                } else {
                    it
                }
            }
            .subscribe {
                synchronized(lock) {
                    lastBuffer = it
                }
            }.let { disposeable = it }
    }

    fun stopTracking() {
        disposeable?.dispose()
        disposeable = null
    }

    fun processValue() = lastBuffer

}