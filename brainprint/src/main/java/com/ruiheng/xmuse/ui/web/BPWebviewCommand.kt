package com.ruiheng.xmuse.ui.web

import com.blankj.utilcode.util.BusUtils.Bus
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.ui.ui.web.WebviewCommand

data class BPWebCommandUser(
    val command: String = "userToken",
    val token: String
) : WebviewCommand

fun createBPBusymindReportCommand(busymindDataMap: Map<BusymindData, MutableList<Double>>): String {
    val parent = JsonObject()
    parent.addProperty("command", "nfbpacket")
    val jsonObject = JsonObject()
    val gson = Gson()
    busymindDataMap.forEach { key, value ->
        val valueList = value.map(Double::toFloat)
        jsonObject.add(key.jsonName(), gson.toJsonTree(valueList))
    }
    parent.add("nfbpacket", jsonObject)
    return parent.toString()
}

fun BusymindData.jsonName() = when (this) {
    BusymindData.ALPHA -> "bandAvgPowsRawAlpha"
    BusymindData.BETA -> "bandAvgPowsRawAlpha"
    BusymindData.THETA -> "bandAvgPowsRawTheta"
    BusymindData.GAMMA -> "bandAvgPowsRawGamma"
    BusymindData.DELTA -> "bandAvgPowsRawDelta"
    BusymindData.BODY -> "busybody"
    BusymindData.HEART -> "heartRate"
    BusymindData.PPG_HRV_HF -> "ppgHf"
    BusymindData.PPG_HRV_SDNN -> "ppgSdnn"
    BusymindData.PPG_HRV_RMSSD -> "ppgRmssd"
    BusymindData.PPG_HRV_LF -> "ppgLf"
    BusymindData.PPG_HRV_PNN50 -> "ppgPnn50"
    BusymindData.PPG_HRV_LFvsHF -> "ppgLfVsHf"
    else -> ""
}