package com.ruiheng.xmuse.ui.home.helper

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import com.blankj.utilcode.util.SpanUtils
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.privacyPolicy
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule.userProtocol
import com.ruiheng.xmuse.core.ui.helper.dialog.FullScreenDialogFragment
import com.ruiheng.xmuse.core.ui.helper.getColorOnSurface
import com.ruiheng.xmuse.core.ui.helper.getColorPrimary
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.web.AgentWebActivity
import com.ruiheng.xmuse.databinding.DialogPersonInfoBinding
import com.ruiheng.xmuse.feature.login.R
import com.ruiheng.xmuse.feature.login.databinding.DialogAgreementConfirmBinding

class BPPersonPageDialog(private val user: UserData,private val onSureClick: () -> Unit) :
    FullScreenDialogFragment<DialogPersonInfoBinding>() {
    override fun bindDataBindingView(layoutInflater: LayoutInflater) =
        DialogPersonInfoBinding.inflate(layoutInflater)


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        binding.tvPersonName.text = user.nickName
        binding.imgPersonAvatar.showImage(user.headPortrait, placeHolder = com.ruiheng.xmuse.core.ui.R.drawable.ico_avatar_default)
        binding.btnSure.setOnClickListener {
            dismiss()
            onSureClick.invoke()
        }
        binding.root.setOnClickListener{
            dismiss()
        }
        isCancelable = true
        return dialog
    }
}