package com.ruiheng.xmuse.ui.detection

import android.annotation.SuppressLint
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.choosemuse.libmuse.MuseDataPacket
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import com.choosemuse.libmuse.internal.BusymindMode
import com.ruiheng.xmuse.calculate.MuseCalculateDataType
import com.ruiheng.xmuse.core.common.result.PetivityThrowable
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.BusymindMonitor
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseDataObservableFactory
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesBPSessionRepository
import com.ruiheng.xmuse.core.network.bp.ApiRequestBPSessionInfo
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel.Companion
import com.ruiheng.xmuse.ui.detection.busymind.BPDetectionDataTracker
import com.ruiheng.xmuse.ui.detection.busymind.BPDetectionDataTracker.Companion.VISUALIZATION_BUSYMIND_DATA
import com.ruiheng.xmuse.ui.detection.busymind.BPMuseDataTimeseriesCalculateDataTracker
import com.ruiheng.xmuse.ui.detection.musedata.BPMuseDataTimeseriesPPGDataTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class BPDetectionViewModel @Inject constructor(
    val app: Application,
    private val bpDetectionDataTracker: BPDetectionDataTracker,
    private val busymindMonitor: BusymindMonitor,
    private val museDataObservableFactory: MuseDataObservableFactory,
    private val museConnector: MuseConnector,
    private val netResourcesBPSessionRepository: NetResourcesBPSessionRepository
) : AndroidViewModel(app) {

    companion object {
        private const val PeriodTime = 120L
        val BAND_POWER_GRAPH = arrayOf(
            BusymindData.DELTA,
            BusymindData.ALPHA,
            BusymindData.BETA,
            BusymindData.GAMMA,
            BusymindData.THETA
        )

        val CALCULATE_MUSE_DATA = arrayOf(
            MuseDataPacketType.ALPHA_RELATIVE,
            MuseDataPacketType.BETA_RELATIVE,
            MuseDataPacketType.GAMMA_RELATIVE,
            MuseDataPacketType.THETA_RELATIVE,
            MuseDataPacketType.DELTA_RELATIVE,
            MuseDataPacketType.HSI_PRECISION
        )
    }

    private var disposable = CompositeDisposable()
    private val frameRateEpochMillisClock = Observable.interval(
        PeriodTime,
        TimeUnit.MILLISECONDS
    )

    //
    private val rxCountdownUtil = RxCountdownUtil()

    private val _completedLiveData = MutableLiveData(false)
    val completedLiveData = _completedLiveData as LiveData<Boolean>

    private val _remainingTimeLiveData = MutableLiveData("00:00")
    val remainingTimeLiveData = _remainingTimeLiveData as LiveData<String>

    val cacheBusyMindData: Map<BusymindData, MutableList<Double>> =
        VISUALIZATION_BUSYMIND_DATA.associateWith {
            mutableListOf()
        }

    val cacheMuseCalculateData: Map<MuseCalculateDataType, MutableList<Double>> =
        MuseCalculateDataType.entries.associateWith {
            mutableListOf()
        }

    private val ppgDataTracker: BPMuseDataTimeseriesPPGDataTracker =
        BPMuseDataTimeseriesPPGDataTracker()

    private val calculateDataTracker: BPMuseDataTimeseriesCalculateDataTracker =
        BPMuseDataTimeseriesCalculateDataTracker()

    /**
     * 衍生值数据
     * 心率、体动、HRV系列数值 -- 用于图表更新
     */
    private val _latestBusymindValueMapLiveData =
        MutableLiveData(bpDetectionDataTracker.loadLastValueMap())
    val latestBusymindValueMapLiveData =
        _latestBusymindValueMapLiveData as LiveData<Map<BusymindData, Double>>

    /**
     * PPG 原始数据 --用于图表更新
     */
    private var lastPPGValueMap = 0.0
    private val _latestPPGValueMapLiveData =
        MutableLiveData(lastPPGValueMap)
    val latestPPGValueMapLiveData = _latestPPGValueMapLiveData as LiveData<Double>

    private var lastCalculateDataMap: Map<MuseCalculateDataType, Double>? = null
    private var _lastCalculateDataMapLiveData = MutableLiveData(lastCalculateDataMap)
    val lastCalculateDataMapLiveData =
        _lastCalculateDataMapLiveData as LiveData<Map<MuseCalculateDataType, Double>?>

    private val _busymindModeLiveData = MutableLiveData(BusymindMode.IDLE)
    val busymindModeLiveData = _busymindModeLiveData as LiveData<BusymindMode>

    private var busymindModeDisposable: Disposable? = null

    private var sessionInfo: ApiRequestBPSessionInfo? = null

    fun startSession() =
        netResourcesBPSessionRepository.startSession().stateIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            initialValue = Result.Loading()
        )

    fun endSession(): LiveData<Result<Any?>>? {
        val resultMutableLiveData = MutableLiveData<Result<Any?>>()
        val sessionId = sessionInfo?.id ?: return null
        val startTime = sessionInfo?.startTime ?: return null
        netResourcesBPSessionRepository.startRxPushSessionProgress(
            sessionId,
            startTime,
            cacheBusyMindData
        ).subscribeOn(Schedulers.io())
            .flatMap { pushResult ->
                if (pushResult.isSuccessWithData()) {
                    netResourcesBPSessionRepository.rxEndSession(sessionId)
                } else if (pushResult.isError()) {
                    Observable.error((pushResult as Result.Error).exception)
                } else {
                    Observable.error(PetivityThrowable())
                }
            }.observeOn(AndroidSchedulers.mainThread()).subscribe({ endResult ->
                if (endResult.isSuccessWithData()) {
                    resultMutableLiveData.value = Result.Success(true)
                } else if (endResult.isError()) {
                    resultMutableLiveData.value =
                        Result.Error((endResult as Result.Error).exception)
                }
            }, {
                resultMutableLiveData.value = Result.Error(it)
            }).let { disposable.add(it) }
        return resultMutableLiveData
    }

    fun startResetCalibration(): LiveData<BusymindMode> {
        busymindMonitor.resetCalibration()
        busymindModeDisposable =
            busymindMonitor.busymindModeObservable
                .subscribe { busymindMode ->
                    _busymindModeLiveData.value = busymindMode
                    if (busymindMode == BusymindMode.FEEDBACK) {
                        busymindModeDisposable?.dispose()
                    }
                }
        return _busymindModeLiveData
    }

    fun startBusymindModeCheckAndDataPicker(apiRequestBPSessionInfo: ApiRequestBPSessionInfo) {
        this.sessionInfo = apiRequestBPSessionInfo
        busymindModeDisposable =
            busymindMonitor.busymindModeObservable
                .subscribe { busymindMode ->
                    _busymindModeLiveData.value = busymindMode
                    if (busymindMode == BusymindMode.FEEDBACK) {
                        busymindModeDisposable?.dispose()
                        startDataPicker()
                    }
                }
    }

    private fun startDataPicker() {
        bpDetectionDataTracker.startTracking()
        frameRateEpochMillisClock
            .map {
                lastCalculateDataMap = calculateDataTracker.processValue()
                lastPPGValueMap = ppgDataTracker.processValue()
                bpDetectionDataTracker.loadLastValueMap()
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { valueMap ->
                _lastCalculateDataMapLiveData.value = lastCalculateDataMap
                _latestPPGValueMapLiveData.value = lastPPGValueMap
                _latestBusymindValueMapLiveData.value = valueMap
            }.let { disposable.add(it) }

        rxCountdownUtil.setListener(object : RxCountdownUtil.Listener {
            @SuppressLint("DefaultLocale")
            override fun onIntervalTick(remainingSeconds: Long) {
                val minutes = remainingSeconds / 60
                val seconds = remainingSeconds % 60
                val timeText = String.format("%02d:%02d", minutes, seconds)
                _remainingTimeLiveData.value = timeText
                cacheLastData()
            }

            override fun onFinish() {
                _completedLiveData.value = true
                bpDetectionDataTracker.stopTracking()
                disposable.clear()
            }
        })
        val muse = museConnector.getCurrentMuse() ?: return
        ppgDataTracker.startTracking(
            museDataObservableFactory.createDataObservable(
                muse,
                MuseDataPacketType.PPG
            )
        )
        val museDataMap = CALCULATE_MUSE_DATA.associateWith {
            museDataObservableFactory.createDataObservable(
                muse,
                it
            )
        }
        calculateDataTracker.startTracking(museDataMap)
        rxCountdownUtil.start()

    }

    private fun cacheLastData() {
        val lastDataMap = _latestBusymindValueMapLiveData.value
        cacheBusyMindData.forEach { busymindData, cacheList ->
            val value: Double? = lastDataMap?.get(busymindData)
            if (value != null) {
                cacheList.add(value)
            }
        }
        val lastMuseCalculateDateMap = _lastCalculateDataMapLiveData.value
        cacheMuseCalculateData.forEach { type, cacheList ->
            val value: Double? = lastMuseCalculateDateMap?.get(type)
            if (value != null) {
                cacheList.add(value)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        bpDetectionDataTracker.stopTracking()
        ppgDataTracker.stopTracking()
        calculateDataTracker.stopTracking()
        rxCountdownUtil.onDestroy()
        disposable.clear()
    }
}

