package com.ruiheng.xmuse.ui.web.repository

import com.blankj.utilcode.util.SPUtils
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Web缓存数据管理Repository
 * 负责处理Web页面的缓存数据存储、获取和删除操作
 */
@Singleton
class WebCacheRepository @Inject constructor() {

    companion object {
        private const val CACHE_PREFIX = "cache_"
    }

    /**
     * 保存缓存数据
     * @param key 缓存键
     * @param data 缓存数据
     * @return 是否保存成功
     */
    fun saveCache(key: String, data: String): Bo<PERSON>an {
        return try {
            val cacheKey = "$CACHE_PREFIX$key"
            SPUtils.getInstance().put(cacheKey, data)
            Timber.d("保存缓存成功: key=$cacheKey, data=$data")
            true
        } catch (e: Exception) {
            Timber.e(e, "保存缓存失败: key=$key")
            false
        }
    }

    /**
     * 获取缓存数据
     * @param key 缓存键
     * @return 缓存数据，如果不存在则返回null
     */
    fun getCache(key: String): String? {
        return try {
            val cacheKey = "$CACHE_PREFIX$key"
            val cachedData = SPUtils.getInstance().getString(cacheKey, null)
            Timber.d("获取缓存: key=$cacheKey, hasData=${cachedData != null}, data=$cachedData")
            cachedData
        } catch (e: Exception) {
            Timber.e(e, "获取缓存失败: key=$key")
            null
        }
    }

    /**
     * 删除缓存数据
     * @param key 缓存键
     * @return 是否删除成功
     */
    fun deleteCache(key: String): Boolean {
        return try {
            val cacheKey = "$CACHE_PREFIX$key"
            
            // 删除前检查数据是否存在
            val existingData = SPUtils.getInstance().getString(cacheKey, null)
            Timber.d("删除缓存前: key=$cacheKey, 存在数据=${existingData != null}")
            
            // 删除缓存数据
            SPUtils.getInstance().remove(cacheKey)
            
            // 删除后再次检查确认删除成功
            val afterDeleteData = SPUtils.getInstance().getString(cacheKey, null)
            val success = afterDeleteData == null
            Timber.d("删除缓存后: key=$cacheKey, 删除成功=$success")
            
            success
        } catch (e: Exception) {
            Timber.e(e, "删除缓存失败: key=$key")
            false
        }
    }

    /**
     * 检查缓存是否存在
     * @param key 缓存键
     * @return 是否存在
     */
    fun hasCache(key: String): Boolean {
        return try {
            val cacheKey = "$CACHE_PREFIX$key"
            val cachedData = SPUtils.getInstance().getString(cacheKey, null)
            cachedData != null
        } catch (e: Exception) {
            Timber.e(e, "检查缓存失败: key=$key")
            false
        }
    }

    /**
     * 清空所有缓存
     */
    fun clearAllCache() {
        try {
            // 这里可以根据需要实现清空所有以CACHE_PREFIX开头的缓存
            Timber.d("清空所有缓存")
        } catch (e: Exception) {
            Timber.e(e, "清空缓存失败")
        }
    }
}
