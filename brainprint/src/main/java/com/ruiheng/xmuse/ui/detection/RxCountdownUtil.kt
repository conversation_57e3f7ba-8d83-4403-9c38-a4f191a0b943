package com.ruiheng.xmuse.ui.detection

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.concurrent.TimeUnit

class RxCountdownUtil {
    // 管理RxJava订阅，防止内存泄漏
    private val compositeDisposable = CompositeDisposable()

    // 保存当前配置参数
    private var currentTotalSeconds: Long = 0
    private var currentIntervalSeconds: Long = 1

    // 定义回调接口
    interface Listener {
        // 每次间隔回调：剩余时长（秒）、当前是第几次回调（从0开始）
        fun onIntervalTick(remainingSeconds: Long)
        // 倒计时结束
        fun onFinish()
    }

    private var listener: Listener? = null

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    /**
     * 启动或重启倒计时
     * @param totalSeconds 总时长（秒）
     * @param intervalSeconds 处理间隔（秒）：每N秒触发一次onIntervalTick
     */
    fun start(totalSeconds: Long = 30, intervalSeconds: Long = 1) {
//    fun start(totalSeconds: Long = 180, intervalSeconds: Long = 1) {
        // 保存当前配置
        currentTotalSeconds = totalSeconds
        currentIntervalSeconds = intervalSeconds

        // 先停止当前倒计时
        stop()

        // 校验参数
        if (totalSeconds <= 0 || intervalSeconds <= 0) {
            throw IllegalArgumentException("总时长和间隔必须大于0")
        }

        // 计算需要发射的次数
        val totalTicks = (totalSeconds + intervalSeconds - 1) / intervalSeconds

        // 生成倒计时序列
        val disposable: Disposable =  Flowable.intervalRange(0,totalTicks,0,intervalSeconds,TimeUnit.SECONDS)
            .map { tickIndex ->
                totalSeconds - (tickIndex * intervalSeconds)
            }.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { remainingSeconds ->
                    listener?.onIntervalTick(remainingSeconds)
                },
                { error ->
                    error.printStackTrace()
                },
                {
                    listener?.onFinish()
                    compositeDisposable.clear()
                }
            )
        compositeDisposable.add(disposable)
    }

    /**
     * 重启倒计时（使用当前配置的总时长和间隔）
     */
    fun restart() {
        if (currentTotalSeconds > 0 && currentIntervalSeconds > 0) {
            start(currentTotalSeconds, currentIntervalSeconds)
        } else {
            throw IllegalStateException("请先调用start()方法初始化倒计时参数")
        }
    }

    /**
     * 停止倒计时
     */
    fun stop() {
        compositeDisposable.clear()
    }

    /**
     * 页面销毁时调用，释放资源
     */
    fun onDestroy() {
        compositeDisposable.dispose()
        listener = null
    }
}