/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.ui.detection

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.choosemuse.libmuse.MuseDataPacketType
import com.choosemuse.libmuse.Ppg
import com.choosemuse.libmuse.internal.BusymindMode
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.formatter.ValueFormatter
import com.google.android.material.chip.Chip
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.ui.helper.dialog.FullScreenDialogFragment
import com.ruiheng.xmuse.core.ui.px
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.core.ui.ui.BaseBindingActivity
import com.ruiheng.xmuse.databinding.ActivityMainBinding
import com.ruiheng.xmuse.databinding.BpActivityDetectionBinding
import com.ruiheng.xmuse.feature.device.BluetoothConnectionViewModel
import com.ruiheng.xmuse.feature.device.R as DeviceR
import com.ruiheng.xmuse.R
import com.ruiheng.xmuse.calculate.MuseCalculateDataType
import com.ruiheng.xmuse.core.ui.helper.controlLoading
import com.ruiheng.xmuse.core.ui.ui.autoRemoveFlow
import com.ruiheng.xmuse.feature.device.SignalQualityViewModel
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository.Companion
import com.ruiheng.xmuse.feature.device.ui.visualization.MuseDataVisualizationRepository.Companion.visualizationViewHeartSize
import com.ruiheng.xmuse.feature.device.ui.visualization.XmuseVisualizationViewViewModel
import com.ruiheng.xmuse.feature.login.LoginViewModel
import com.ruiheng.xmuse.ui.home.HomeFragment
import com.ruiheng.xmuse.ui.home.HomeViewModel
import com.ruiheng.xmuse.ui.home.helper.BPHasSqcViewPage
import com.ruiheng.xmuse.ui.web.BPWebActivity
import dagger.hilt.android.AndroidEntryPoint
import me.jessyan.autosize.internal.CustomAdapt
import timber.log.Timber

@AndroidEntryPoint
class DetectionActivity : BaseBindingActivity<BpActivityDetectionBinding>(), CustomAdapt,
    BPHasSqcViewPage {
    override fun bindDataBindingView() = BpActivityDetectionBinding.inflate(layoutInflater)

    val bluetoothConnectionViewModel: BluetoothConnectionViewModel by viewModels()
    val homeViewModel: HomeViewModel by viewModels()
    val bpDetectionViewModel: BPDetectionViewModel by viewModels()
    val signalQualityViewModel: SignalQualityViewModel by viewModels()

    val MIN_POWER = -1.0f
    val MAX_POWER = 4.0f

    val MIN_PPG = 1800000f
    val MAX_PPG = 2300000f
    private var checkMuseDialogVisible: ((Boolean) -> Boolean)? = null
    override fun initView(saveInsBundle: Bundle?, contentView: View) {
        super.initView(saveInsBundle, contentView)
        ScreenUtils.setFullScreen(this)
        BarUtils.setStatusBarVisibility(this, false)
        binding.imgBpHomeTitleLogo.showImage(com.ruiheng.xmuse.R.drawable.pic_bp_xmuse_logo01)
        initChartGraph(binding.layoutBandPowerContainer.chartPower, MAX_POWER, MIN_POWER)
        initChartGraph(
            binding.layoutPpgGraphContainer.chartPower,
            MAX_PPG,
            MIN_PPG,
            customValueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    return ""
                }
            })
        initBodyGraph(this, binding.layoutBodyGraphContainer.chart)

        initBandPower()
        initPPGChipGroup()
        bpDetectionViewModel.busymindModeLiveData.observe(this) { mode ->
            binding.layoutContentCalibration.root.isVisible = mode == BusymindMode.CALIBRATION
        }

        checkMuseDialogVisible =
            initSqc(this, binding.sqcLayout, museListDialogCallback = { isVisible ->
                binding.viewMuseListMask.isVisible = isVisible
            })
        bpDetectionViewModel.remainingTimeLiveData.observe(this) { time ->
            binding.tvTimeRemaining.text = time
        }

        bpDetectionViewModel.latestBusymindValueMapLiveData.observe(this) { valueMap ->
            val heart = (valueMap[BusymindData.HEART]?.toInt()) ?: 0
            val text =
                if (heart <= 45) getString(DeviceR.string.prepare) else "${heart}"
            val sdnnText = (valueMap[BusymindData.PPG_HRV_SDNN]?.toInt()) ?: 0
            binding.tvSdnn.text = "$sdnnText"
            binding.tvHeartRate.text = text
            updateBandPowerGraph(valueMap.filter { entry ->
                BPDetectionViewModel.BAND_POWER_GRAPH.contains(entry.key)
            })
            updateBodyGraph(valueMap[BusymindData.BODY])
        }

        bpDetectionViewModel.latestPPGValueMapLiveData.observe(this) { ppgValueMap ->
            updatePPGGraph(ppgValueMap)
        }

        bpDetectionViewModel.lastCalculateDataMapLiveData.observe(this) { calculateMap ->
            binding.tvStable.text = "${calculateMap?.get(MuseCalculateDataType.STABILITY)?.toInt() ?: 0}"
            binding.tvStress.text = "${calculateMap?.get(MuseCalculateDataType.STRESS)?.toInt() ?: 0}"
        }

        bpDetectionViewModel.completedLiveData.observe(this) { completed ->
            if (!completed) return@observe
            onCompleted()
        }

        autoRemoveFlow(bpDetectionViewModel.startSession()) { result ->
            result.controlLoading(this)
            result.showResult()
            if (result.isSuccessWithData()) {
                bpDetectionViewModel.startBusymindModeCheckAndDataPicker(result.data!!)
            }
            if (result.isError()) {
//                finish()
            }
        }
    }

    private fun onCompleted() {
        fun loadDataAndJump() {
            bpDetectionViewModel.endSession()?.observe(this) { result ->
                result.controlLoading(this)
                result.showResult()
                if (result.isSuccessWithData()) {
                    val resultData = bpDetectionViewModel.cacheBusyMindData
                    // 使用协程获取配置的报告URL
                    lifecycleScope.launch {
                        val reportUrl = homeViewModel.getReportUrl()
                        Log.v("DetectionActivity", "Report URL: $reportUrl")
                        BPWebActivity.start(this@DetectionActivity, reportUrl, resultData)
                    }
                }
            }

        }
        BPCompletedDialog {
            loadDataAndJump()
        }.startShow(supportFragmentManager)
    }

    private fun initBandPower() {
        binding.layoutBandPowerContainer.apply {
            val chipIdMap = mapOf(
                chipAlpha.id to BusymindData.ALPHA,
                chipBeta.id to BusymindData.BETA,
                chipDelta.id to BusymindData.DELTA,
                chipGamma.id to BusymindData.GAMMA,
                chipTheta.id to BusymindData.THETA
            )
            chipGroup.setOnCheckedStateChangeListener { group, checkedIds ->
                val data = chartPower.data ?: return@setOnCheckedStateChangeListener
                chipIdMap.forEach { entry ->
                    val key = entry.key
                    val value = entry.value
                    val set = data.getDataSetByLabel(value.name, true)
                    set?.isVisible = checkedIds.contains(key)
                }
                chartPower.notifyDataSetChanged()
                chartPower.invalidate()
            }
        }
    }

    private fun initPPGChipGroup() {
        binding.layoutPpgGraphContainer.apply {
            val chipIdMap = mapOf(
                chipAlpha.id to Ppg.AMBIENT,
                chipBeta.id to Ppg.IR,
                chipGamma.id to Ppg.RED,
            )
            chipGroup.setOnCheckedStateChangeListener { group, checkedIds ->
                val data = chartPower.data ?: return@setOnCheckedStateChangeListener
                chipIdMap.forEach { entry ->
                    val key = entry.key
                    val value = entry.value
                    val set = data.getDataSetByLabel(value.name, true)
                    set?.isVisible = checkedIds.contains(key)
                }
                chartPower.notifyDataSetChanged()
                chartPower.invalidate()
            }
        }
    }

    private val visualizationViewSize = 120
    private var count: Int = visualizationViewSize
    private fun updateBandPowerGraph(bandPowerValueMap: Map<BusymindData, Double>) {
        val data = binding.layoutBandPowerContainer.chartPower.data ?: return
        bandPowerValueMap.forEach { key, value ->
            val set = loadSet(
                binding.layoutBandPowerContainer.chartPower,
                visualizationViewSize,
                key.name,
                key.loadGraphColor()
            )
            val entryCount = (set?.entryCount ?: 0)
            if (entryCount > visualizationViewSize) {
                set?.removeFirst()
            }
            set?.addEntry(Entry(count.toFloat(), value.toFloat()))
            data.notifyDataChanged()
        }
        count++
        binding.layoutBandPowerContainer.chartPower.notifyDataSetChanged()
        binding.layoutBandPowerContainer.chartPower.setVisibleXRangeMaximum(visualizationViewSize.toFloat())
        binding.layoutBandPowerContainer.chartPower.moveViewToX((data.entryCount).toFloat())
    }

    private val visualizationBodyViewSize = 60
    private var bodyCount: Int = visualizationBodyViewSize
    private fun updateBodyGraph(value: Double?) {
        if (value == null) return
        val data = binding.layoutBodyGraphContainer.chart.data ?: return
        val busymindData = BusymindData.BODY
        val set = loadSet(
            binding.layoutBodyGraphContainer.chart,
            visualizationBodyViewSize,
            busymindData.name,
            busymindData.loadGraphColor()
        )
        val entryCount = (set?.entryCount ?: 0)
        if (entryCount > visualizationBodyViewSize) {
            set?.removeFirst()
        }
        set?.addEntry(Entry(bodyCount.toFloat(), value.toFloat()))
        data.notifyDataChanged()
        bodyCount++
        binding.layoutBodyGraphContainer.chart.apply {
            notifyDataSetChanged()
            setVisibleXRangeMaximum(visualizationBodyViewSize.toFloat())
            moveViewToX((data.entryCount).toFloat())
        }
    }

    private val ppgViewSize = 64
    private var ppgCount: Int = ppgViewSize
    private fun updatePPGGraph(value: Double?) {
        val data = binding.layoutPpgGraphContainer.chartPower.data ?: return
        val key = Ppg.RED //
        value ?: return
        if (value == 0.0) return
        val set = loadSet(
            binding.layoutPpgGraphContainer.chartPower,
            ppgViewSize,
            key.name,
            key.loadGraphColor()
        )
        val entryCount = (set?.entryCount ?: 0)
        if (entryCount > ppgViewSize) {
            set?.removeFirst()
        }
        set?.addEntry(Entry(ppgCount.toFloat(), value.toFloat()))
        data.notifyDataChanged()
        ppgCount++
        binding.layoutPpgGraphContainer.chartPower.apply {
            notifyDataSetChanged()
            setVisibleXRangeMaximum(ppgViewSize.toFloat())
            moveViewToX((data.entryCount).toFloat())
        }
    }

    override fun isBaseOnWidth(): Boolean = true

    override fun getSizeInDp() = 960f

    /**
     * 重写getLogoResourceId方法，直接返回brainprint模块中的logo资源ID
     */
    override fun getLogoResourceId(): Int? {
        return R.id.img_bp_home_title_logo
    }
}
