package com.ruiheng.xmuse.ui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.ruiheng.xmuse.core.ui.showImage
import com.ruiheng.xmuse.feature.device.ui.widget.sqc.MuseStatusIndicatorView


class BPMuseStatusIndicatorView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : MuseStatusIndicatorView(context, attrs, defStyleAttr){


    override fun onCreate(owner: LifecycleOwner) {
       super<MuseStatusIndicatorView>.onCreate(owner)
        binding.btnBluetoothConnection.showImage(com.ruiheng.xmuse.R.drawable.img_bp_bt)

    }
}