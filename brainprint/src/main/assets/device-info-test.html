<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备信息获取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .button:hover {
            background-color: #0056CC;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007AFF;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .device-info {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-top: 15px;
        }
        .device-info .label {
            font-weight: bold;
            color: #333;
        }
        .device-info .value {
            color: #666;
            word-break: break-all;
        }
        .json-display {
            background-color: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设备信息获取测试</h1>
        <p>点击下面的按钮来测试获取设备信息的功能：</p>
        
        <button class="button" onclick="getDeviceInfo()">获取设备信息</button>
        <button class="button" onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        // 调用Android原生方法的函数
        function callAndroid(command) {
            try {
                if (window.xmuseAndroid && window.xmuseAndroid.processWebMsg) {
                    const jsonString = JSON.stringify(command);
                    console.log('发送命令:', jsonString);
                    const result = window.xmuseAndroid.processWebMsg(jsonString);
                    console.log('收到响应:', result);
                    return result ? JSON.parse(result) : null;
                } else {
                    console.error('xmuseAndroid接口不可用');
                    return null;
                }
            } catch (e) {
                console.error('调用Android方法失败:', e);
                return null;
            }
        }

        // 获取设备信息
        function getDeviceInfo() {
            const command = {
                command: 'getDeviceInfo'
            };

            const result = callAndroid(command);
            displayResult(result, '设备信息获取');
        }

        // 显示结果
        function displayResult(result, operation) {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleString();
            
            let resultHtml = `<div class="result ${result && result.success ? 'success' : 'error'}">`;
            resultHtml += `<h3>${operation} - ${timestamp}</h3>`;
            
            if (result) {
                if (result.success && result.data) {
                    resultHtml += `<p><strong>状态:</strong> 成功</p>`;
                    resultHtml += `<p><strong>消息:</strong> ${result.message || '无消息'}</p>`;
                    
                    // 显示设备信息
                    resultHtml += `<div class="device-info">`;
                    resultHtml += `<div class="label">MAC地址:</div><div class="value">${result.data.mac || '未知'}</div>`;
                    resultHtml += `<div class="label">设备ID:</div><div class="value">${result.data.deviceId || '未知'}</div>`;
                    resultHtml += `<div class="label">客户端ID:</div><div class="value">${result.data.tlClientId || '未知'}</div>`;
                    resultHtml += `<div class="label">时间戳:</div><div class="value">${result.data.timestamp || '未知'}</div>`;
                    resultHtml += `</div>`;
                    
                    // 显示原始JSON
                    resultHtml += `<div class="json-display">${JSON.stringify(result, null, 2)}</div>`;
                } else {
                    resultHtml += `<p><strong>状态:</strong> 失败</p>`;
                    resultHtml += `<p><strong>错误:</strong> ${result.message || '未知错误'}</p>`;
                    resultHtml += `<div class="json-display">${JSON.stringify(result, null, 2)}</div>`;
                }
            } else {
                resultHtml += `<p><strong>状态:</strong> 失败</p>`;
                resultHtml += `<p><strong>错误:</strong> 无法调用Android接口或解析响应</p>`;
            }
            
            resultHtml += `</div>`;
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('页面加载完成');
            console.log('xmuseAndroid接口可用:', !!window.xmuseAndroid);
        };
    </script>
</body>
</html>
