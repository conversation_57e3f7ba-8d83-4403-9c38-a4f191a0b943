<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView双向交互示例</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#10B981',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        neutral: '#6B7280',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .btn-effect {
                @apply transition-all duration-300 transform hover:scale-105 active:scale-95;
            }
            .content-box {
                @apply bg-gray-50 border border-gray-200 rounded-lg p-4 min-h-[200px] transition-all duration-300;
            }
            .status-indicator {
                @apply w-2 h-2 rounded-full inline-block mr-2;
            }
            .log-item {
                @apply mb-3 pb-3 border-b border-gray-100 last:border-0 last:mb-0 last:pb-0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen p-4 md:p-8">
    <div class="max-w-md mx-auto bg-white rounded-xl shadow-md p-6 md:p-8">
        <h1 class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-gray-800 mb-2 text-center">
            <i class="fa fa-exchange text-primary mr-2"></i>WebView双向交互
        </h1>
        <p class="text-center text-gray-500 text-sm mb-6">H5 ←→ Android 数据交互演示</p>

        <!-- 连接状态指示 -->
        <div class="flex items-center justify-center text-sm mb-6">
            <span class="status-indicator bg-gray-300" id="connectionStatus"></span>
            <span id="connectionText" class="text-gray-500">等待与App建立连接...</span>
        </div>

        <div class="flex flex-col gap-4 mb-6">
            <button id="btn1" class="bg-primary text-white py-3 px-6 rounded-lg font-medium btn-effect flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fa fa-key mr-2"></i>获取用户Token
            </button>

            <button id="btn2" class="bg-secondary text-white py-3 px-6 rounded-lg font-medium btn-effect flex items-center justify-center">
                <i class="fa fa-refresh mr-2"></i>清空交互日志
            </button>

            <button id="btn3" class="bg-success text-white py-3 px-6 rounded-lg font-medium btn-effect flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fa fa-save mr-2"></i>保存缓存数据
            </button>

            <button id="btn4" class="bg-warning text-white py-3 px-6 rounded-lg font-medium btn-effect flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fa fa-download mr-2"></i>获取缓存数据
            </button>

            <button id="btn5" class="bg-danger text-white py-3 px-6 rounded-lg font-medium btn-effect flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fa fa-trash mr-2"></i>删除缓存数据
            </button>
        </div>

        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <label class="block text-sm font-medium text-gray-700">
                    <i class="fa fa-comment text-neutral mr-1"></i>交互日志:
                </label>
                <span class="text-xs text-gray-500" id="logCount">0 条记录</span>
            </div>
            <div id="contentArea" class="content-box text-gray-700 text-sm overflow-auto">
                <div class="log-item text-gray-500">
                    <span class="status-indicator bg-gray-300"></span>
                    页面加载完成，等待交互...
                </div>
            </div>
        </div>

        <div class="text-xs text-gray-500 text-center mt-6">
            <p>支持: 调用App方法 / 接收App数据</p>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const btn1 = document.getElementById('btn1');
        const btn2 = document.getElementById('btn2');
        const btn3 = document.getElementById('btn3');
        const btn4 = document.getElementById('btn4');
        const btn5 = document.getElementById('btn5');
        const contentArea = document.getElementById('contentArea');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionText = document.getElementById('connectionText');
        const logCount = document.getElementById('logCount');

        // 日志计数
        let logItemCount = 1;

        // 更新日志计数显示
        function updateLogCount() {
            logCount.textContent = `${logItemCount} 条记录`;
        }

        // 记录日志到内容区域
        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();

            // 根据类型设置不同的指示器颜色
            let indicatorColor = 'bg-gray-300';
            switch(type) {
                case 'send':
                    indicatorColor = 'bg-primary';
                    break;
                case 'receive':
                    indicatorColor = 'bg-secondary';
                    break;
                case 'error':
                    indicatorColor = 'bg-danger';
                    break;
                case 'warning':
                    indicatorColor = 'bg-warning';
                    break;
            }

            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `
                <span class="status-indicator ${indicatorColor}"></span>
                <span class="text-gray-500 text-xs">${timestamp}</span><br>
                ${message}
            `;

            contentArea.appendChild(logItem);
            contentArea.scrollTop = contentArea.scrollHeight; // 滚动到底部

            logItemCount++;
            updateLogCount();
        }

        // 检查App接口是否可用
        function checkAppInterface() {
            if (window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                connectionStatus.className = 'status-indicator bg-secondary';
                connectionText.textContent = '已连接到App，可进行交互';
                btn1.disabled = false;
                btn3.disabled = false;
                btn4.disabled = false;
                btn5.disabled = false;
                logMessage('检测到App接口，已准备好进行交互', 'receive');
            } else {
                setTimeout(checkAppInterface, 1000); // 1秒后再次检查
            }
        }

        // 按钮1点击事件 - 调用Android方法
        btn1.addEventListener('click', () => {
            try {
                // 准备要发送的JSON数据
                const message = {
                    command: "requestUserToken"
                };
                const jsonString = JSON.stringify(message, null, 2);

                logMessage(`发送请求到App:<br><pre class="bg-gray-100 p-2 rounded mt-1 text-xs">${jsonString}</pre>`, 'send');

                // 调用Android注册的方法
                if (window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                    window.xmuseAndroid.processWebMsg(jsonString);
                } else {
                    logMessage("错误: 未找到xmuseAndroid.processWebMsg方法", 'error');
                }
            } catch (error) {
                logMessage(`调用失败: ${error.message}`, 'error');
            }
        });

        // 按钮2点击事件 - 清空内容
        btn2.addEventListener('click', () => {
            contentArea.innerHTML = '';
            logItemCount = 0;
            updateLogCount();
            logMessage("交互日志已清空", 'warning');
        });

        // 按钮3点击事件 - 保存缓存数据
        btn3.addEventListener('click', () => {
            try {
                // 准备要保存的测试数据
           

                const message = {
                    command: "saveCacheData",
                    key: "testData",
                    data: JSON.stringify( {"patient_person_id":14,"name":"姜严","mobile":"15974287428","id_card_no":"","age":50,"sex":"男","inpatient_no":"5656565","user_id":0,"inpatient_ward":"","is_deleted":false,"in_patient_time":0,"out_patient_time":0,"patient_avatar":"","patient_category":"","patient_know_img":"","attending_doctor_id":10,"assistant_doctor_id":0,"internal_no":"","bed_no":"","from":"自评室","tl_client_id":1000000000222,"inpatient_status":5,"is_discharged":false,"birthday":"1975-03-01","attending_doctor_name":"","id":601759,"is_info_change":false,"created_at":"2025-05-30T08:59:52.553523+08:00","updated_at":"2025-05-30T08:59:52.553523+08:00","nurse_id":0,"forms":"[{\"paper_id\":10146,\"question_id\":10401,\"key\":\"bumen\",\"name\":\"部门\",\"backupName\":\"部门\",\"description\":\"\",\"answer_group_code\":\"answer_group_bumen\",\"other\":\"\",\"otherList\":[],\"askedIf\":[],\"paper_re_question_id\":10410,\"view_type\":\"cascader\",\"type\":\"object\",\"sort\":0,\"unit\":\"\",\"paperIndex\":0,\"questionIndex\":0,\"answer_key\":[\"nQ_YVOE5ipfXgDiOBvirn\"],\"userAnswerName\":\"上海市总局-\",\"userAnswerNameCopy\":\"\",\"matrix\":{\"th\":[],\"score\":[],\"questionList\":[]},\"cascaderList\":[{\"id\":\"nQ_YVOE5ipfXgDiOBvirn\",\"name\":\"上海市总局\",\"children\":[{\"id\":\"iPpGl_8WSnq0dmQltFzcw\",\"name\":\"黄浦区分局\"},{\"id\":\"m_MpV6Q3TQVtuNWTff53_\",\"name\":\"静安区分局\"},{\"id\":\"p-eEb6Mf6OxQRiJ-zsX8d\",\"name\":\"徐汇区分局\"},{\"id\":\"-6memEqMQve87JqtXL-70\",\"name\":\"长宁区分局\"},{\"id\":\"OTfg0we7ra1dOUMpV4jdO\",\"name\":\"杨浦区分局\"},{\"id\":\"pFIq6PA6SbOYiMX541JmD\",\"name\":\"虹口区分局\"},{\"id\":\"DaMSYxPjxua-cPrYs65A_\",\"name\":\"普陀区分局\"}]},{\"id\":\"JK0fZb5a1awnsDQkdawmE\",\"name\":\"杭州市总局\",\"children\":[{\"id\":\"c58o_US9_y5QeThWuX-CJ\",\"name\":\"上城区‌分局\"},{\"id\":\"vTwcj6bHHCzZL-OZyCiUi\",\"name\":\"拱墅区‌分局\"},{\"id\":\"vQgQd_5YlrwIbS7jrst0Z\",\"name\":\"西湖区‌分局\"},{\"id\":\"GfaCj2YrtmZckEXiX-0Xz\",\"name\":\"滨江区‌分局\"},{\"id\":\"L-n6q9ewwHxMrGo3m7HG_\",\"name\":\"‌萧山区‌分局\"},{\"id\":\"B0hlknazVcaU_B-lW9f5Z\",\"name\":\"余杭区‌分局\"}]}],\"answer_items\":[],\"public\":0,\"is_show_report\":false,\"correct_answer\":\"\",\"newProperty\":\"newValue\"}]","department_ids":"","patient_name":"姜严"})
                };
                const jsonString = JSON.stringify(message, null, 2);

                logMessage(`发送保存请求到App:<br><pre class="bg-gray-100 p-2 rounded mt-1 text-xs">${jsonString}</pre>`, 'send');

                // 调用Android注册的方法
                if (window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                    window.xmuseAndroid.processWebMsg(jsonString);
                } else {
                    logMessage("错误: 未找到xmuseAndroid.processWebMsg方法", 'error');
                }
            } catch (error) {
                logMessage(`保存失败: ${error.message}`, 'error');
            }
        });

        // 按钮4点击事件 - 获取缓存数据
        btn4.addEventListener('click', () => {
            try {
                const message = {
                    command: "getCacheData",
                    key: "testData"
                };
                const jsonString = JSON.stringify(message, null, 2);

                logMessage(`发送获取请求到App:<br><pre class="bg-gray-100 p-2 rounded mt-1 text-xs">${jsonString}</pre>`, 'send');

                // 调用Android注册的方法
                if (window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                    window.xmuseAndroid.processWebMsg(jsonString);
                } else {
                    logMessage("错误: 未找到xmuseAndroid.processWebMsg方法", 'error');
                }
            } catch (error) {
                logMessage(`获取失败: ${error.message}`, 'error');
            }
        });

        // 按钮5点击事件 - 删除缓存数据
        btn5.addEventListener('click', () => {
            try {
                const message = {
                    command: "deleteCacheData",
                    key: "testUserData"
                };
                const jsonString = JSON.stringify(message, null, 2);

                logMessage(`发送删除请求到App:<br><pre class="bg-gray-100 p-2 rounded mt-1 text-xs">${jsonString}</pre>`, 'send');

                // 调用Android注册的方法
                if (window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                    window.xmuseAndroid.processWebMsg(jsonString);
                } else {
                    logMessage("错误: 未找到xmuseAndroid.processWebMsg方法", 'error');
                }
            } catch (error) {
                logMessage(`删除失败: ${error.message}`, 'error');
            }
        });

        // 供Android调用的方法 - 接收来自App的数据
        window.fromNative = function(message) {
            try {
                // 尝试解析JSON格式的消息

                 let displayMessage;
            if (typeof message === 'object' && message !== null) {
                // 如果是对象/数组，使用JSON.stringify格式化显示
                displayMessage = JSON.stringify(message, null, 2);
            } else {
                // 其他类型直接转换为字符串
                displayMessage = String(message);
            }
                logMessage(`收到App数据:<br><pre class="bg-gray-100 p-2 rounded mt-1 text-xs">${displayMessage}</pre>`, 'receive');
            } catch (e) {
                // 如果不是JSON格式，直接显示
                logMessage(`收到App消息: ${message}`, 'receive');
            }
        };

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            // 初始禁用按钮，直到检测到App接口
            btn1.disabled = true;
            btn3.disabled = true;
            btn4.disabled = true;
            btn5.disabled = true;

            // 开始检查App接口是否可用
            checkAppInterface();

            updateLogCount();
        });
    </script>
</body>
</html>
