<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XMUSE Android 调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-send { background: #e3f2fd; }
        .log-receive { background: #e8f5e8; }
        .log-error { background: #ffebee; color: #c62828; }
        .log-info { background: #fff3e0; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.disconnected {
            background: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>XMUSE Android 调试工具</h1>
        
        <!-- 环境检测 -->
        <div class="section">
            <h3>环境检测</h3>
            <div id="status" class="status disconnected">未连接到 Android 环境</div>
            <button onclick="checkEnvironment()">检测环境</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <!-- 基础测试 -->
        <div class="section">
            <h3>基础功能测试</h3>
            <button onclick="testBasicConnection()">测试基础连接</button>
            <button onclick="testUserToken()">获取用户Token</button>
            <button onclick="testSaveData()">保存测试数据</button>
            <button onclick="testGetData()">获取测试数据</button>
            <button onclick="testDeleteData()">删除测试数据</button>
        </div>

        <!-- 患者信息测试 -->
        <div class="section">
            <h3>患者信息测试</h3>
            <div>
                <label>患者姓名：</label>
                <input type="text" id="patientName" value="张三" placeholder="输入患者姓名">
            </div>
            <div>
                <label>住院号：</label>
                <input type="text" id="inpatientNo" value="12345678" placeholder="输入住院号">
            </div>
            <div>
                <label>用户ID：</label>
                <input type="text" id="userId" value="1001" placeholder="输入用户ID">
            </div>
            <button onclick="savePatientInfo()">保存患者信息</button>
            <button onclick="getPatientInfo()">获取患者信息</button>
            <button onclick="clearPatientInfo()">清除患者信息</button>
        </div>

        <!-- 自定义命令测试 -->
        <div class="section">
            <h3>自定义命令测试</h3>
            <textarea id="customCommand" placeholder='输入自定义JSON命令，例如：{"command": "getCacheData", "key": "testKey"}'></textarea>
            <button onclick="sendCustomCommand()">发送自定义命令</button>
        </div>

        <!-- 日志显示 -->
        <div class="section">
            <h3>调试日志</h3>
            <div id="logContainer" class="log"></div>
        </div>
    </div>

    <script>
        // 定义 window.fromNative 函数来接收来自 Android 的消息
        window.fromNative = function(data) {
            try {
                logMessage(`收到来自Android的消息: ${JSON.stringify(data)}`, 'receive');

                // 处理不同类型的消息
                if (data && data.command) {
                    switch (data.command) {
                        case 'userTokenResult':
                            logMessage(`✅ 收到Token: ${data.token}`, 'info');
                            break;
                        case 'error':
                            logMessage(`❌ Android错误: ${data.message}`, 'error');
                            break;
                        default:
                            logMessage(`📨 收到消息: ${data.command}`, 'info');
                            break;
                    }
                }
            } catch (e) {
                logMessage(`处理Android消息失败: ${e.message}`, 'error');
            }
        };

        // 日志记录函数
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 检测环境
        function checkEnvironment() {
            const statusEl = document.getElementById('status');
            
            logMessage('开始检测 Android 环境...', 'info');
            logMessage(`window 对象存在: ${typeof window !== 'undefined'}`, 'info');
            logMessage(`xmuseAndroid 对象存在: ${!!window.xmuseAndroid}`, 'info');
            logMessage(`processWebMsg 方法存在: ${typeof window.xmuseAndroid?.processWebMsg === 'function'}`, 'info');
            
            if (typeof window !== 'undefined' && !!window.xmuseAndroid && typeof window.xmuseAndroid.processWebMsg === 'function') {
                statusEl.textContent = '已连接到 Android 环境';
                statusEl.className = 'status connected';
                logMessage('✅ Android 环境检测成功！', 'receive');
            } else {
                statusEl.textContent = '未连接到 Android 环境';
                statusEl.className = 'status disconnected';
                logMessage('❌ Android 环境检测失败！', 'error');
            }
        }

        // 调用 Android 方法的通用函数
        function callAndroid(command) {
            try {
                if (!window.xmuseAndroid || typeof window.xmuseAndroid.processWebMsg !== 'function') {
                    throw new Error('Android 环境不可用');
                }

                const jsonString = typeof command === 'string' ? command : JSON.stringify(command);
                logMessage(`发送到Android: ${jsonString}`, 'send');

                const result = window.xmuseAndroid.processWebMsg(jsonString);
                logMessage(`Android返回原始数据: ${result}`, 'receive');

                // 尝试解析返回的JSON
                if (result && typeof result === 'string') {
                    try {
                        const parsedResult = JSON.parse(result);
                        logMessage(`解析后的结果: ${JSON.stringify(parsedResult, null, 2)}`, 'info');
                        return parsedResult;
                    } catch (parseError) {
                        logMessage(`JSON解析失败: ${parseError.message}`, 'error');
                        return result; // 返回原始字符串
                    }
                }

                return result;
            } catch (error) {
                logMessage(`调用失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 测试基础连接
        function testBasicConnection() {
            const testCommand = { command: 'test', message: 'Hello Android!' };
            callAndroid(testCommand);
        }

        // 获取用户Token
        function testUserToken() {
            const command = { command: 'userToken' };
            const result = callAndroid(command);
            if (result) {
                if (result.success && result.token) {
                    logMessage(`✅ 获取Token成功: ${result.token}`, 'info');
                } else {
                    logMessage(`❌ 获取Token失败: ${result.message || '未知错误'}`, 'error');
                }
            }
        }

        // 保存测试数据
        function testSaveData() {
            const testData = {
                timestamp: new Date().toISOString(),
                message: '这是测试数据',
                number: Math.random()
            };

            const command = {
                command: 'saveCacheData',
                key: 'testData',
                data: JSON.stringify(testData)
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success) {
                    logMessage(`✅ 保存数据成功: ${result.message}`, 'info');
                } else {
                    logMessage(`❌ 保存数据失败: ${result.message}`, 'error');
                }
            }
        }

        // 获取测试数据
        function testGetData() {
            const command = {
                command: 'getCacheData',
                key: 'testData'
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success) {
                    if (result.data) {
                        try {
                            const parsedData = JSON.parse(result.data);
                            logMessage(`✅ 获取数据成功: ${JSON.stringify(parsedData, null, 2)}`, 'info');
                        } catch (e) {
                            logMessage(`✅ 获取数据成功（原始字符串）: ${result.data}`, 'info');
                        }
                    } else {
                        logMessage(`⚠️ 未找到数据: ${result.message}`, 'info');
                    }
                } else {
                    logMessage(`❌ 获取数据失败: ${result.message}`, 'error');
                }
            }
        }

        // 删除测试数据
        function testDeleteData() {
            const command = {
                command: 'deleteCacheData',
                key: 'testData'
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success) {
                    logMessage(`✅ 删除数据成功: ${result.message}`, 'info');
                } else {
                    logMessage(`❌ 删除数据失败: ${result.message}`, 'error');
                }
            }
        }

        // 保存患者信息
        function savePatientInfo() {
            const patientData = {
                name: document.getElementById('patientName').value,
                inpatient_no: document.getElementById('inpatientNo').value,
                user_id: parseInt(document.getElementById('userId').value) || 0,
                timestamp: new Date().toISOString()
            };

            const command = {
                command: 'saveCacheData',
                key: 'patientInfo',
                data: JSON.stringify(patientData)
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success) {
                    logMessage(`✅ 保存患者信息成功: ${result.message}`, 'info');
                } else {
                    logMessage(`❌ 保存患者信息失败: ${result.message}`, 'error');
                }
            }
        }

        // 获取患者信息
        function getPatientInfo() {
            const command = {
                command: 'getCacheData',
                key: 'patientInfo'
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success && result.data) {
                    try {
                        const patientData = JSON.parse(result.data);
                        logMessage(`✅ 获取患者信息成功: ${JSON.stringify(patientData, null, 2)}`, 'info');

                        // 更新表单
                        if (patientData.name) document.getElementById('patientName').value = patientData.name;
                        if (patientData.inpatient_no) document.getElementById('inpatientNo').value = patientData.inpatient_no;
                        if (patientData.user_id) document.getElementById('userId').value = patientData.user_id;
                    } catch (e) {
                        logMessage(`❌ 患者信息解析失败: ${e.message}`, 'error');
                    }
                } else {
                    logMessage(`⚠️ 未找到患者信息: ${result.message}`, 'info');
                }
            }
        }

        // 清除患者信息
        function clearPatientInfo() {
            const command = {
                command: 'deleteCacheData',
                key: 'patientInfo'
            };

            const result = callAndroid(command);
            if (result) {
                if (result.success) {
                    logMessage(`✅ 清除患者信息成功: ${result.message}`, 'info');
                    // 清空表单
                    document.getElementById('patientName').value = '张三';
                    document.getElementById('inpatientNo').value = '12345678';
                    document.getElementById('userId').value = '1001';
                } else {
                    logMessage(`❌ 清除患者信息失败: ${result.message}`, 'error');
                }
            }
        }

        // 发送自定义命令
        function sendCustomCommand() {
            const customCommandText = document.getElementById('customCommand').value.trim();
            if (!customCommandText) {
                logMessage('请输入自定义命令', 'error');
                return;
            }

            try {
                const command = JSON.parse(customCommandText);
                const result = callAndroid(command);
                if (result) {
                    logMessage(`✅ 自定义命令执行完成`, 'info');
                }
            } catch (e) {
                logMessage(`❌ 自定义命令格式错误: ${e.message}`, 'error');
            }
        }

        // 页面加载时自动检测环境
        window.addEventListener('load', function() {
            logMessage('页面加载完成，开始自动检测环境...', 'info');
            setTimeout(checkEnvironment, 500);

            // 自动测试基础功能
            setTimeout(function() {
                logMessage('开始自动测试基础功能...', 'info');
                testBasicConnection();
                setTimeout(testUserToken, 1000);
                setTimeout(testSaveData, 2000);
                setTimeout(testGetData, 3000);
            }, 2000);
        });
    </script>
</body>
</html>
