cmake_minimum_required(VERSION 3.4.1)

#set(LIB_FMOD ${FMOD_API_ROOT}/${ANDROID_ABI}/libfmod${FMOD_LIB_SUFFIX}.so)
#set(LIB_FMODSTUDIO ${FMOD_API_ROOT}/studio/${ANDROID_ABI}/libfmodstudio${FMOD_LIB_SUFFIX}.so)
#
#add_library(fmod SHARED IMPORTED)
#set_target_properties(
#        fmod PROPERTIES
#        IMPORTED_LOCATION ${LIB_FMOD}
#        INTERFACE_INCLUDE_DIRECTORIES ${CMAKE_CURRENT_SOURCE_DIR}/inc
#)
#
#add_library(fmodstudio SHARED IMPORTED)
#set_target_properties(
#        fmodstudio PROPERTIES
#        IMPORTED_LOCATION ${LIB_FMODSTUDIO}
#        INTERFACE_INCLUDE_DIRECTORIES ${CMAKE_CURRENT_SOURCE_DIR}/inc
#)

find_library(
        log-lib
        log
)

add_library(eegcalculate SHARED
        EEG_derived_indicators.cpp)

target_link_libraries(eegcalculate ${log-lib})

