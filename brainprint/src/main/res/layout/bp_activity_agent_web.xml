<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/toolbar_space_view"
        android:layout_width="match_parent"
        android:layout_height="82dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/app_bar" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/view_navigation_space"
        app:layout_constraintTop_toBottomOf="@id/app_bar">

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="84dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_bp_home_title_logo"
            android:layout_width="120dp"
            android:layout_height="22dp"
            android:layout_marginStart="@dimen/space36"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/pic_bp_xmuse_logo01" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_toolbar_title"
            style="?TextTitleStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:lineSpacingExtra="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="妙诗App"
            tools:textStyle="bold" />

        <com.ruiheng.xmuse.ui.widget.BPMuseStatusIndicatorView
            android:id="@+id/sqc_layout"
            android:layout_width="72dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/space24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_navigation_space"
        android:layout_width="match_parent"
        android:layout_height="1px"

        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>