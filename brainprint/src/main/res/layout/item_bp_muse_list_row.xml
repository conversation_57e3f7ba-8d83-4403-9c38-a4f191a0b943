<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="muse"
            type="com.choosemuse.libmuse.Muse" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.divider.MaterialDivider
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dividerColor="?colorOnSurfaceVariant"
            app:dividerThickness="1px"
            app:layout_constraintBottom_toBottomOf="@id/layout_container" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingTop="@dimen/activity_vertical_margin"
            android:paddingBottom="@dimen/activity_vertical_margin"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_public_option"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginEnd="8dp"
                android:scaleType="fitXY"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@android:drawable/presence_away" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="?colorOnPrimaryContainer"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_public_option_content"
                app:layout_constraintStart_toEndOf="@id/img_public_option"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Less Active" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_public_option_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:gravity="end"
                android:textColor="@color/grey600"
                android:textSize="14sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/layout_option_container"
                app:layout_constraintStart_toEndOf="@id/tv_public_option"
                app:layout_constraintTop_toTopOf="@id/tv_public_option"
                tools:text="NNNNNNNN" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_option_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_public_menu_end"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_public_action_arrow_hor" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img_connected"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_radio_button_checked" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
