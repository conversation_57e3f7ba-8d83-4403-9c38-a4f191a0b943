<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#192147"
    android:keepScreenOn="true">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/img_bp_home_title"
        android:layout_width="450dp"
        android:layout_height="60dp"
        android:gravity="center"
        android:text="脑电波HRV检测"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_bp_home_title_logo"
        android:layout_width="120dp"
        android:layout_height="22dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_home_title"
        app:layout_constraintEnd_toStartOf="@id/img_bp_home_title"
        app:layout_constraintHorizontal_bias="0.2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_bp_home_title"
        tools:src="@drawable/pic_bp_xmuse_logo01" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_person"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        android:padding="@dimen/activity_horizontal_margin"
        app:layout_constraintBottom_toBottomOf="@id/sqc_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/sqc_layout">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_person"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginEnd="@dimen/half_normal_margin"
            app:layout_constraintEnd_toStartOf="@id/tv_time_remaining"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ico_bp_account_setting"
            app:tint="?colorOnSurface" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_time_remaining"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_title"
            app:layout_constraintBottom_toBottomOf="@id/img_person"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/img_person"
            tools:text="蔡全全" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/activity_horizontal_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/img_bp_home_title">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_heart_rate_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/activity_horizontal_margin"
            app:cardBackgroundColor="#14ffffff"
            app:cardCornerRadius="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="200dp"
                android:paddingStart="@dimen/space24"
                android:paddingTop="@dimen/space12"
                android:paddingEnd="@dimen/space24"
                android:paddingBottom="@dimen/space12">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_heart_rate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?colorOnSurface"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="86" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="心率"
                    android:textColor="?colorOnSurface"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintStart_toStartOf="@id/tv_heart_rate"
                    app:layout_constraintTop_toBottomOf="@id/tv_heart_rate" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ico_bp_heartrate" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_sdnn_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            app:cardBackgroundColor="#14ffffff"
            app:cardCornerRadius="16dp"
            app:layout_constraintStart_toEndOf="@id/layout_heart_rate_card"
            app:layout_constraintTop_toTopOf="@id/layout_heart_rate_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="200dp"
                android:paddingStart="@dimen/space24"
                android:paddingTop="@dimen/space12"
                android:paddingEnd="@dimen/space24"
                android:paddingBottom="@dimen/space12">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_sdnn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?colorOnSurface"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="86" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SDNN"
                    android:textColor="?colorOnSurface"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintStart_toStartOf="@id/tv_sdnn"
                    app:layout_constraintTop_toBottomOf="@id/tv_sdnn" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ico_bp_stabilization" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_stable_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            app:cardBackgroundColor="#14ffffff"
            app:cardCornerRadius="16dp"
            app:layout_constraintStart_toEndOf="@id/layout_sdnn_card"
            app:layout_constraintTop_toTopOf="@id/layout_heart_rate_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="200dp"
                android:paddingStart="@dimen/space24"
                android:paddingTop="@dimen/space12"
                android:paddingEnd="@dimen/space24"
                android:paddingBottom="@dimen/space12">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_stable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?colorOnSurface"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="86" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="稳定指数"
                    android:textColor="?colorOnSurface"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintStart_toStartOf="@id/tv_stable"
                    app:layout_constraintTop_toBottomOf="@id/tv_stable" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ico_bp_stabilization" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/layout_stress_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            app:cardBackgroundColor="#14ffffff"
            app:cardCornerRadius="16dp"
            app:layout_constraintStart_toEndOf="@id/layout_stable_card"
            app:layout_constraintTop_toTopOf="@id/layout_heart_rate_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="200dp"
                android:paddingStart="@dimen/space24"
                android:paddingTop="@dimen/space12"
                android:paddingEnd="@dimen/space24"
                android:paddingBottom="@dimen/space12">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_stress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?colorOnSurface"
                    android:textSize="28sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="86" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="压力指数"
                    android:textColor="?colorOnSurface"
                    android:textSize="@dimen/font_small"
                    app:layout_constraintStart_toStartOf="@id/tv_stress"
                    app:layout_constraintTop_toBottomOf="@id/tv_stress" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ico_bp_crush" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <include
            android:id="@+id/layout_band_power_container"
            layout="@layout/bp_content_detection_band_power"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@id/layout_heart_rate_card"
            app:layout_constraintTop_toBottomOf="@id/layout_heart_rate_card" />

        <include
            android:id="@+id/layout_body_graph_container"
            layout="@layout/bp_content_detection_body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            app:layout_constraintStart_toEndOf="@id/layout_band_power_container"
            app:layout_constraintTop_toBottomOf="@id/layout_heart_rate_card" />

        <include
            android:id="@+id/layout_ppg_graph_container"
            layout="@layout/bp_content_detection_ppg_graph"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/space24"
            app:layout_constraintStart_toEndOf="@id/layout_body_graph_container"
            app:layout_constraintTop_toBottomOf="@id/layout_heart_rate_card" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="重要提示：请您在检测过程中尽量保持平静，不要说话，保持手指不动。否则可能影响最终结果真实性"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ruiheng.xmuse.ui.widget.BPMuseStatusIndicatorView
        android:id="@+id/sqc_layout"
        android:layout_width="72dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/space12"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_home_title"
        app:layout_constraintEnd_toStartOf="@id/layout_person"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_muse_list_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black64"
        android:visibility="gone" />


    <include
        android:id="@+id/layout_content_calibration"
        layout="@layout/bp_content_calibration"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>