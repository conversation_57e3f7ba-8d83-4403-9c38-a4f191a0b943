<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_power_band"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_vertical_margin"
        android:text="PPG"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/chart_power"
        android:layout_width="280dp"
        android:layout_height="220dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_power_band" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/half_normal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/half_normal_margin"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chart_power"
        app:singleLine="true">


        <com.google.android.material.chip.Chip
            android:id="@+id/chip_alpha"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="AMBIENT"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/green500"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_beta"
            style="@style/Widget.Material3.Chip.Suggestion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="IR"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color3"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_gamma"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="RED"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/red500"
            app:chipStrokeWidth="0dp" />


    </com.google.android.material.chip.ChipGroup>


</androidx.constraintlayout.widget.ConstraintLayout>