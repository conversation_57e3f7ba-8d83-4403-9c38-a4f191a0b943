<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black26">

    <com.google.android.material.card.MaterialCardView
        style="?attr/materialCardViewFilledStyle"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.45">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/space24">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_person_avatar"
                android:layout_width="64dp"
                android:layout_height="64dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ico_avatar_default" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_person_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_vertical_margin"
                android:textColor="?colorOnSurface"
                android:textSize="@dimen/font_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/img_person_avatar"
                tools:text="蔡全全" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_horizontal_margin"
                android:layout_marginTop="@dimen/space24"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:text="退出登录"
                android:textSize="@dimen/font_normal"
                app:layout_constraintTop_toBottomOf="@id/tv_person_name" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>


</androidx.constraintlayout.widget.ConstraintLayout>