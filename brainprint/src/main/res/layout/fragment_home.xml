<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        tools:src="@drawable/pic_bp_home_bg" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_bp_home_title"
        android:layout_width="450dp"
        android:layout_height="60dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/pic_bp_home_title" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_bp_home_title_logo"
        android:layout_width="120dp"
        android:layout_height="22dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_home_title"
        app:layout_constraintEnd_toStartOf="@id/img_bp_home_title"
        app:layout_constraintHorizontal_bias="0.2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_bp_home_title"
        tools:src="@drawable/pic_bp_xmuse_logo01" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img_bp_brain_bg"
        android:layout_width="332dp"
        android:layout_height="312dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/pic_bp_brain_bg" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_mental_health_assessment"
        android:layout_width="250dp"
        android:layout_height="250dp"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_brain_bg"
        app:layout_constraintEnd_toStartOf="@id/img_bp_brain_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_bp_brain_bg"
        app:layout_constraintVertical_bias="0.95">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_mental_health_assessment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            tools:src="@drawable/pic_bp_health" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_mental_health_assessment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="心理健康评估"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/img_mental_health_assessment"
            app:layout_constraintHorizontal_bias="0.2"
            app:layout_constraintStart_toStartOf="@id/img_mental_health_assessment"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_mental_health_assessment_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Mental health assessment"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_small"
            app:layout_constraintStart_toStartOf="@id/tv_mental_health_assessment"
            app:layout_constraintTop_toBottomOf="@id/tv_mental_health_assessment" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_eeg_detection"
        android:layout_width="250dp"
        android:layout_height="250dp"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_brain_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/img_bp_brain_bg"
        app:layout_constraintTop_toTopOf="@id/img_bp_brain_bg"
        app:layout_constraintVertical_bias="0.95">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_eeg_detection"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            tools:src="@drawable/pic_bp_eeg" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_detection"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="脑电波NRV检测"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="@id/img_eeg_detection"
            app:layout_constraintHorizontal_bias="0.25"
            app:layout_constraintStart_toStartOf="@id/img_eeg_detection"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_detection_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="EEG detection"
            android:textColor="?colorOnSurfaceVariant"
            android:textSize="@dimen/font_small"
            app:layout_constraintStart_toStartOf="@id/tv_detection"
            app:layout_constraintTop_toBottomOf="@id/tv_detection" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_person"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/activity_horizontal_margin"
        app:layout_constraintBottom_toBottomOf="@id/sqc_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/sqc_layout">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_person"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginEnd="@dimen/half_normal_margin"
            app:layout_constraintEnd_toStartOf="@id/tv_person"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ico_bp_account_setting"
            app:tint="?colorOnSurface" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_person"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="?colorOnSurface"
            android:textSize="@dimen/font_large"
            app:layout_constraintBottom_toBottomOf="@id/img_person"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/img_person"
            tools:text="蔡全全dddddd" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_muse_list_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black64"
        android:visibility="gone" />


    <com.ruiheng.xmuse.ui.widget.BPMuseStatusIndicatorView
        android:id="@+id/sqc_layout"
        android:layout_width="72dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/space12"
        app:layout_constraintBottom_toBottomOf="@id/img_bp_home_title"
        app:layout_constraintEnd_toStartOf="@id/layout_person" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_reset_calibration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/space24"
        android:text="设备校准"
        android:textSize="@dimen/font_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include
        android:id="@+id/layout_content_calibration"
        layout="@layout/bp_content_calibration"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>