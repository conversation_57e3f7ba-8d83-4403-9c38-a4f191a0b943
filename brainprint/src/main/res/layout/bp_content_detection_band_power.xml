<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_power_band"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_vertical_margin"
        android:text="脑电波功率波段"
        android:textColor="?colorOnSurface"
        android:textSize="@dimen/font_normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/chart_power"
        android:layout_width="280dp"
        android:layout_height="220dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_power_band" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chip_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/half_normal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:paddingBottom="@dimen/half_normal_margin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chart_power"
        app:singleLine="true">


        <com.google.android.material.chip.Chip
            android:id="@+id/chip_alpha"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="α"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color2"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_beta"
            style="@style/Widget.Material3.Chip.Suggestion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="β"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color3"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_gamma"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="γ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color4"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_delta"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="δ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color5"
            app:chipStrokeWidth="0dp" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_theta"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:gravity="center"
            android:text="θ"
            android:textAlignment="center"
            android:textSize="@dimen/font_small"
            app:checkedIconEnabled="false"
            app:chipCornerRadius="16dp"
            app:chipIcon="@drawable/ic_dot"
            app:chipIconEnabled="true"
            app:chipIconSize="8dp"
            app:chipIconTint="@color/color1"
            app:chipStrokeWidth="0dp" />
    </com.google.android.material.chip.ChipGroup>


</androidx.constraintlayout.widget.ConstraintLayout>