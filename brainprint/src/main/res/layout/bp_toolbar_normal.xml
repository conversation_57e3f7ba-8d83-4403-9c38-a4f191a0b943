<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#192147">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_cl_toolbar"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_bp_home_title_logo"
            android:layout_width="120dp"
            android:layout_height="22dp"
            android:scaleType="fitXY"
            android:layout_marginStart="@dimen/space36"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:src="@drawable/pic_bp_xmuse_logo01" />

        <TextView
            android:id="@+id/tv_toolbar_title"
            style="?TextTitleStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:lineSpacingExtra="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="妙诗App"
            tools:textStyle="bold" />



    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.divider.MaterialDivider
        android:id="@+id/divider_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>