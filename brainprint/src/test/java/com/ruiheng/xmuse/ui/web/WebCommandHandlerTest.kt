package com.ruiheng.xmuse.ui.web

import com.google.gson.Gson
import com.ruiheng.xmuse.ui.web.handler.WebCommandHandler
import com.ruiheng.xmuse.ui.web.repository.WebCacheRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * WebCommandHandler 单元测试
 */
class WebCommandHandlerTest {

    private lateinit var webCacheRepository: WebCacheRepository
    private lateinit var webCommandHandler: WebCommandHandler
    private val gson = Gson()

    @Before
    fun setup() {
        webCacheRepository = mockk()
        webCommandHandler = WebCommandHandler(webCacheRepository)
    }

    @Test
    fun `test handleUserToken command`() {
        val jsonCommand = """{"command": "userToken"}"""
        val userToken = "test_token_123"

        val result = webCommandHandler.executeCommand(jsonCommand, userToken)
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("userTokenResult", response["command"])
        assertEquals(true, response["success"])
        assertEquals(userToken, response["token"])
    }

    @Test
    fun `test handleTestCommand`() {
        val testMessage = "Hello from test"
        val jsonCommand = """{"command": "test", "message": "$testMessage"}"""

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("testResult", response["command"])
        assertEquals(true, response["success"])
        assertTrue(response["message"].toString().contains(testMessage))
    }

    @Test
    fun `test saveCacheData command success`() {
        val key = "testKey"
        val data = "testData"
        val jsonCommand = """{"command": "saveCacheData", "key": "$key", "data": "$data"}"""

        every { webCacheRepository.saveCache(key, data) } returns true

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("saveCacheDataResult", response["command"])
        assertEquals(true, response["success"])
        assertEquals(key, response["key"])

        verify { webCacheRepository.saveCache(key, data) }
    }

    @Test
    fun `test getCacheData command success`() {
        val key = "testKey"
        val cachedData = "cachedTestData"
        val jsonCommand = """{"command": "getCacheData", "key": "$key"}"""

        every { webCacheRepository.getCache(key) } returns cachedData

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("getCacheDataResult", response["command"])
        assertEquals(true, response["success"])
        assertEquals(key, response["key"])
        assertEquals(cachedData, response["data"])

        verify { webCacheRepository.getCache(key) }
    }

    @Test
    fun `test deleteCacheData command success`() {
        val key = "testKey"
        val jsonCommand = """{"command": "deleteCacheData", "key": "$key"}"""

        every { webCacheRepository.hasCache(key) } returns true
        every { webCacheRepository.deleteCache(key) } returns true

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("deleteCacheDataResult", response["command"])
        assertEquals(true, response["success"])
        assertEquals(key, response["key"])

        verify { webCacheRepository.hasCache(key) }
        verify { webCacheRepository.deleteCache(key) }
    }

    @Test
    fun `test invalid JSON command`() {
        val invalidJson = """{"invalid": json}"""

        val result = webCommandHandler.executeCommand(invalidJson, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("error", response["command"])
        assertEquals(false, response["success"])
        assertTrue(response["message"].toString().contains("JSON格式错误"))
    }

    @Test
    fun `test unknown command`() {
        val unknownCommand = """{"command": "unknownCommand"}"""

        val result = webCommandHandler.executeCommand(unknownCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("error", response["command"])
        assertEquals(false, response["success"])
        assertTrue(response["message"].toString().contains("未知命令"))
    }

    @Test
    fun `test saveCacheData with missing key`() {
        val jsonCommand = """{"command": "saveCacheData", "data": "testData"}"""

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("error", response["command"])
        assertEquals(false, response["success"])
        assertTrue(response["message"].toString().contains("缺少key参数"))
    }

    @Test
    fun `test getCacheData with missing key`() {
        val jsonCommand = """{"command": "getCacheData"}"""

        val result = webCommandHandler.executeCommand(jsonCommand, "")
        assertNotNull(result)

        val response = gson.fromJson(result, Map::class.java)
        assertEquals("error", response["command"])
        assertEquals(false, response["success"])
        assertTrue(response["message"].toString().contains("缺少key参数"))
    }
}
