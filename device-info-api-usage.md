# 设备信息获取API使用说明

## 概述

新增了一个用于H5端JS调用的设备信息获取方法，可以获取设备的MAC地址、设备ID、客户端ID等信息。

## API详情

### 命令名称
`getDeviceInfo`

### 请求格式
```javascript
const command = {
    command: 'getDeviceInfo'
};
```

### 响应格式

#### 成功响应
```json
{
    "command": "getDeviceInfoResult",
    "success": true,
    "data": {
        "mac": "设备MAC地址的MD5值",
        "deviceId": "设备唯一标识符",
        "tlClientId": "1000000000359",
        "timestamp": 1703123456789
    },
    "message": "设备信息获取成功"
}
```

#### 失败响应
```json
{
    "command": "error",
    "success": false,
    "message": "获取设备信息失败: 具体错误信息"
}
```

## 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `mac` | String | 设备唯一标识符，基于ANDROID_ID和Build.SERIAL的MD5值 |
| `deviceId` | String | 设备ID，Android 10+使用ANDROID_ID，Android 10-使用MAC地址 |
| `tlClientId` | String | 客户端ID常量，从配置文件获取 |
| `timestamp` | Long | 响应时间戳 |

## 使用示例

### JavaScript调用示例
```javascript
// 调用Android原生方法的函数
function callAndroid(command) {
    try {
        if (window.xmuseAndroid && window.xmuseAndroid.processWebMsg) {
            const jsonString = JSON.stringify(command);
            const result = window.xmuseAndroid.processWebMsg(jsonString);
            return result ? JSON.parse(result) : null;
        } else {
            console.error('xmuseAndroid接口不可用');
            return null;
        }
    } catch (e) {
        console.error('调用Android方法失败:', e);
        return null;
    }
}

// 获取设备信息
function getDeviceInfo() {
    const command = {
        command: 'getDeviceInfo'
    };

    const result = callAndroid(command);
    
    if (result && result.success) {
        console.log('设备信息获取成功:', result.data);
        console.log('MAC地址:', result.data.mac);
        console.log('设备ID:', result.data.deviceId);
        console.log('客户端ID:', result.data.tlClientId);
    } else {
        console.error('设备信息获取失败:', result ? result.message : '未知错误');
    }
}
```

### 完整HTML示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>设备信息获取示例</title>
</head>
<body>
    <button onclick="getDeviceInfo()">获取设备信息</button>
    <div id="result"></div>

    <script>
        function callAndroid(command) {
            // ... (上面的callAndroid函数代码)
        }

        function getDeviceInfo() {
            const command = { command: 'getDeviceInfo' };
            const result = callAndroid(command);
            
            const resultDiv = document.getElementById('result');
            if (result && result.success) {
                resultDiv.innerHTML = `
                    <h3>设备信息:</h3>
                    <p>MAC地址: ${result.data.mac}</p>
                    <p>设备ID: ${result.data.deviceId}</p>
                    <p>客户端ID: ${result.data.tlClientId}</p>
                    <p>时间戳: ${result.data.timestamp}</p>
                `;
            } else {
                resultDiv.innerHTML = `<p>错误: ${result ? result.message : '未知错误'}</p>`;
            }
        }
    </script>
</body>
</html>
```

## 测试页面

项目中已包含一个测试页面：`brainprint/src/main/assets/device-info-test.html`

可以在WebView中加载此页面来测试设备信息获取功能。

## 技术实现

### 后端实现
- 在 `WebCommandHandler` 中添加了 `GET_DEVICE_INFO` 命令处理
- 使用 `DeviceUtils.getUniqueId()` 获取设备唯一标识符
- 使用 `DeviceUtils.getDeviceId()` 获取设备ID
- 从配置文件 `PlatformKey.kt` 中获取客户端ID常量

### 相关文件
- `brainprint/src/main/java/com/ruiheng/xmuse/ui/web/handler/WebCommandHandler.kt` - 主要实现
- `core-data/src/main/java/com/ruiheng/xmuse/core/data/PlatformKey.kt` - 配置常量
- `core-data/src/main/java/com/ruiheng/xmuse/core/data/util/DeviceUtils.kt` - 设备信息工具类

## 注意事项

1. 确保WebView已正确配置JavaScript接口 `xmuseAndroid`
2. MAC地址是经过MD5加密的，不是原始MAC地址
3. 设备ID的获取方式在不同Android版本下有所不同
4. 客户端ID是从统一配置文件中获取的常量值

## 错误处理

API包含完整的错误处理机制：
- 捕获所有异常并返回错误响应
- 提供详细的错误信息
- 记录错误日志便于调试
