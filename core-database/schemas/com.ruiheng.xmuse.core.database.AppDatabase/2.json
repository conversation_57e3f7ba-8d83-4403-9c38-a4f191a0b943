{"formatVersion": 1, "database": {"version": 2, "identityHash": "137ec144e2db8e8bc4a192a0868e7855", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `nickName` TEXT, `headPortrait` TEXT, `token` TEXT, `judgeInputPassword` INTEGER, `judgeInputPhone` INTEGER, `selected` INTEGER, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "token", "columnName": "token", "affinity": "TEXT", "notNull": false}, {"fieldPath": "judgeInput<PERSON>ass<PERSON>", "columnName": "judgeInput<PERSON>ass<PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "judgeInputPhone", "columnName": "judgeInputPhone", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "selected", "columnName": "selected", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '137ec144e2db8e8bc4a192a0868e7855')"]}}