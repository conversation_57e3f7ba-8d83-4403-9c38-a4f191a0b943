{"formatVersion": 1, "database": {"version": 4, "identityHash": "ef333dbbd5d7b5b70ce3749a83881a96", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `nickName` TEXT, `headPortrait` TEXT, `phone` TEXT, `token` TEXT, `judgeInputPassword` INTEGER, `judgeInputPhone` INTEGER, `selected` INTEGER, `email` TEXT, `judgeInputBind` INTEGER, `deviceId` TEXT, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "token", "columnName": "token", "affinity": "TEXT", "notNull": false}, {"fieldPath": "judgeInput<PERSON>ass<PERSON>", "columnName": "judgeInput<PERSON>ass<PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "judgeInputPhone", "columnName": "judgeInputPhone", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "selected", "columnName": "selected", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "judge<PERSON>n<PERSON><PERSON><PERSON>", "columnName": "judge<PERSON>n<PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "deviceId", "columnName": "deviceId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "thirdparty", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `headPortrait` TEXT, `nickName` TEXT, `thirdType` INTEGER, `unionId` TEXT, `userId` TEXT NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thirdType", "columnName": "thirdType", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "unionId", "columnName": "unionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'ef333dbbd5d7b5b70ce3749a83881a96')"]}}