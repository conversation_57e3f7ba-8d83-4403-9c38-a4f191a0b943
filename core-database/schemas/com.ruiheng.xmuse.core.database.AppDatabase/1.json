{"formatVersion": 1, "database": {"version": 1, "identityHash": "0b2b8ce29e040d527a8fdea675dd7f33", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `nickName` TEXT, `headPortrait` TEXT, `thirdType` TEXT, `unionId` TEXT, `token` TEXT, `judgeInputPassword` INTEGER, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thirdType", "columnName": "thirdType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "unionId", "columnName": "unionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "token", "columnName": "token", "affinity": "TEXT", "notNull": false}, {"fieldPath": "judgeInput<PERSON>ass<PERSON>", "columnName": "judgeInput<PERSON>ass<PERSON>", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0b2b8ce29e040d527a8fdea675dd7f33')"]}}