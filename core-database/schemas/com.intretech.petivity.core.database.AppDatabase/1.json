{"formatVersion": 1, "database": {"version": 1, "identityHash": "36f438eb534535a6a54d17d4aa4ffe1b", "entities": [{"tableName": "user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `displayName` TEXT, `email` TEXT NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "cat", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `catName` TEXT NOT NULL, `birthdate` TEXT NOT NULL, `gender` TEXT, `avatar` TEXT, `neutered` INTEGER, `activityLevel` INTEGER, `ribLevel` INTEGER, `waistLevel` INTEGER, `profileLevel` INTEGER, `bodyConditionScore` INTEGER, `careGoal` INTEGER, `healthCondition` TEXT, `createdAt` TEXT NOT NULL, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "catName", "columnName": "catName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "birthdate", "columnName": "birthdate", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "neutered", "columnName": "neutered", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "activityLevel", "columnName": "activityLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "ribLevel", "columnName": "ribLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "waistLevel", "columnName": "waistLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileLevel", "columnName": "profileLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bodyConditionScore", "columnName": "bodyConditionScore", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "careGoal", "columnName": "careGoal", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "healthCondition", "columnName": "healthCondition", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "device_feeder", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `deviceName` TEXT, `firstConnectedAt` TEXT, `sn` TEXT, `batteryPercentage` TEXT, `batteryVoltage` REAL, `powerMode` TEXT, `wifiRssi` INTEGER, `camera` TEXT, `showBatteryWarning` INTEGER, `factoryResetCount` INTEGER, `hardwareRevision` TEXT, `stFirmwareRevision` TEXT, `espFirmwareRevision` TEXT, `macAddress` TEXT, `createdAt` TEXT, `updatedAt` TEXT, `timestamp` TEXT, `notifications` TEXT, `mostRecentEventAt` TEXT, PRIMARY KEY(`uid`))", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deviceName", "columnName": "deviceName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "firstConnectedAt", "columnName": "firstConnectedAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sn", "columnName": "sn", "affinity": "TEXT", "notNull": false}, {"fieldPath": "batteryPercentage", "columnName": "batteryPercentage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "batteryVoltage", "columnName": "batteryVoltage", "affinity": "REAL", "notNull": false}, {"fieldPath": "powerMode", "columnName": "powerMode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "wifiRssi", "columnName": "wifiRssi", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "camera", "columnName": "camera", "affinity": "TEXT", "notNull": false}, {"fieldPath": "showBatteryWarning", "columnName": "showBatteryWarning", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "factoryResetCount", "columnName": "factoryResetCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "hardwareRevision", "columnName": "hardwareRevision", "affinity": "TEXT", "notNull": false}, {"fieldPath": "stFirmwareRevision", "columnName": "stFirmwareRevision", "affinity": "TEXT", "notNull": false}, {"fieldPath": "espFirmwareRevision", "columnName": "espFirmwareRevision", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notifications", "columnName": "notifications", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mostRecentEventAt", "columnName": "mostRecentEventAt", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [], "foreignKeys": []}, {"tableName": "food", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `type` INTEGER NOT NULL, `brand` INTEGER, `product` INTEGER, `flavor` INTEGER, `prescriptionFoodName` TEXT, `enable` INTEGER NOT NULL, `machineId` TEXT NOT NULL, `brandName` TEXT, `productName` TEXT, `flavorName` TEXT, PRIMARY KEY(`uid`), FOREIGN KEY(`machineId`) REFERENCES `device_feeder`(`uid`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "brand", "columnName": "brand", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "product", "columnName": "product", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flavor", "columnName": "flavor", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "prescriptionFoodName", "columnName": "prescriptionFoodName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enable", "columnName": "enable", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "machineId", "columnName": "machineId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "brandName", "columnName": "brandName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "productName", "columnName": "productName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "flavorName", "columnName": "flavorName", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [{"name": "index_food_machineId", "unique": false, "columnNames": ["machineId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_food_machineId` ON `${TABLE_NAME}` (`machineId`)"}], "foreignKeys": [{"table": "device_feeder", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["machineId"], "referencedColumns": ["uid"]}]}, {"tableName": "schedule", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uid` TEXT NOT NULL, `name` TEXT, `dayOfWeek` TEXT, `enable` INTEGER, `createdAt` TEXT, `updatedAt` TEXT, `timestamp` TEXT, `machineId` TEXT NOT NULL, `meals` TEXT, PRIMARY KEY(`uid`), FOREIGN KEY(`machineId`) REFERENCES `device_feeder`(`uid`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "uid", "columnName": "uid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dayOfWeek", "columnName": "dayOfWeek", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enable", "columnName": "enable", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": false}, {"fieldPath": "machineId", "columnName": "machineId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "meals", "columnName": "meals", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["uid"]}, "indices": [{"name": "index_schedule_machineId", "unique": false, "columnNames": ["machineId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_schedule_machineId` ON `${TABLE_NAME}` (`machineId`)"}], "foreignKeys": [{"table": "device_feeder", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["machineId"], "referencedColumns": ["uid"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '36f438eb534535a6a54d17d4aa4ffe1b')"]}}