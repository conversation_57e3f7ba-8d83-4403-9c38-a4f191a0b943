package com.ruiheng.xmuse.core.database.di

import com.ruiheng.xmuse.core.database.AppDatabase
import com.ruiheng.xmuse.core.database.dao.UserDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
internal object DaoModule{
    @Provides
    fun providesUserDao(
        database: AppDatabase,
    ): UserDao = database.userDao()
}