/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ruiheng.xmuse.core.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.ruiheng.xmuse.core.database.dao.UserDao
import com.ruiheng.xmuse.core.database.model.ThirdPartyEntity
import com.ruiheng.xmuse.core.database.model.UserAioTokenJsonConverter
import com.ruiheng.xmuse.core.database.model.UserEntity

@Database(
    entities = [UserEntity::class, ThirdPartyEntity::class],
    version = 6
)
@TypeConverters(UserAioTokenJsonConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
}
