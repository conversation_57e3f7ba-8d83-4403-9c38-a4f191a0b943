package com.ruiheng.xmuse.core.database.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ruiheng.xmuse.core.model.data.UserAioTokenData
import com.ruiheng.xmuse.core.model.data.UserData
import com.ruiheng.xmuse.core.model.data.UserThirdParty

@Entity(
    tableName = "user",
)
data class UserEntity(
    @PrimaryKey
    val uid: String,
    val nickName: String?,
    val headPortrait: String?,
    val phone: String?,
    val token: String?,
    val judgeInputPassword: Boolean?,
    val judgeInputPhone: Boolean?,
    val selected: Boolean?,
    val email: String?,
    val judgeInputBind: Boolean?,
    val deviceId: String?,
    val gender: Int?,
    val birthday: String?,
    @TypeConverters(UserAioTokenJsonConverter::class)
    val aioToken: UserAioTokenEntity?
)

data class UserAioTokenEntity(
    val code: String?,
    val jwt_token: String?,
    val message: String?,
    val uid: String?
)

fun UserAioTokenEntity.asExternalModel() = UserAioTokenData(code, jwt_token, message, uid)

class UserAioTokenJsonConverter {
    @TypeConverter
    fun fromJson(value: String): UserAioTokenEntity {
        val listType = object : TypeToken<UserAioTokenEntity>() {}.type
        return Gson().fromJson(value, listType)
    }

    @TypeConverter
    fun fromEntity(entity: UserAioTokenEntity): String {
        return Gson().toJson(entity)
    }
}

fun UserEntity.asExternalModel() = UserData(
    uid = uid,
    nickName = nickName,
    phone = phone,
    headPortrait = headPortrait,
    token = token,
    judgeInputPassword = judgeInputPassword,
    judgeInputPhone = judgeInputPhone,
    selected = selected == true,
    email = email,
    judgeInputBind = judgeInputBind,
    deviceId = deviceId,
    gender = gender,
    birthday = birthday,
    bpAioToken = aioToken?.asExternalModel()
)

@Entity(
    tableName = "thirdparty",
)
data class ThirdPartyEntity(
    @PrimaryKey
    val uid: String,
    val headPortrait: String?,
    val nickName: String?,
    val thirdType: Int?,
    val unionId: String?,
    val userId: String
)

fun ThirdPartyEntity.asExternalModel() = UserThirdParty(
    nickName = nickName,
    headPortrait = headPortrait,
    thirdType = thirdType,
    unionId = unionId
)
//fun UserData.asFtsEntity() = UserEntity(uid = uid, displayName, email)