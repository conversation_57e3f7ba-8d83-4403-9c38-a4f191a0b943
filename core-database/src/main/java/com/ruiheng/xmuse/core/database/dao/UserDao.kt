package com.ruiheng.xmuse.core.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.ruiheng.xmuse.core.database.model.ThirdPartyEntity
import com.ruiheng.xmuse.core.database.model.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    @Query("SELECT * FROM user WHERE  selected = 1 ORDER BY uid DESC LIMIT 1 ")
    fun getUser(): Flow<UserEntity?>

    @Query("SELECT * FROM user LIMIT 2 ")
    fun getUsers(): Flow<List<UserEntity>?>

    @Query("UPDATE user SET selected = CASE WHEN uid = :uid THEN 1 ELSE 0 END")
    suspend fun updateUserSelected(uid: String): Int


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(item: UserEntity)

    @Query("Delete from user")
    suspend fun deleteAllUsers()

    @Query("Delete from user WHERE selected = 1")
    suspend fun deleteCurrentUsers()

    @Query("Delete from user WHERE selected = 1")
    fun deleteCurrentUserSync()

    @Query("Delete from user WHERE phone IS NULL OR phone = ''")
    suspend fun deleteInterimUser()

    @Transaction
    suspend fun insertUserAndRemove(item: UserEntity) {
        deleteAllUsers()
        insertUser(item)
    }

    @Transaction
    suspend fun insertUserAndSelect(item: UserEntity) {
//        deleteAllUsers()
        insertUser(item)
        updateUserSelected(item.uid)
    }

    @Query("Delete from thirdparty WHERE userId IN (SELECT uid FROM user WHERE selected = 1 LIMIT 1)")
    suspend fun deleteThirdPartyInfo()

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertThirdParty(item: List<ThirdPartyEntity>)

    @Transaction
    suspend fun updateThirdPartyList(item: List<ThirdPartyEntity>) {
        deleteThirdPartyInfo()
        insertThirdParty(item)
    }

    @Query("SELECT * FROM thirdparty WHERE  userId IN (SELECT uid FROM user WHERE selected = 1 LIMIT 1)")
    fun getThirdPartyUser(): Flow<List<ThirdPartyEntity>?>
}