[versions]
androidGradlePlugin = "8.5.1"
androidxActivity = "1.9.1"
androidxComposeBom = "2024.10.00"
androidxCore = "1.13.1"
dynamicanimation = "1.1.0"

androidxHilt = "1.2.0"
androidxLifecycle = "2.8.4"
androidxNavigation = "2.7.7"
androidxRoom = "2.6.1"
androidxTestCore = "1.6.1"
androidxTestExt = "1.2.1"
androidxTestRunner = "1.6.1"
androidXGraphics = "1.4.0"
androidxCredentials = "1.2.2"
firebaseBom = "33.6.0"
googleIds = "1.1.1"
androidxDataStore = "1.1.2"
angentWeb = "v5.1.1-androidx"
tensorflowLite = "2.11.0"

coroutines = "1.8.1"
hilt = "2.52"
junit = "4.13.2"
kotlin = "2.0.10"
ksp = "2.0.10-1.0.24"
appcompat = "1.7.0"
material = "1.12.0"
activity = "1.9.2"
kotlinGradlePlugin = "2.0.10"
constraintlayout = "2.1.4"
kotlinGradlePluginVersion = "2.0.10"
navigationFragmentKtx = "2.8.2"
navigationUiKtx = "2.8.2"
lifecycleLivedataKtx = "2.8.6"
lifecycleViewmodelKtx = "2.8.6"
fragmentKtx = "1.5.6"
autozie = "v1.2.1"
glide = "4.15.1"
glideRransformations = "4.3.0"

hw_scan = "2.2.0.300"
numberpicker = "2.4.13"
pickerView = "1.0.0"
utilcodex = "1.31.1"
rxble = "1.19.0"
xxPermission = "20.0"
rxAndroid = "3.0.2"
rxJava = "3.1.9"
cbor = "0.01.02"
spongycastle = "1.58.0.0"
apollographql = "4.0.0"
apple_login = "1.0.2"
okHttp = "4.12.0"
retrofit = "2.11.0"
pictureselector = "v3.11.2"
gson = "2.8.8"
multidex = "2.0.1"
wechat = "6.8.0"

google_services = "4.4.2"
refreshLayout = "3.0.0-alpha"
firebaseAuthKtx = "23.1.0"
timber = "4.5.1"
threetenabp = "1.1.1"
mpAndroidChart = "v3.1.0"
anychart="1.1.5"
palette = "1.0.0"
paletteKtx = "1.0.0"

aliyunOSS = "2.9.21"
aliyunFeedback = "3.4.2"
aliyunEmas = "2.0.0"
leakcanary = "2.10"
coreAnimation = "1.0.0"


[libraries]
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidxCore" }
#androidx-dynamicanimation = { module = "androidx.dynamicanimation:dynamicanimation", version.ref = "dynamicanimation" }

androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidxRoom" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "androidxRoom" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "androidxRoom" }
androidx-test-core = { module = "androidx.test:core", version.ref = "androidxTestCore" }
androidx-test-ext-junit = { module = "androidx.test.ext:junit", version.ref = "androidxTestExt" }
androidx-test-runner = { module = "androidx.test:runner", version.ref = "androidxTestRunner" }
androidx-dataStore = { group = "androidx.datastore", name = "datastore", version.ref = "androidxDataStore" }
androidx-dataStore-core = { group = "androidx.datastore", name = "datastore-core", version.ref = "androidxDataStore" }
androidx-ui-palette = { group = "androidx.palette:palette", name = "palette" }

firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }
firebase-auth = { module = "com.google.firebase:firebase-auth" }
firebase-auth-ktx = { module = "com.google.firebase:firebase-auth-ktx" }

hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }
hilt-android-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hilt" }
hilt-android-testing = { module = "com.google.dagger:hilt-android-testing", version.ref = "hilt" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }
hilt-gradle-plugin = { module = "com.google.dagger:hilt-android-gradle-plugin", version.ref = "hilt" }
junit = { module = "junit:junit", version.ref = "junit" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-graphics = { group = "androidx.graphics", name = "graphics", version.ref = "androidXGraphics" }

androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigationUiKtx" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
androidx-fragment-ktx = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragmentKtx" }
android-auto-size = { module = "com.github.JessYanCoding:AndroidAutoSize", version.ref = "autozie" }

google-iot-cbor = { module = "com.google.iot.cbor:cbor", version.ref = "cbor" }

javax-inject = { module = "javax.inject:javax.inject", version = "1" }

glide-runtime = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-annotation = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
glide-transformations = { module = "jp.wasabeef:glide-transformations", version.ref = "glideRransformations" }

hw-scan = { module = "com.huawei.hms:scan", version.ref = "hw_scan" }
number-picker = { module = "io.github.ShawnLin013:number-picker", version.ref = "numberpicker" }
other-utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }
other-rxble = { module = "com.polidea.rxandroidble3:rxandroidble", version.ref = "rxble" }
other-xxpermission = { module = "com.github.getActivity:XXPermissions", version.ref = "xxPermission" }
other-rx-android = { module = "io.reactivex.rxjava3:rxandroid", version.ref = "rxAndroid" }
other-rx-java = { module = "io.reactivex.rxjava3:rxjava", version.ref = "rxJava" }
other-gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
other-multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }

picker-view = { module = "com.github.duanhong169:picker-view", version.ref = "pickerView" }
squareup-okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okHttp" }
squareup-okhttp-logging = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okHttp" }
squareup-retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
squareup-retrofit-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
squareup-retrofit-rxjava = { module = "com.squareup.retrofit2:adapter-rxjava3", version.ref = "retrofit" }


other-wechat-core = {module = "com.tencent.mm.opensdk:wechat-sdk-android", version.ref = "wechat"}
other-pictureselector-core = { module = "io.github.lucksiege:pictureselector", version.ref = "pictureselector" }
other-pictureselector-ucrop = { module = "io.github.lucksiege:ucrop", version.ref = "pictureselector" }
other-smartrefreshLayout-core = { module = "io.github.scwang90:refresh-layout-kernel", version.ref = "refreshLayout" }
other-smartrefreshLayout-header = { module = "io.github.scwang90:refresh-header-classics", version.ref = "refreshLayout" }
other-smartrefreshLayout-footer = { module = "io.github.scwang90:refresh-footer-classics", version.ref = "refreshLayout" }
other_timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }
other_threetenabp = { module = "com.jakewharton.threetenabp:threetenabp", version.ref = "threetenabp" }
other_mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpAndroidChart" }
other_anychart = { module = "com.github.AnyChart:AnyChart-Android", version.ref = "anychart" }

androidx-palette-ktx = { group = "androidx.palette", name = "palette-ktx", version.ref = "paletteKtx" }
other_agentweb = { module = "io.github.justson:agentweb-core",version.ref = "angentWeb" }
other-tensorflow-lite = {module = "org.tensorflow:tensorflow-lite",version.ref = "tensorflowLite"}
other-aliyun-oss = {module = "com.aliyun.dpa:oss-android-sdk",version.ref = "aliyunOSS"}
other-aliyun-apm = {module = "com.aliyun.ams:alicloud-apm",version.ref = "aliyunEmas"}
other-aliyun-feedback = {module = "com.aliyun.ams:alicloud-android-feedback",version.ref = "aliyunFeedback"}
other-leakcanary = {module = "com.squareup.leakcanary:leakcanary-android",version.ref = "leakcanary"}
androidx-core-animation = { group = "androidx.core", name = "core-animation", version.ref = "coreAnimation" }


[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt-gradle = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
aliyun-emas = { id = "com.aliyun.emas.networkmonitor"}

